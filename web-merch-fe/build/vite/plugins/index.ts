/**
 * @name createVitePlugins
 * @description 封装plugins数组统一调用
 */
import uniPlugin from '@dcloudio/vite-plugin-uni';
import type { PluginOption } from 'vite';
import { AutoImportDeps } from './autoImport';
import { AutoRegistryComponents } from './component';
import { ConfigUnoCSSPlugin } from './unocss';
import { RequireTransform } from './requireTransform';

export default function createVitePlugins(isBuild: boolean) {
  const vitePlugins: (PluginOption | PluginOption[])[] = [
    // UnoCSS配置
    ConfigUnoCSSPlugin(),
    // 自动按需引入依赖
    AutoImportDeps(),
    // 自动按需引入组件(注意：需注册至 uni 之前，否则不会生效)
    AutoRegistryComponents(),
    // uni支持
    uniPlugin(),
    // require 2 import
    RequireTransform(),
  ];

  if (isBuild) {
    const buildPlugins: (PluginOption | PluginOption[])[] = [
      // 打包视图分析
      // VisualizerPlugin(),
    ];
    vitePlugins.push(...buildPlugins);
  }

  return vitePlugins;
}
