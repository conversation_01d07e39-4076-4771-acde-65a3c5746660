/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    CustomDatetimePicker: typeof import('./../src/components/custom-datetime-picker/index.vue')['default']
    CustomNomore: typeof import('./../src/components/page-paging/modules/custom-nomore.vue')['default']
    CustomRefresher: typeof import('./../src/components/page-paging/modules/custom-refresher.vue')['default']
    GivenUpload: typeof import('./../src/components/given-upload/index.vue')['default']
    PagePaging: typeof import('./../src/components/page-paging/index.vue')['default']
    RateModule: typeof import('./../src/components/rate-module/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
