import type { VNodeChild } from 'vue';

declare global {

  declare interface Window {
    // 高德地图安全密钥配置
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
  }

  declare type EnumMap = Record<string, any>;

  // vue
  declare type VueNode = VNodeChild | JSX.Element;

  declare type TimeoutHandle = ReturnType<typeof setTimeout>;
  declare type IntervalHandle = ReturnType<typeof setInterval>;

  interface ImportMetaEnv extends ViteEnv {
    __: unknown;
  }

  declare interface ViteEnv {
    VITE_APP_ENV: 'development' | 'production';
    VITE_API_PREFIX: string;
    VITE_API_URL: string;
    VITE_DROP_CONSOLE: boolean;
    VITE_SELF_WEBVIEW_URL: string;
  }

  declare function parseInt(s: string | number, radix?: number): number;

  declare function parseFloat(string: string | number): number;

  declare interface Uni {
    $u: any;
  }

  namespace JSX {
    interface IntrinsicElements {
      view: _View;
    }
  }
}
