import { resolve } from 'node:path';
import { defineConfig, loadEnv } from 'vite';
import type { UserConfig } from 'vite';
import createVitePlugins from './build/vite/plugins';

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }): UserConfig => {
  const isBuild = command === 'build';
  const env = loadEnv(mode, process.cwd());

  return {
    base: './',
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置别名
        '@': resolve(__dirname, './src'),
      },
    },
    // vite 相关配置
    server: {
      port: 8080,
      host: '0.0.0.0',
      open: false,
      proxy: {
        [env.VITE_API_PREFIX]: {
          target: env.VITE_API_URL,
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp(`^${env.VITE_API_PREFIX}`), ''),
        },
      },
    },
    // 设置scss的api类型为modern-compiler
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          // 消除一些不必要的警告
          silenceDeprecations: ['legacy-js-api'],
        },
      },
    },
    plugins: createVitePlugins(isBuild),
  };
});
