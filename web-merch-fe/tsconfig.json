{"compilerOptions": {"target": "es2018", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["@dcloudio/types", "@uni-helper/uni-app-types", "miniprogram-api-typings", "wot-design-uni/global", "z-paging/global.d.ts", "pinia-plugin-persist-uni"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "outDir": "dist", "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-app-types/volar-plugin"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "src/utils/secret/index.ts"], "exclude": ["dist", "node_modules", "uni_modules"]}