<template>
  <view class="h-200rpx w-400rpx" :class="customClass">
    <wd-upload
      :file-list="fileList" :limit="1"
      :auto-upload="false"
      :disabled="disabled"
      custom-class="w-full h-full px-12px"
      custom-evoke-class="w-full h-full"
      custom-preview-class="!w-full !h-full bg-black !m-0"
      :before-upload="onUpload"
      @remove="onRemove"
    >
      <view class="relative h-full">
        <!-- 占位图 -->
        <image
          class="h-full w-full rounded-md"
          :src="placeholder"
          mode="aspectFill"
        />
        <!-- 图片名称 -->
        <view class="absolute bottom-0 w-full rounded-b-md bg-#909399 p-12px py-8rpx text-center text-24rpx text-white">
          {{ fileName }}
        </view>
      </view>
    </wd-upload>
  </view>
</template>

<script lang="ts" setup>
import type { UploadBeforeUploadOption } from 'wot-design-uni/components/wd-upload/types';
import { pathToBase64 } from 'image-tools';
import NP from 'number-precision';
import { givenUploadProps } from './type';
import WCompress from '@/uni_modules/watasi-compress/js_sdk/index.js';

const props = defineProps(givenUploadProps);

const emit = defineEmits<{
  (e: 'update:fileData', value: string): void;
  (e: 'choose', value: string): void;
  (e: 'remove'): void;
}>();

const fileList = computed<any>({
  get: () => {
    if (!props.fileData)
      return [];

    // 图片链接
    if (/^(https?:)/.test(props.fileData)) {
      return [{ url: props.fileData }];
    }

    // 图片转换、压缩处理 loading状态
    if (props.fileData === '_loading') {
      return [{ status: 'loading' }];
    }

    // 图片base64
    const urlPrefix = 'data:image/png;base64,';
    return [{ url: urlPrefix + props.fileData, name: '.png', status: 'success' }]; // name用来占位, 勿删
  },
  set: (val: string) => emit('update:fileData', val),
});

/**
 * 选择图片
 */
async function onUpload({ files }: UploadBeforeUploadOption) {
  fileList.value = '_loading';

  // 图片base64
  let dataUrl: string = '';

  try {
    /** 执行压缩处理 */

    const blob = await compress(files[0].path, {
    });

    // #ifdef H5
    // blob to dataUrl
    dataUrl = await filetoDataURL(blob[0]);
    // #endif

    // #ifndef H5
    dataUrl = await pathToBase64(blob[0]);
    // #endif
  }
  catch (error) {
    /** 不压缩 */

    dataUrl = await pathToBase64(files[0].path);
  }

  const data = dataUrl.split(',')[1];
  fileList.value = data;
  // 触发事件 选择完成
  emit('choose', data);
  // 取消自动上传
  Promise.resolve(false);
}

/**
 * 移除图片
 */
function onRemove() {
  fileList.value = '';
  emit('remove');
}

async function compress(imgPath: string, options: any) {
  options = Object.assign(
    {
      quality: 0.6,
      maxSize: 200,
    },
    options,
  );
  const { width: imgW, height: imgH } = await getImageInfo(imgPath);
  let scale = 1;

  const imgBlobData = await _compress();

  return imgBlobData;

  async function _compress() {
    const imgWScale = imgW * scale;
    const imgHScale = imgH * scale;

    const blob = await WCompress([imgPath], {
      quality: options.quality,
      width: imgWScale,
      height: imgHScale,
    });

    if (scale <= 0.1) {
      return blob;
    }

    let size = 0;
    // #ifdef H5
    size = blob[0].size;
    // #endif
    // #ifndef H5
    const base64 = await pathToBase64(blob[0]);
    size = calSize(base64);
    // #endif
    if (size > options.maxSize * 1024) {
      scale = NP.minus(scale, 0.1);
      return _compress();
    }

    return blob;
  }
}

async function getImageInfo(imgPath: string): Promise<any> {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: imgPath,
      success: (res) => {
        resolve(res);
      },
      fail: (res) => {
        reject(res);
      },
    });
  });
}

/**
 * 将File（Blob）对象转变为一个dataURL字符串
 *
 * @param {Blob} file
 * @returns {Promise(string)} Promise含有一个dataURL字符串参数
 */
function filetoDataURL(file: Blob): Promise<string> {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onloadend = (e: any) => resolve(e.target.result as string);
    reader.readAsDataURL(file);
  });
};

// 获取base64图片大小，返回MB数字
function calSize(base64url: string): any {
  let str = base64url.split(',')[1];
  const equalIndex = str.indexOf('=');
  if (str.indexOf('=') > 0) {
    str = str.substring(0, equalIndex);
  }
  const strLength = str.length;
  const fileLength = strLength - (strLength / 8) * 2;
  // 返回单位为B的大小
  return (fileLength).toFixed(2);
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-upload__status-content{
    .wd-upload__progress-txt{
      display: none;
    }
  }
}
</style>
