export const givenUploadProps = {
  /**
   * 文件数据 (base64 | url)
   * 类型：string
   * 默认值：''
   */
  fileData: {
    type: String,
    default: '',
  },
  /**
   * 必选参数，文件名称
   * 类型：string
   * 默认值：''
   */
  fileName: {
    type: String,
    default: '',
  },
  /**
   * 必选参数，占位图
   * 类型：string
   * 默认值：''
   */
  placeholder: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  /**
   * 自定义根节点样式类
   */
  customClass: String,
};

export type GivenUploadProps = Partial<ExtractPropTypes<typeof givenUploadProps>> & { [key: string]: any };
