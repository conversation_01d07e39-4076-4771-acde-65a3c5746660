<!-- eslint-disable vue/no-mutating-props -->
<template>
  <view>
    <view class="rate-module">
      <template v-if="rateItem.rateType === 1">
        <template v-if="showRateTypeTitle">
          <wd-cell title="线下收单费率信息(%)" />
          <wd-cell border />
        </template>

        <!-- 按组遍历 -->
        <wd-row v-for="(groupList, groupIndex) in Object.values(groupBy(allScanRateFields, 'group_id')) as any" :key="groupIndex" :gutter="10">
          <template v-for="(groupItem, groupItemIndex) in groupList" :key="groupItemIndex">
            <template v-if="targetRateKeys.includes(groupItem.field)">
              <wd-col :span="12">
                <wd-input
                  v-model="rateItem.rateInfoDTO[groupItem.field]" :label="groupItem.name" type="digit" placeholder="请输入"
                  :prop="makeProp(groupItem.field)"
                  :rules="makeRules(groupItem.field)"
                  :disabled="readonly"
                />
              </wd-col>
              <wd-col v-if="groupItem.single === 1" :span="12">
                <view class="invisible">
                  <wd-input label="仅占位" />
                </view>
              </wd-col>
            </template>
          </template>
        </wd-row>
      </template>

      <template v-else-if="rateItem.rateType === 4">
        <wd-cell title="机构出款设置(%)" />
        <wd-cell border />

        <wd-row :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPubWdRate" label="分润提现税率(公)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPubWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPriWdRate" label="分润提现税率(私)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPriWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
          </wd-col>
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPubWdSingleFee" label="分润提现单笔(公)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPubWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPriWdSingleFee" label="分润提现单笔(私)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPriWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
          </wd-col>
        </wd-row>

        <wd-row :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPubWdRate" label="返现提现税率(公)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPubWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPriWdRate" label="返现提现税率(私)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPriWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
          </wd-col>
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPubWdSingleFee" label="返现提现单笔(公)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPubWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPriWdSingleFee" label="返现提现单笔(私)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPriWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
          </wd-col>
        </wd-row>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { FormItemRule } from 'wot-design-uni/components/wd-form/types';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  /*
   * 费率信息
   */
  rateItem: {
    type: Object as PropType<RateItem>,
    required: true as const,
  },
  /*
   * 是否只读
   */
  readonly: {
    type: Boolean,
    default: false,
  },
  /*
   * 表单prop前缀
   */
  formPropPrefix: {
    type: Array,
    default() {
      return [];
    },
  },
  /*
   * 费率校验规则
   */
  formRules: Object as PropType<RateFormRules>,
  /*
   * 是否显示D0单笔费用
   */
  showD0SingleFee: {
    type: Boolean,
    default: false,
  },
  /*
   * 是否显示费率类型标题
   */
  showRateTypeTitle: {
    type: Boolean,
    default: true,
  },
});

// 扫码相关的字段
const allScanRateFields: any = [
  { field: 'creditRate', name: '银联标准(贷)', group_id: 1, single: 1 },
  { field: 'debitRate', name: '银联标准(借)', group_id: 1 },
  { field: 'debitFeeMax', name: '银联标准封顶(借)', group_id: 1 },
  { field: 'nfcCreditRate', name: '银联云闪付(贷)', group_id: 2 },
  { field: 'nfcDebitRate', name: '银联云闪付(借)', group_id: 2 },
  { field: 'aliPayRate', name: '支付宝', group_id: 3 },
  { field: 'aliPayLargeRate', name: '支付宝大额', group_id: 3 },
  { field: 'wechatRate', name: '微信', group_id: 3 },
  { field: 'creditQrD0Rate', name: 'D0附加费率(贷)', group_id: 4 },
  { field: 'creditQrD0SingleFee', name: 'D0附加单笔(贷)', group_id: 4 },
  { field: 'debitQrD0Rate', name: 'D0附加费率(借)', group_id: 4 },
  { field: 'debitQrD0SingleFee', name: 'D0附加单笔(借)', group_id: 4 },
];

const targetRateKeys: any = ref([]);

watch(
  () => props.rateItem, // 要监听的属性
  (val: any) => {
    if (props.rateItem.rateType === 1 && val?.rateInfoDTO) {
      // setRateDefValue();
      const rateKeys = Object.keys(val.rateInfoDTO);
      targetRateKeys.value = allScanRateFields.filter((item: any) => rateKeys.includes(item.field)).map((item: any) => item.field);
    }
  },
  {
    immediate: true, // 立即执行
  },
);

const rateFromRules: RateFormRules = props.formRules
  || {
    rate: [
      { required: true, pattern: /^\d+(\.\d{1,4})?$/, message: '正数且小数点后最多四位' },
    ],
    amount: [
      { required: true, pattern: /^\d+(\.\d{1,2})?$/, message: '正数且小数点后最多两位' },
    ],
  };

const makeProp = function (prop: string) {
  return [...props.formPropPrefix, 'rateInfoDTO', prop].join('.');
};

const makeRules = function (prop: string) {
  if (prop.endsWith('Rate')) {
    return rateFromRules.rate;
  }
  else {
    return rateFromRules.amount;
  }
};

function onInputChangeWechatRate({ value }: { value: any }) {
  const rateKeys = ['nfcCreditRate', 'nfcDebitRate', 'wechatRate', 'aliPayRate'];
  rateKeys.forEach((key) => {
    // eslint-disable-next-line vue/no-mutating-props
    props.rateItem.rateInfoDTO[key] = value;
  });
}

function setRateDefValue() {
  onInputChangeWechatRate({ value: props.rateItem.rateInfoDTO.wechatRate });

  let rateKeys = ['creditQrD0Rate', 'debitQrD0Rate'];
  if (!props.showD0SingleFee) {
    rateKeys = [...rateKeys, 'creditQrD0SingleFee', 'debitQrD0SingleFee'];
  }
  rateKeys.forEach((key) => {
    // eslint-disable-next-line vue/no-mutating-props
    props.rateItem.rateInfoDTO[key] = 0;
  });
}

function groupBy<T, K extends keyof any>(collection: T[], key: K | ((item: T) => K)): { [key: string]: T[] } {
  return collection.reduce((result, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key as unknown as keyof T];
    const keyStr = String(groupKey);
    result[keyStr] = result[keyStr] || [];
    result[keyStr].push(item);
    return result;
  }, {} as { [key: string]: T[] });
}

export type RateItem = {
  rateType: number;
  rateInfoDTO: Record<string, any>;
};

export type RateFormRules = {
  rate: FormItemRule[];
  amount: FormItemRule[];
};
</script>

<style lang="scss" scoped>
:deep(.rate-module){
  .wd-input.is-cell{
    padding: 0;
    margin-bottom: 12px;

    .wd-input__label{
      margin-right: 10px;
      width: auto;
      flex: 1;

      // 隐藏必填*号
      &.is-required{
        padding-left: 0;

        &::after{
          display: none;
        }
      }
    }

    .wd-input__body{
      width: 66px;
      text-align: center;
      background-color: #f3f5f7;
      border-radius: 4px;
      flex: none;
      flex-shrink: 0;
    }
  }
}
</style>
