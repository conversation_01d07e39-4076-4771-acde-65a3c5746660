<!-- 基于z-paging封装个性化分页 -->
<template>
  <!-- 这边统一设置z-paging，在页面中使用时就不用重复写 -->
  <z-paging
    ref="paging"
    v-model="list"
    :default-page-size="defaultPageSize"
    auto-show-back-to-top refresher-threshold="160rpx"
    :use-virtual-list="useVirtualList" :use-inner-list="useInnerList"
    :cell-key-name="cellKeyName"
    :inner-list-style="innerListStyle" :preload-page="preloadPage"
    :cell-height-mode="cellHeightMode" :virtual-scroll-fps="virtualScrollFps"
    :loading-more-enabled="loadingMoreEnabled"
    :auto="auto"
    :refresher-only="refresherOnly"
    :fixed="fixed"
    @query="queryList"
    @on-refresh="refreshList"
  >
    <!-- 这里插入一个view到z-paging中，并且这个view会被z-paging标记为top固定在顶部 -->
    <template #top>
      <slot name="top" />
    </template>

    <!-- 这里插入一个view到z-paging中，并且这个view会被z-paging标记为bottom固定在顶部 -->
    <template #bottom>
      <slot name="bottom" />
    </template>

    <!-- 这个是插入虚拟列表/内置列表的cell -->
    <template #cell="{ item, index }">
      <slot name="cell" :item="item" :index="index" />
    </template>

    <!-- 这里通过slot统一自定义了下拉刷新view和没有更多数据view，页面那边就不用再写下面两行了 -->
    <!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->
    <template #refresher="{ refresherStatus }">
      <CustomRefresher :status="refresherStatus" />
    </template>
    <!-- 自定义没有更多数据view -->
    <template #loadingMoreNoMore>
      <CustomNomore />
    </template>

    <!-- 这里接收页面传进来的普通slot，如列表数据等 -->
    <slot />
  </z-paging>
</template>

<script setup>
import { ref, watch } from 'vue';
import CustomNomore from './modules/custom-nomore.vue';
import CustomRefresher from './modules/custom-refresher.vue';

const props = defineProps({
  // 用于接收父组件v-model所绑定的list的值
  value: {
    type: Array,
    default() {
      return [];
    },
  },
  // 是否使用虚拟列表，默认为否
  useVirtualList: {
    type: Boolean,
    default: false,
  },
  // 是否在z-paging内部循环渲染列表(内置列表)，默认为否。若use-virtual-list为true，则此项恒为true
  useInnerList: {
    type: Boolean,
    default: false,
  },
  // 内置列表cell的key名称，仅nvue有效，在nvue中开启use-inner-list时必须填此项
  cellKeyName: {
    type: String,
    default: '',
  },
  // innerList样式
  innerListStyle: {
    type: Object,
    default() {
      return {};
    },
  },
  // 预加载的列表可视范围(列表高度)页数，默认为7，即预加载当前页及上下各7页的cell。此数值越大，则虚拟列表中加载的dom越多，内存消耗越大(会维持在一个稳定值)，但增加预加载页面数量可缓解快速滚动短暂白屏问题
  preloadPage: {
    type: [Number, String],
    default: 7,
  },
  // 虚拟列表cell高度模式，默认为fixed，也就是每个cell高度完全相同，将以第一个cell高度为准进行计算。可选值【dynamic】，即代表高度是动态非固定的，【dynamic】性能低于【fixed】。
  cellHeightMode: {
    type: String,
    default: 'fixed',
  },
  // 虚拟列表scroll取样帧率，默认为60，过高可能出现卡顿等问题
  virtualScrollFps: {
    type: [Number, String],
    default: 60,
  },
  // 自定义pageSize(每页显示多少条)
  defaultPageSize: {
    type: [Number, String],
    default: 10,
  },
  // 是否启用加载更多数据(含滑动到底部加载更多数据和点击加载更多数据)
  loadingMoreEnabled: {
    type: Boolean,
    default: true,
  },
  auto: {
    type: Boolean,
    default: true,
  },
  refresherOnly: {
    type: Boolean,
    default: false,
  },
  // 是否是固定布局
  fixed: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['input', 'query', 'onRefresh', 'update:modelValue']);

const paging = ref(null);
const list = ref([]);

watch(() => props.modelValue, (newVal) => {
  list.value = newVal;
});

watch(() => list.value, (newVal) => {
  emits('update:modelValue', newVal);
});

// 监听z-paging的@onRefresh事件，通过emit传递给页面
const refreshList = () => {
  emits('onRefresh');
};

// 监听z-paging的@query事件，通过emit传递给页面
const queryList = (pageNo, pageSize) => {
  emits('query', pageNo, pageSize);
};

// 接收页面触发的reload方法，传给z-paging
const reload = (data) => {
  paging.value.reload(data);
};

// 接收页面触发的complete方法，传给z-paging
const complete = (data) => {
  paging.value.complete(data);
};

// 接收页面触发的completeByTotal方法，传给z-paging
const completeByTotal = (...data) => {
  paging.value.completeByTotal(...data);
};

defineExpose({ reload, complete, completeByTotal });
</script>
