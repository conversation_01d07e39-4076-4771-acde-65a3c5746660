<!-- z-paging自定义的下拉刷新view -->
<template>
  <view class="refresher-container">
    <!-- 这里的图片请换成自己项目的图片 -->
    <image class="refresher-image" mode="aspectFit" src="@/static/images/refresher_loading.gif" />
    <text class="refresher-text">
      {{ statusText }}
    </text>
  </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: Number,
    default() {
      return 0;
    },
  },
});

const statusText = computed(() => {
  const statusTextArr = ['哎呀，用点力继续下拉！', '拉疼我啦，松手刷新~~', '正在努力刷新中...', '刷新成功啦~'];
  return statusTextArr[props.status];
});
</script>

<style scoped>
 .refresher-container {
  /* #ifndef APP-NVUE */
  display: flex;
  justify-content: center;
  align-items: center;

  /* #endif */
  height: 150rpx;
  flex-direction: column;
  background-color: #fff;
 }

 .refresher-image {
  margin-top: 10rpx;
  width: 45px;
  height: 45px;
 }

 .refresher-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666;
 }
</style>
