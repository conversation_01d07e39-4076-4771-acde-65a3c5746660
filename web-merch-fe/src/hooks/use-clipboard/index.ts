/**
 * 剪切板
 */

interface SetClipboardDataOptions {
  data: string;
  showToast?: boolean;
}

export default function useClipboard() {
  const setClipboardData = ({ data, showToast = true }: SetClipboardDataOptions) => {
    return new Promise<string>((resolve, reject) => {
      uni.setClipboardData({
        data,
        showToast: false,
        success: ({ data }) => {
          if (showToast) {
            uni.showToast({
              title: '内容已复制',
              icon: 'none',
            });
          }
          resolve(data);
        },
        fail: error => reject(error),
      });
    });
  };
  const getClipboardData = () => {
    return new Promise<string>((resolve, reject) => {
      uni.getClipboardData({
        success: ({ data }) => resolve(data),
        fail: error => reject(error),
      });
    });
  };
  return {
    setClipboardData,
    getClipboardData,
  };
}
