interface ShareOptions {
  title?: string;
  path?: string;
  query?: string;
  imageUrl?: string;
}

/**
 * 小程序分享
 * @param {object} options
 * @example
 * const {onShareAppMessage, onShareTimeline} = useShare({title: '分享标题', path: 'pages/index/index', query: 'id=1', imageUrl: 'https://xxx.png'})
 * onShareAppMessage()
 * onShareTimeline()
 */
export default function useShare(options?: ShareOptions) {
  // #ifdef MP-WEIXIN
  const title = options?.title ?? '';
  const path = options?.path ?? '';
  const query = options?.query ?? '';
  const imageUrl = options?.imageUrl ?? '';

  const shareApp = (params: ShareOptions = {}) => {
    onShareAppMessage(() => {
      return {
        title,
        path: path ? `${path}${query ? `?${query}` : ''}` : '',
        imageUrl,
        ...params,
      };
    });
  };

  const shareTime = (params: ShareOptions = {}) => {
    onShareTimeline(() => {
      return {
        title,
        query: options?.query ?? '',
        imageUrl,
        ...params,
      };
    });
  };
  return {
    onShareAppMessage: shareApp,
    onShareTimeline: shareTime,
  };
  // #endif
}
