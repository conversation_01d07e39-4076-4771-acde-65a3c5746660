import * as crypto from 'sm-crypto';
import * as Base64 from 'js-base64';
import type { HttpRequestConfig } from 'luch-request';

/**
 * 加密模式 1 - C1C3C2，0 - C1C2C3，默认为1
 */
const CipherMode = 1;
/**
 * 拼接前缀 防碰撞
 */
const SplicingPrefix = '04';
/**
 * 客户端私钥
 */
const ClientPrivateKey = '00926edffb9cef32365fca96624abea5ea7ba456c2bacfdab893fe2c946cd30bb9';
/**
 * 平台公钥
 */
const ServePublicKey
  = '04d799f52b4216bb83c32997d2b041c577d8b01f6b59c7516856bcee6f0202b723acd0b51d4ac1a036620bdab748904636c5a71c993f79d7601b711bc04083447a';
/**
 * 设备信息
 */
const { platform } = uni.getSystemInfoSync();
const osType = platform === 'ios' ? 2 : platform === 'android' ? 1 : 1;

/**
 * 数据加密
 * @param {*} config 请求配置对象
 * @returns 请求配置对象
 */
export function encrypt(config: HttpRequestConfig) {
  try {
    const { method, params, data } = config;

    // 参数对象
    const reqData = data || params || '';
    console.log(`%c[ req ][${config.url}] >`, 'background-color: #f0f0f0; color: green;', reqData);
    // 加密串
    let encryptedStr = '';

    if (reqData) {
      // 转json
      const reqData2Json = JSON.stringify(reqData);
      // 转base64
      const reqJson2Base64 = Base64.encode(reqData2Json);
      // 组装加密串
      encryptedStr = SplicingPrefix + crypto.sm2.doEncrypt(reqJson2Base64, ServePublicKey, CipherMode);
    }

    // 私钥签名
    const signStr = encryptedStr + osType;
    const sign = crypto.sm2.doSignature(signStr, ClientPrivateKey, {
      der: true,
      hash: true,
    });

    // 请求参数体
    const httpBody = {
      reqData: encryptedStr,
      osType,
      sign,
      appBrandType: 1,
    };

    switch (method) {
      case 'POST':
        config.data = httpBody;
        break;
      case 'GET':
        config.params = httpBody;
        break;
      default:
        throw new Error('只支持post、get请求加密');
    }

    return config;
  }
  catch (error) {
    throw new Error(`[数据加密失败]===>>${error}`);
  }
}

/**
 * 数据解密
 * @param {*} encryptedStr 加密串
 * @returns 解密后的数据
 */
export function decrypt(encryptedStr: string) {
  try {
    if (!encryptedStr)
      return encryptedStr;

    // 去除前缀
    if (encryptedStr.startsWith(SplicingPrefix)) {
      encryptedStr = encryptedStr.substring(2);
    }

    // base64数据
    const resBase64 = crypto.sm2.doDecrypt(encryptedStr, ClientPrivateKey, CipherMode);
    // json数据
    const resJson = Base64.decode(resBase64);
    // 解密数据
    const resData = JSON.parse(resJson);

    return resData;
  }
  catch (error) {
    throw new Error(`[数据解密失败]===>>${error}`);
  }
}
