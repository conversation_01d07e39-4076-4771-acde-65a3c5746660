import type {
  Http<PERSON>rror,
  HttpRequestAbstract,
  HttpRequestConfig,
  HttpResponse,
} from 'luch-request';
import { Dialog, Loading } from '../modals';
import { decrypt, encrypt } from '../secret/index.js';
import { assembleMessage } from './status';
import { getToken } from '@/utils/auth';
import { HEADER_TOKEN_NAME } from '@/config/setting';
import { useUserStore } from '@/store';

function requestInterceptors(http: HttpRequestAbstract) {
  /**
   * 请求拦截
   * @param {object} http
   */
  http.interceptors.request.use(
    (config: HttpRequestConfig) => {
      /* 可使用async await 做异步操作 */

      // 是否需要loading
      const isLoading = config.custom?.loading === false;
      // 是否需要设置 token
      const isToken = config.custom?.auth === false;
      // 是否需要加密
      const isSecret = config.custom?.secret === false;

      if (!isLoading) {
        // 显示loading
        Loading.show('加载中...');
      }

      if (getToken() && !isToken && config.header) {
        // token设置
        config.header[HEADER_TOKEN_NAME] = getToken();
      }

      if (!isSecret) {
        // 加密
        config = Object.assign(config, encrypt(config));
      }

      return config;
    },
    (config: any) => // 可使用async await 做异步操作
      Promise.reject(config),
  );
}
function responseInterceptors(http: HttpRequestAbstract) {
  /**
   * 响应拦截
   * @param {object} http
   */
  http.interceptors.response.use(
    async (response: HttpResponse) => {
      /* 对响应成功做点什么 可使用async await 做异步操作 */

      // 关闭loading
      Loading.hide();
      // 响应结果
      const res = response.data;
      // 配置参数
      const config = response.config;
      // 自定义参数
      const custom = config?.custom;
      // 是否需要解密
      const isSecret = custom?.secret === false;
      if (!isSecret) {
        // 解密
        res.data = decrypt(res.data);
      }

      // 请求成功则返回结果
      if (res.code === '00000')
        return res;

      // 登录状态失效，重新登录
      if (['A0307', 'B0301'].includes(res.code)) {
        Dialog('登录失效, 请重新登录').then(() => {
          useUserStore().logout(false);
        });
        return Promise.reject(new Error('Login invalid'));
      }

      // 如果没有显式定义custom的toast参数为false的话，默认对报错进行弹出提示
      if (custom?.toast !== false)
        Dialog(res.message);

      // reject
      return Promise.reject(res);
    },
    (response: HttpError) => {
      // 关闭loading
      Loading.hide();
      if (response.statusCode) {
        // 请求已发出，但是不在2xx的范围
        Dialog(assembleMessage(response.statusCode), { title: '提示' });
        return Promise.reject(response.data);
      }
      const errMsg = response.errMsg || (response as any)?.message || '网络连接异常,请稍后再试!';
      Dialog(errMsg, { title: '提示' });
      return Promise.reject(response);
    },
  );
}

export { requestInterceptors, responseInterceptors };
