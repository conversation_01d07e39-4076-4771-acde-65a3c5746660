// 引入配置
import type { HttpRequestConfig, HttpResponse } from 'luch-request';
import Request from 'luch-request';
import { requestInterceptors, responseInterceptors } from './interceptors';
import type { IResponse } from './type';
import { API_PREFIX, API_URL } from '@/config/setting';

const http = new Request();

// 获取请求基本路径
function getBaseUrl() {
  let basuUrl: string = '';

  switch (uni.getSystemInfoSync().uniPlatform) {
    case 'web':
      // h5
      basuUrl = API_PREFIX;
      break;
      // 其他
    default:
      basuUrl = API_URL;
  }

  return basuUrl;
}

// 引入拦截器配置
export function setupRequest() {
  http.setConfig((defaultConfig: HttpRequestConfig) => {
    /* defaultConfig 为默认全局配置 */
    defaultConfig.baseURL = getBaseUrl();
    return defaultConfig;
  });
  requestInterceptors(http);
  responseInterceptors(http);
}

export function request<T = any>(config: HttpRequestConfig): Promise<T> {
  return new Promise((resolve, reject) => {
    http.request(config)
      .then((res: HttpResponse<IResponse<T>>) => {
        console.log(`%c[ res ][${config.url}] >`, 'background-color: #f0f0f0; color: blue;', res);
        const { data } = res;
        resolve(data as T);
      })
      .catch((err: unknown) => reject(err));
  });
}

export function get<T = any>(config: HttpRequestConfig): Promise<T> {
  return request({ ...config, method: 'GET' });
}

export function post<T = any>(config: HttpRequestConfig): Promise<T> {
  return request({ ...config, method: 'POST' });
}

export function upload<T = any>(config: HttpRequestConfig): Promise<T> {
  return request({ ...config, method: 'UPLOAD' });
}

export function download<T = any>(config: HttpRequestConfig): Promise<T> {
  return request({ ...config, method: 'DOWNLOAD' });
}

export default setupRequest;
