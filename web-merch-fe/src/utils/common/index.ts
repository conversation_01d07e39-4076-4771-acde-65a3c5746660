// 小程序更新检测
export function mpUpdate() {
  const updateManager = uni.getUpdateManager();
  updateManager.onCheckForUpdate((res) => {
    // 请求完新版本信息的回调
    console.log(res.hasUpdate);
  });
  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '检测到新版本，是否下载新版本并重启小程序？',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        }
      },
    });
  });
  updateManager.onUpdateFailed(() => {
    // 新的版本下载失败
    uni.showModal({
      title: '已经有新版本了哟~',
      content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
      showCancel: false,
    });
  });
}

/**
 * 构建带参数的URL
 * @param baseUrl 基础URL
 * @param params 参数对象，键值对表示要添加到URL的参数
 * @returns 返回带有参数的URL
 */
export function buildUrlWithParams(baseUrl: string, params: Record<string, string>) {
  // 将参数对象转换为查询字符串
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  // 检查基础URL是否已包含查询字符串，并选择适当的分隔符
  const separator = baseUrl.includes('?') ? '&' : '?';

  // 返回带有参数的URL
  return `${baseUrl}${separator}${queryString}`;
}

/**
 * 解码URL参数
 * @param params 参数对象，键值对表示要解码的参数
 * @returns 返回解码后的参数对象
 */
export function decodeUrlParams(params: Record<string, any>) {
  const result: Record<string, string> = {};
  for (const [key, value] of Object.entries(params)) {
    result[key] = decodeURIComponent(value);
  }
  return result;
}
/**
 * 深拷贝函数，用于将对象进行完整复制。
 * @param obj 要深拷贝的对象
 * @param cache 用于缓存已复制的对象，防止循环引用
 * @returns 深拷贝后的对象副本
 */
export function deepClone<T>(obj: T, cache: Map<any, any> = new Map()): T {
  // 如果对象为 null 或者不是对象类型，则直接返回该对象
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 处理特殊对象类型：日期、正则表达式、错误对象
  if (obj instanceof RegExp) {
    return new RegExp(obj.source, obj.flags) as any;
  }
  if (obj instanceof Error) {
    const errorCopy = new Error(obj.message) as any;
    errorCopy.stack = obj.stack;
    return errorCopy;
  }

  // 检查缓存中是否已存在该对象的复制
  if (cache.has(obj)) {
    return cache.get(obj);
  }

  // 根据原始对象的类型创建对应的空对象或数组
  const copy: any = Array.isArray(obj) ? [] : {};

  // 将当前对象添加到缓存中
  cache.set(obj, copy);

  // 递归地深拷贝对象的每个属性
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      copy[key] = deepClone(obj[key], cache);
    }
  }

  return copy as T;
}

/**
 * 剔除对象中的某些属性
 * @param obj
 * @param predicate
 * @returns
 */
export function omitBy<O extends Record<string, any>>(obj: O, predicate: (value: any, key: keyof O) => boolean): Partial<O> {
  const newObj = deepClone(obj);
  Object.keys(newObj).forEach(key => predicate(newObj[key], key) && delete newObj[key]); // 遍历对象的键，删除值为不满足predicate的字段
  return newObj;
}
