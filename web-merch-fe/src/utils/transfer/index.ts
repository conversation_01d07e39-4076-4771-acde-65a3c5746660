import { useTransferStore } from '@/store';
import { buildUrlWithParams } from '@/utils/common';
import type { TransferOptions } from '@/store/modules/transfer/types';

/**
 * 数据传递工具类
 * 提供便捷的页面间数据传递功能
 */
export class DataTransfer {
  private static _store: ReturnType<typeof useTransferStore> | null = null;

  private static get store() {
    if (!this._store) {
      this._store = useTransferStore();
    }
    return this._store;
  }

  /**
   * 存储数据并生成唯一key
   * @param data 要存储的数据
   * @param options 存储选项
   * @returns 生成的key
   */
  static setData(data: any, options: TransferOptions = {}): string {
    try {
      // 基本数据验证
      if (data === undefined || data === null) {
        throw new Error('数据不能为空');
      }

      // 验证数据是否可序列化
      JSON.stringify(data);

      const key = this.store.generateKey('page_data');
      this.store.setData(key, data, {
        expire: 2 * 60 * 60 * 1000, // 默认2分钟过期
        autoRemove: true,
        ...options,
      });

      return key;
    }
    catch (error) {
      console.error('[DataTransfer] 存储数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据
   * @param key 存储键
   * @param autoRemove 是否自动删除
   * @returns 存储的数据
   */
  static getData(key: string, autoRemove = true): any {
    try {
      return this.store.getData(key, autoRemove);
    }
    catch (error) {
      console.error('[DataTransfer] 获取数据失败:', error);
      return null;
    }
  }

  /**
   * 检查数据是否存在
   * @param key 存储键
   * @returns 是否存在
   */
  static hasData(key: string): boolean {
    return this.store.hasData(key);
  }

  /**
   * 删除数据
   * @param key 存储键
   * @returns 是否删除成功
   */
  static removeData(key: string): boolean {
    return this.store.removeData(key);
  }

  /**
   * 清理过期数据
   * @returns 清理的数据数量
   */
  static cleanup(): number {
    try {
      return this.store.cleanup();
    }
    catch (error) {
      console.error('[DataTransfer] 清理数据失败:', error);
      return 0;
    }
  }

  /**
   * 获取数据统计信息
   */
  static getStats() {
    return this.store.getStats();
  }

  /**
   * 清空所有数据
   */
  static clearAll(): void {
    this.store.clearAll();
  }
}

/**
 * 页面跳转助手
 */
export class NavigationHelper {
  /**
   * 带数据跳转到页面
   * @param url 目标页面URL
   * @param data 要传递的数据
   * @param urlParams 仍需要通过URL传递的简单参数
   * @param options 传递选项
   */
  static navigateToWithData(
    url: string,
    data?: any,
    urlParams: Record<string, string | number> = {},
    options: TransferOptions = {},
  ): void {
    try {
      if (!url || typeof url !== 'string') {
        throw new Error('无效的URL');
      }

      let finalUrl = url;
      let transferKey: string | undefined;

      // 处理复杂数据
      if (data) {
        transferKey = DataTransfer.setData(data, {
          expire: 2 * 60 * 1000, // 2分钟过期
          autoRemove: false, // 不自动删除，由目标页面控制
          ...options,
        });
      }

      // 处理URL参数
      const params: Record<string, any> = { ...urlParams };
      if (transferKey) {
        params.transferKey = transferKey;
      }

      // 构建最终URL
      if (Object.keys(params).length > 0) {
        finalUrl = buildUrlWithParams(url, params);
      }

      // 执行跳转
      uni.navigateTo({
        url: finalUrl,
        fail: (error) => {
          console.error('[NavigationHelper] 页面跳转失败:', error);
          // 如果跳转失败，清理已创建的传递数据
          if (transferKey) {
            DataTransfer.removeData(transferKey);
          }
        },
      });
    }
    catch (error) {
      console.error('[NavigationHelper] 跳转失败:', error);
      throw error;
    }
  }

  /**
   * 重定向到页面（带数据）
   * @param url 目标页面URL
   * @param data 要传递的数据
   * @param urlParams URL参数
   * @param options 传递选项
   */
  static redirectToWithData(
    url: string,
    data?: any,
    urlParams: Record<string, string | number> = {},
    options: TransferOptions = {},
  ): void {
    try {
      if (!url || typeof url !== 'string') {
        throw new Error('无效的URL');
      }

      let finalUrl = url;
      let transferKey: string | undefined;

      // 处理复杂数据
      if (data) {
        transferKey = DataTransfer.setData(data, {
          expire: 2 * 60 * 1000, // 2分钟过期
          autoRemove: false, // 不自动删除，由目标页面控制
          ...options,
        });
      }

      // 处理URL参数
      const params: Record<string, any> = { ...urlParams };
      if (transferKey) {
        params.transferKey = transferKey;
      }

      // 构建最终URL
      if (Object.keys(params).length > 0) {
        finalUrl = buildUrlWithParams(url, params);
      }

      // 执行重定向
      uni.redirectTo({
        url: finalUrl,
        fail: (error) => {
          console.error('[NavigationHelper] 页面重定向失败:', error);
          // 如果重定向失败，清理已创建的传递数据
          if (transferKey) {
            DataTransfer.removeData(transferKey);
          }
        },
      });
    }
    catch (error) {
      console.error('[NavigationHelper] 重定向失败:', error);
      throw error;
    }
  }

  /**
   * 在onLoad中获取传递的数据
   * @param query onLoad的query参数
   * @returns 包含URL参数和传递数据的对象
   */
  static getTransferredData(query: Record<string, any> = {}) {
    try {
      const { transferKey, ...urlParams } = query;

      let transferredData = null;
      if (transferKey && typeof transferKey === 'string') {
        transferredData = DataTransfer.getData(transferKey);
      }

      return {
        urlParams,
        transferredData,
        hasTransferredData: transferredData !== null,
      };
    }
    catch (error) {
      console.error('[NavigationHelper] 获取传递数据失败:', error);
      return {
        urlParams: query,
        transferredData: null,
        hasTransferredData: false,
        error: String(error),
      };
    }
  }
}

/**
 * 自动清理管理器
 * 定期清理过期数据
 */
export class AutoCleanup {
  private static timer: number | null = null;
  private static isRunning = false;

  /**
   * 启动自动清理
   * @param interval 清理间隔（毫秒），默认5分钟
   */
  static start(interval = 5 * 60 * 1000): void {
    if (this.isRunning) {
      console.warn('[AutoCleanup] 自动清理已在运行中');
      return;
    }

    this.isRunning = true;
    this.timer = setInterval(() => {
      const cleaned = DataTransfer.cleanup();
      if (cleaned > 0) {
        console.log(`[AutoCleanup] 清理了 ${cleaned} 项过期数据`);
      }
    }, interval);

    console.log(`[AutoCleanup] 自动清理已启动，间隔: ${interval}ms`);
  }

  /**
   * 停止自动清理
   */
  static stop(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.isRunning = false;
    console.log('[AutoCleanup] 自动清理已停止');
  }

  /**
   * 手动触发清理
   * @returns 清理的数据数量
   */
  static manualCleanup(): number {
    return DataTransfer.cleanup();
  }

  /**
   * 获取运行状态
   */
  static getStatus() {
    return {
      isRunning: this.isRunning,
      stats: DataTransfer.getStats(),
    };
  }
}

// 导出默认实例
export default {
  DataTransfer,
  NavigationHelper,
  AutoCleanup,
};
