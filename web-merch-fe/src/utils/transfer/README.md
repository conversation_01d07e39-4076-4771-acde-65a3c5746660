# Pinia 数据传递模块

一个轻量级的页面间数据传递解决方案，基于 Pinia 状态管理。

## 核心功能

- ✅ 页面间数据传递
- ✅ 自动过期清理
- ✅ 数据统计
- ✅ 自动清理管理
- ✅ 简洁的 API

## 快速开始

### 基本使用

```typescript
import { DataTransfer, NavigationHelper } from '@/utils/transfer';

// 存储数据
const key = DataTransfer.setData({ name: '张三', age: 25 });

// 获取数据
const data = DataTransfer.getData(key);

// 带数据跳转页面
NavigationHelper.navigateToWithData('/pages/detail', { id: 123 });
```

### 在目标页面获取数据

```typescript
// pages/detail.vue
export default {
  onLoad(query) {
    const { transferredData } = NavigationHelper.getTransferredData(query);
    if (transferredData) {
      console.log('接收到的数据:', transferredData);
    }
  }
};
```

### 自动清理

```typescript
import { AutoCleanup } from '@/utils/transfer';

// 启动自动清理（5分钟间隔）
AutoCleanup.start();

// 手动清理
AutoCleanup.manualCleanup();

// 停止自动清理
AutoCleanup.stop();
```

## API 文档

### DataTransfer

#### setData(data, options?)
存储数据并返回唯一key

- `data`: 要存储的数据
- `options.expire`: 过期时间（毫秒），默认5分钟
- `options.autoRemove`: 是否自动删除，默认true

#### getData(key, autoRemove?)
获取存储的数据

- `key`: 存储键
- `autoRemove`: 是否自动删除，默认true

#### hasData(key)
检查数据是否存在且有效

#### removeData(key)
删除指定数据

#### cleanup()
清理过期数据

#### getStats()
获取数据统计信息

#### clearAll()
清空所有数据

### NavigationHelper

#### navigateToWithData(url, data?, urlParams?, options?)
带数据跳转到页面

#### redirectToWithData(url, data?, urlParams?, options?)
带数据重定向到页面

#### getTransferredData(query)
在目标页面获取传递的数据

### AutoCleanup

#### start(interval?)
启动自动清理，默认5分钟间隔

#### stop()
停止自动清理

#### manualCleanup()
手动触发清理

#### getStatus()
获取运行状态和统计信息

## 注意事项

1. 数据会自动过期，默认5分钟
2. 建议启用自动清理避免内存泄漏
3. 传递的数据必须可序列化
4. 适用于中小型数据传递，大文件请使用其他方案

## 更新日志

### v2.0.0 (精简版)
- 移除了复杂的压缩功能
- 移除了性能监控
- 移除了高级配置选项
- 保留核心的数据传递功能
- 简化了 API 接口
- 提升了性能和稳定性
