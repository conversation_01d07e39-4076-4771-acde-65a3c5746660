import type { Emitter } from 'mitt';
import mitt from 'mitt';
import type { CommonEmitValue } from './type';

type Events = {
  // 选择类目
  'picker-category': CommonEmitValue;
  // 选择结算卡
  'picker-bank-card': CommonEmitValue;
  // 选择地区
  'picker-area': CommonEmitValue;
  // 选择总行
  'picker-bank-type': CommonEmitValue;
  // 选择支行
  'picker-bank-sub': CommonEmitValue;
  // 选择支行
  'route-parames': CommonEmitValue;
  // 选择地址
  'picker-address': CommonEmitValue;
  // 同步政策 选择下级机构
  'picker-sub-org': CommonEmitValue;
  // 选择费率政策
  'picker-rate-policy': CommonEmitValue;
  // 选择返现政策
  'picker-cashback-policy': CommonEmitValue;
  // 选择目标代理商
  'picker-target-agent': CommonEmitValue;
};

const emitter: Emitter<Events> = mitt<Events>();

export { emitter };
