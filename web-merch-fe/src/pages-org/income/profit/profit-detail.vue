<template>
  <view class="p-30rpx">
    <!-- 骨架屏 -->
    <wd-skeleton theme="paragraph" :loading="loading" />

    <!-- 交易详情 -->
    <view v-show="!loading" class="rounded-lg bg-primary py-30rpx">
      <view class="flex flex-col items-center py-30rpx text-28rpx">
        <text class="text-40rpx text-#b51e1e font-500">
          {{ detail.transAmount }}
        </text>
        <text class="mt-10rpx">
          交易金额(元)
        </text>
      </view>

      <view class="text-28rpx">
        <wd-row :gutter="10">
          <wd-col :span="8" custom-class="col-flex text-#999">
            <text>结算金额(元)</text>
            <text>我的收益(元)</text>
            <text>手续费类型</text>
            <text>交易费率(%)</text>
            <text>交易方式</text>
            <text>交易类型</text>
            <text>交易卡类型</text>
            <text>交易成功时间</text>
            <text>机具SN号</text>
            <text>商户所属团队</text>
            <text>商户直属代理</text>
            <text>交易参考号</text>
          </wd-col>
          <wd-col :span="16" custom-class="col-flex items-end">
            <text>{{ detail.settleAmount || '--' }}</text>
            <text>{{ detail.profitAmount || '--' }}</text>
            <text>{{ detail.feeTypeDesc || '--' }}</text>
            <text>{{ detail.transRate || '--' }}</text>
            <text>{{ payMethodMap[detail.payMethod] || '--' }}</text>
            <text>{{ detail.transCodeName || '--' }}</text>
            <text>{{ payCardTypeMap[detail.payCardType] || '--' }}</text>
            <text>{{ detail.finishTime || '--' }}</text>
            <view>
              {{ detail.terminalSn || '--' }}
              <i v-if="detail.terminalSn" class="copy-icon" @click="copyContent(detail.terminalSn)" />
            </view>
            <view>
              {{ detail.directTeamName || '--' }}
            </view>
            <text>{{ detail.agentNo || '--' }}</text>
            <view>
              {{ detail.rrn || '--' }}
              <i v-if="detail.rrn" class="copy-icon" @click="copyContent(detail.rrn)" />
            </view>
          </wd-col>
        </wd-row>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { IncomeApi } from '@/api-org/income';
import { useClipboard } from '@/hooks';

const loading = ref(true);

const detail = ref<any>({});

const payMethodMap: Record<number, string> = {
  1: '云闪付支付',
  2: '微信支付',
  3: '支付宝支付',
  4: 'EPOS支付',
  5: 'POS刷卡',
};

const payCardTypeMap: Record<number, string> = {
  1: '借记卡',
  2: '贷记卡',
  3: '准贷记卡',
  4: '预付费卡',
};

onLoad((query) => {
  const { id } = query || {};
  getDetail({ id });
});

async function getDetail(where: any) {
  const data = await IncomeApi.queryProfitDetailInfById(where);
  detail.value = data || {};

  loading.value = false;
}

function copyContent(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>

<style lang="scss" scoped>
 :deep(.col-flex){
  @apply flex flex-col;

  text,view{
    @apply mb-8px break-all;
  }
}

.copy-icon{
  @apply i-mdi-content-copy  size-30rpx text-#b51e1e ml-2px mb-6px shrink-0;
}
</style>
