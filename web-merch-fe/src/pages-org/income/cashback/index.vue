<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <view v-if="[3, 5].includes(loginUser.orgType as number)">
          <wd-tabs v-model="where.selType" @change="onChangeWhereSelType">
            <wd-tab title="全部" name="3" />
            <wd-tab title="自营" name="1" />
            <wd-tab title="团队" name="2" />
          </wd-tabs>
        </view>
        <view class="p-20rpx">
          <view class="flex flex-col items-start rounded-md bg-#4D80F0 p-20rpx text-28rpx text-white">
            <!-- 查询条件 -->
            <view class="flex items-center">
              <custom-datetime-picker
                v-model="whereDate"
                type="date"
                :default-value="whereDate"
                custom-value-class="!text-white"
                @confirm="handleConfirmDate"
              />

              <wd-drop-menu custom-class="custom-drop-menu">
                <wd-drop-menu-item
                  v-model="where.cashbackType" :options="cashbackTypeOptions"
                  :[cashbackTypeTitleProp]="'返现类型'"
                  @change="onChangeWhereCashbackType"
                />
              </wd-drop-menu>
            </view>

            <!-- 数据信息 -->
            <view class="mt-10rpx w-full flex flex items-center">
              <view class="flex grow flex-col">
                <text>返现金额</text>
                <text>{{ cashbackProfitInfo.cashbackTotalAmt }}</text>
              </view>
              <view class="w-20rpx shrink-0" />
              <view class="flex grow flex-col items-end">
                <text>奖励笔数</text>
                <text>{{ cashbackProfitInfo.totalCount }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <view v-for="(item, key) in datasource" :key="key" class="bg-white px-20rpx" @click="toDetail(item)">
        <view class="border border-#e8e8e8 border-b-solid py-20rpx">
          <view>
            <view class="mb-5px font-bold">
              {{ item.merchantName }}
            </view>

            <view class="cell">
              <text class="cell_label">
                商户号:
              </text>
              <view class="cell_value flex items-center">
                <text>
                  {{ item.merchantNo }}
                </text>
                <i
                  v-if="item.merchantNo"
                  class="copy-icon"
                  @click.stop="copyData(item.merchantNo)"
                />
              </view>
            </view>
            <view class="cell">
              <text class="cell_label">
                通道商户号:
              </text>
              <view class="cell_value flex items-center">
                <text>
                  {{ item.chnMerchNo }}
                </text>
                <i
                  v-if="item.chnMerchNo"
                  class="copy-icon"
                  @click.stop="copyData(item.chnMerchNo)"
                />
              </view>
            </view>
            <view v-if="item.teamFlag === 1" class="cell">
              <text class="cell_label">
                所属团队:
              </text>
              <text class="cell_value">
                {{ item.directTeamName }}
              </text>
            </view>
            <view class="flex items-center">
              <view class="cell grow">
                <text class="cell_label">
                  活动金额:
                </text>
                <text class="cell_value">
                  {{ item.transAmount }}
                </text>
              </view>

              <view class="ml-20rpx flex shrink-0 items-center">
                <view class="flex grow flex-col text-right">
                  <text class="text-red-6">
                    +{{ item.cashbackAmount }}
                  </text>
                  <text class="text-24rpx">
                    {{ item.orderType === 1 ? '设备激活' : item.orderType === 2 ? `通讯费${item.cycleDay}期` : '--' }}
                  </text>
                </view>

                <view class="ml-20rpx shrink-0">
                  <wd-icon name="arrow-right" size="40rpx" />
                </view>
              </view>
            </view>

            <view class="cell">
              <text class="cell_label">
                设备SN:
              </text>
              <view class="cell_value flex items-center">
                <text>
                  {{ item.terminalSn }}
                </text>
                <i
                  v-if="item.terminalSn"
                  class="i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e"
                  @click.stop="copyData(item.terminalSn)"
                />
              </view>
            </view>
            <view class="cell">
              <text class="text-#999">
                {{ item.createTime }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <!-- 弹框挂载点 -->
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { buildUrlWithParams, deepClone } from '@/utils';
import { IncomeApi } from '@/api-org/income';
import { useClipboard } from '@/hooks';
import { useUserStore } from '@/store';

const loginUser = computed(() => useUserStore().info);

const pagingRef = ref();

const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive<any>({
  selType: '3', // 查询类型 1-自营  2-团队 3-全部
  selTimeType: 1, // 查询类型 必填 0-根据月份查询 1-自定义时间查询 2-快捷查询
  cashbackType: -1, // 返现类型 1-服务费 2-流量费
  queryBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  queryEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});

const cashbackTypeTitleProp = computed(() => {
  const prop = where.cashbackType === -1 ? 'title' : '';
  return prop;
});

const datasource = ref<any>([]);

const cashbackProfitInfo = ref<any>({
  cashbackTotalAmt: '0.00', // 返现总金额（单位：元）
  totalCount: '0', // 返现总笔数
});

const cashbackTypeOptions = [
  { label: '全部', value: -1 },
  { label: '服务费', value: 1 },
  { label: '流量费', value: 2 },
];

onLoad((query) => {
  if (query?.transDayTime) {
    const formatTime = dayjs(query.transDayTime).format('YYYY-MM-DD');
    where.queryBeginTime = formatTime;
    where.queryEndTime = formatTime;
    whereDate.value = [dayjs(formatTime).valueOf(), dayjs(formatTime).valueOf()];
  }
});

function handleConfirmDate({ value }: { value: string[] }) {
  [where.queryBeginTime, where.queryEndTime] = value;
  pagingRef.value.reload();
}

function onChangeWhereSelType() {
  pagingRef.value.reload();
}

function onChangeWhereCashbackType() {
  pagingRef.value.reload();
}

function toDetail(item: any) {
  const url = buildUrlWithParams('/pages-org/income/cashback/cashback-detail', {
    id: item.id,
  });
  uni.navigateTo({ url });
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.cashbackType = where.cashbackType === -1 ? null : where.cashbackType;

  IncomeApi.queryCashbackDetailListInfoByPage({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      const { sumResponse, pageResult } = res || {};

      cashbackProfitInfo.value = Object.assign(cashbackProfitInfo.value, sumResponse);

      pagingRef.value.completeByTotal(pageResult?.rows, pageResult?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.custom-drop-menu) {
  color: #fff;

  .wd-drop-menu__list{
    background-color: transparent;

    .wd-drop-menu__item{
      height: auto;
      line-height: normal;

      .wd-drop-menu__item-title::after{
          display:  none;
      }
    }
  }
}

.cell {
  display: flex;
  align-items: center;
  margin-bottom: 4px;

  &_label {
    color: #444;
  }

  &_value {
    margin-left: 10rpx;
  }
}

:deep(.custom-tag-class){
  margin-right: 10px;
  font-size: 24rpx !important;
}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
