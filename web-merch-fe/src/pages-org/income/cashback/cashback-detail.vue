<template>
  <view class="p-20rpx">
    <!-- 骨架屏 -->
    <wd-skeleton theme="paragraph" :loading="loading" />

    <!-- 交易详情 -->
    <view v-show="!loading" class="rounded-lg bg-primary py-30rpx">
      <view class="flex flex-col items-center py-30rpx text-28rpx">
        <text class="text-40rpx text-#b51e1e font-500">
          {{ detail.orderType === 1 ? '设备激活' : detail.orderType === 2 ? `通讯费${detail.cycleDay}期` : '--' }}
        </text>
        <text class="mt-10rpx">
          {{ detail.payAmount }}
        </text>
      </view>

      <view class="text-28rpx">
        <wd-row :gutter="10">
          <wd-col :span="8" custom-class="col-flex text-#999">
            <text>返现金额(元)</text>
            <text>交易时间</text>
            <text>返现时间</text>
            <text>商户号</text>
            <text>通道商户号</text>
            <text>机具SN号</text>
            <text>商户所属团队</text>
            <text>商户直属代理</text>
            <text>订单号</text>
          </wd-col>
          <wd-col :span="16" custom-class="col-flex items-end">
            <text>{{ detail.cashbackAmount || '--' }}</text>
            <text>{{ detail.payTime || '--' }}</text>
            <text>{{ detail.cashbackTime || '--' }}</text>
            <text>{{ detail.merchantNo || '--' }}</text>
            <text>{{ detail.chnMerchNo || '--' }}</text>
            <view>
              {{ detail.terminalSn || '--' }}
              <i v-if="detail.terminalSn" class="copy-icon" @click="copyContent(detail.terminalSn)" />
            </view>
            <view>
              {{ detail.directTeamName || '--' }}
            </view>
            <view>
              {{ detail.agentNo || '--' }}
            </view>
            <view>
              {{ detail.orderNo || '--' }}
              <i v-if="detail.orderNo" class="copy-icon" @click="copyContent(detail.orderNo)" />
            </view>
          </wd-col>
        </wd-row>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { IncomeApi } from '@/api-org/income';
import { useClipboard } from '@/hooks';

const loading = ref(true);

const detail = ref<any>({});

onLoad((query) => {
  const { id } = query || {};
  getDetail({ id });
});

async function getDetail(where: any) {
  const data = await IncomeApi.queryCashbackDetailInfById(where);
  detail.value = data || {};

  loading.value = false;
}

function copyContent(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>

<style lang="scss" scoped>
 :deep(.col-flex){
  @apply flex flex-col;

  text,view{
    @apply mb-8px break-all;
  }
}

.copy-icon{
  @apply i-mdi-content-copy  size-30rpx text-#b51e1e ml-2px mb-6px shrink-0;
}
</style>
