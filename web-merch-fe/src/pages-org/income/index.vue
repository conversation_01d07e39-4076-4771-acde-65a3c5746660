<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <view>
          <wd-tabs v-model="currentTab" @change="onChangeTab">
            <wd-tab title="交易分润" :name="0" />
            <wd-tab title="返现" :name="1" />
          </wd-tabs>
        </view>
        <view v-if="[3, 5].includes(loginUser.orgType as number)" class="mt-5px">
          <wd-tabs v-model="where.selType" @change="onChangeWhereSelType">
            <wd-tab title="全部" name="3" />
            <wd-tab title="自营" name="1" />
            <wd-tab title="团队" name="2" />
          </wd-tabs>
        </view>

        <view class="p-20rpx">
          <view class="flex flex-col items-start rounded-md bg-#4D80F0 p-20rpx text-28rpx text-white">
            <!-- 查询条件 -->
            <view class="flex items-center">
              <custom-datetime-picker
                v-model="whereDate"
                type="date"
                :default-value="whereDate"
                custom-value-class="!text-white"
                @confirm="handleConfirmDate"
              />

              <wd-drop-menu custom-class="custom-drop-menu">
                <wd-drop-menu-item
                  v-if="currentTab === 0"
                  v-model="where.payMethod" :options="payMethodOptions"
                  :[payMethodTitleProp]="'交易方式'"
                  @change="onChangeWherePayMethod"
                />
                <wd-drop-menu-item
                  v-else-if="currentTab === 1"
                  v-model="where.cashbackType" :options="cashbackTypeOptions"
                  :[cashbackTypeTitleProp]="'返现类型'"
                  @change="onChangeWhereCashbackType"
                />
              </wd-drop-menu>
            </view>

            <!-- 数据信息 -->
            <view class="mt-10rpx w-full flex flex items-center">
              <!-- 交易分润 -->
              <template v-if="currentTab === 0">
                <view class="flex grow flex-col">
                  <text>交易总金额</text>
                  <text>{{ transProfitInfo.totalTransAmt }}</text>
                </view>
                <view class="w-20rpx shrink-0" />
                <view class="flex grow flex-col items-end">
                  <text>分润金额</text>
                  <text>{{ transProfitInfo.totalProfitAmt }}</text>
                </view>
              </template>

              <!-- 返现收益 -->
              <template v-else-if="currentTab === 1">
                <view class="flex grow flex-col">
                  <text>返现金额</text>
                  <text>{{ cashbackProfitInfo.cashbackTotalAmt }}</text>
                </view>
                <view class="w-20rpx shrink-0" />
                <view class="flex grow flex-col items-end">
                  <text>奖励笔数</text>
                  <text>{{ cashbackProfitInfo.totalCount }}</text>
                </view>
              </template>
            </view>
          </view>
        </view>
      </template>

      <view v-for="(item, key) in datasource" :key="key" class="bg-white" @click="toDetail(item)">
        <view class="mb-20rpx w-full flex items-center border border-#e8e8e8 border-b-solid p-20rpx">
          <!-- 交易分润 -->
          <template v-if="currentTab === 0">
            <view class="flex grow items-center">
              <view class="w-50% flex flex-col">
                <text>{{ item.transDayTime }}</text>
                <text>交易总额: {{ item.totalTransAmt }}</text>
              </view>

              <view class="w-50% flex flex-col items-end">
                <text>分润金额</text>
                <text>{{ item.totalProfitAmt }}</text>
              </view>
            </view>
          </template>

          <!-- 返现 -->
          <template v-else-if="currentTab === 1">
            <view class="flex grow items-center">
              <view class="w-50% flex flex-col">
                <text>{{ item.transDayTime }}</text>
                <text>共{{ item.totalCount }}笔返现</text>
              </view>

              <view class="w-50% flex flex-col items-end">
                <text>返现金额</text>
                <text>{{ item.cashbackTotalAmt }}</text>
              </view>
            </view>
          </template>

          <!-- 箭头 -->
          <view class="ml-20rpx shrink-0">
            <wd-icon name="arrow-right" size="40rpx" />
          </view>
        </view>
      </view>
    </page-paging>

    <!-- 弹框挂载点 -->
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { buildUrlWithParams, deepClone } from '@/utils';
import { IncomeApi } from '@/api-org/income';
import { useUserStore } from '@/store';

const loginUser = computed(() => useUserStore().info);

const currentTab = ref(0);

const payMethodOptions = [
  { label: '全部', value: -1 },
  { label: '银联云闪付', value: 1 },
  { label: '微信支付', value: 2 },
  { label: '支付宝支付', value: 3 },
  { label: 'Epos支付', value: 4 },
  { label: 'POS刷卡', value: 5 },
];

const cashbackTypeOptions = [
  { label: '全部', value: -1 },
  { label: '服务费', value: 1 },
  { label: '流量费', value: 2 },
];

const pagingRef = ref();

const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive<any>({
  selType: '3', // 查询类型 1-自营  2-团队 3-全部
  selTimeType: 1, // 查询类型 必填 0-根据月份查询 1-自定义时间查询 2-快捷查询
  payMethod: -1, // 交易方式 1-银联云闪付 2-微信支付 3-支付宝支付 4-EPOS支付 5-POS刷卡
  cashbackType: -1, // 返现类型 1-服务费 2-流量费
  queryBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  queryEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});
const payMethodTitleProp = computed(() => {
  const prop = where.payMethod === -1 ? 'title' : '';
  return prop;
});
const cashbackTypeTitleProp = computed(() => {
  const prop = where.cashbackType === -1 ? 'title' : '';
  return prop;
});

const datasource = ref<any>([]);

const transProfitInfo = ref<any>({
  totalTransAmt: '0.00', // 交易总金额（单位：元）
  totalProfitAmt: '0.00', // 分润总金额（单位：元）

});

const cashbackProfitInfo = ref<any>({
  cashbackTotalAmt: '0.00', // 返现总金额（单位：元）
  totalCount: '0', // 返现总笔数
});

function onChangeTab() {
  pagingRef.value.reload();
}

function onChangeWhereSelType() {
  pagingRef.value.reload();
}

function handleConfirmDate({ value }: { value: string[] }) {
  [where.queryBeginTime, where.queryEndTime] = value;
  pagingRef.value.reload();
}

function onChangeWherePayMethod() {
  pagingRef.value.reload();
}

function onChangeWhereCashbackType() {
  pagingRef.value.reload();
}

function toDetail(item: any) {
  let url = '';

  switch (currentTab.value) {
    case 0:
      url = buildUrlWithParams('/pages-org/income/profit/index', {
        transDayTime: item.transDayTime,
      });
      break;
    case 1:
      url = buildUrlWithParams('/pages-org/income/cashback/index', {
        transDayTime: item.transDayTime,
      });
      break;
  }

  uni.navigateTo({ url });
}

function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.payMethod = where.payMethod === -1 ? null : where.payMethod;
  formatWhere.cashbackType = where.cashbackType === -1 ? null : where.cashbackType;

  switch (currentTab.value) {
    case 0:
      IncomeApi.queryProfitSumInfoByPage({ ...formatWhere, pageNo, pageSize })
        .then((res) => {
          const { profitSumResponse, pageResult } = res || {};
          transProfitInfo.value = Object.assign(transProfitInfo.value, profitSumResponse);

          pagingRef.value.completeByTotal(pageResult?.rows, pageResult?.totalRows);
        })
        .catch(() => {
          pagingRef.value.completeByTotal(false);
        });
      break;
    case 1:
      IncomeApi.queryCashbackSumInfoByPage({ ...formatWhere, pageNo, pageSize })
        .then((res) => {
          const { sumResponse, pageResult } = res || {};
          cashbackProfitInfo.value = Object.assign(cashbackProfitInfo.value, sumResponse);

          pagingRef.value.completeByTotal(pageResult?.rows, pageResult?.totalRows);
        })
        .catch(() => {
          pagingRef.value.completeByTotal(false);
        });
      break;
  }
}
</script>

<style lang="scss" scoped>
:deep(.custom-drop-menu) {
  color: #fff;

  .wd-drop-menu__list{
    background-color: transparent;

    .wd-drop-menu__item{
      height: auto;
      line-height: normal;

      .wd-drop-menu__item-title::after{
          display:  none;
      }
    }
  }
}
</style>
