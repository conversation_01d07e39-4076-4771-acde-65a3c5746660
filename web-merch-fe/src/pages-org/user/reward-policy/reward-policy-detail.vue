<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="my-20rpx">
      <wd-cell-group border>
        <wd-cell title="政策类型" :value="cashbackTypeMap[detail.cashbackType]" />
        <wd-cell title="开通政策" :value="detail.policyName" />
      </wd-cell-group>
    </view>

    <wd-cell-group v-for="(item, key) in ruleList" :key="key" border custom-class="custom-rule-group">
      <wd-cell :title="`${item.cashbackType === 0 ? '激活' : '达标'}奖励`" center custom-class="rule-cell">
        <wd-input v-model="item.cashbackAmount" type="text" use-suffix-slot readonly placeholder=" ">
          <template #suffix>
            <text>元</text>
          </template>
        </wd-input>
      </wd-cell>
      <wd-cell
        :label="`条件: 绑机后${item.timeCycleStart}-${item.timeCycleEnd}天, 累计交易满${item.tradeVolume}元`"
        custom-class="rule-desc"
      />
    </wd-cell-group>
  </view>
</template>

<script lang="ts" setup>
import { CashbackPolicyApi } from '@/api-org/cashback-policy';
import { decodeUrlParams } from '@/utils';

const detail = ref<any>({});

const where = reactive<any>({
  orgNo: '',
  policyNo: '',
  cashbackType: '',
});

const ruleList = ref<any>([]);

const cashbackTypeMap: EnumMap = {
  0: '激活返现政策',
  1: '达标返现政策',
};

onLoad((query: any) => {
  query = decodeUrlParams(query);
  Object.assign(where, query);
  queryRuleList();
});

async function queryRuleList() {
  const data = await CashbackPolicyApi.findListByPolicyNo({
    ...where,
  });

  detail.value = data[0] || {};
  ruleList.value = data || [];
}
</script>

<style lang="scss" scoped>
:deep(.custom-rule-group){
  .wd-cell__wrapper{
     justify-content: flex-start;
    }

  .rule-cell{
   .wd-cell__left{
    flex: none;
    flex-shrink: 0;
    max-width: 35%;
   }

   .wd-cell__right{
    flex: none;
   }

   .wd-input__inner{
    height: 30px;
   }
  }

  .rule-desc{
    .wd-cell__label{
      margin-top: 0;
    }

    .wd-cell__right{
      flex: none;
    }
  }
}
</style>
