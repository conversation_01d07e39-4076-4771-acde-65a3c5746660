<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx rounded-lg bg-white"
        >
          <view class="p-20rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="政策名称" :value="item.policyName" />
              <wd-cell title="政策编号" :value="item.policyNo" />
              <wd-cell title="政策类型" :value="cashbackTypeMap[item.cashbackType]" />
              <wd-cell title="创建时间" :value="item.createTime" />
            </wd-cell-group>
          </view>

          <view class="flex justify-end border-1px border-#f4f4f4 border-t-solid p-20rpx">
            <view @click="handleDetail(item)">
              <wd-tag type="primary" plain custom-class="!text-24rpx !px-20rpx !py-8rpx">
                政策详情
              </wd-tag>
            </view>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { CashbackPolicyApi } from '@/api-org/cashback-policy';
import { buildUrlWithParams } from '@/utils';

// 分页ref
const pagingRef = ref();

const cashbackTypeMap: EnumMap = {
  0: '激活返现政策',
  1: '达标返现政策',
};

const datasource = ref<any[]>([]);

function queryDataSource(pageNo: number, pageSize: number) {
  CashbackPolicyApi.findSelfPage({ pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

function handleDetail(item: any) {
  const url = buildUrlWithParams('/pages-org/user/reward-policy/reward-policy-detail', {
    policyNo: item.policyNo,
    cashbackType: item.cashbackType,
    orgNo: item.orgNo,
  });
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
