<template>
  <view class="h-full flex flex-col overflow-hidden bg-primary">
    <view class="shrink-0 pb-10px">
      <wd-tabs v-model="currentTab" @change="onChangePolicyType">
        <wd-tab title="结算政策" />
        <wd-tab title="返现政策" />
      </wd-tabs>
    </view>

    <view class="grow overflow-y-scroll pb-30rpx">
      <!-- 费率政策 -->
      <template v-if="currentTab === 0">
        <wd-tabs v-model="currentChannelTab" slidable="always" custom-class="custom-rate-tabs">
          <template v-for="(item, key) in rateMapDTOList" :key="key">
            <wd-tab :title="item.channelName">
              <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                <RateModule
                  :rate-item="rateItem"
                  :readonly="true"
                />
              </template>
            </wd-tab>
          </template>
        </wd-tabs>
      </template>

      <!-- 返现规则 -->
      <template v-if="currentTab === 1">
        <wd-tabs v-model="activeChannelTabKey" custom-class="custom-rate-tabs" slidable="always">
          <wd-tab v-for="(channel, cIndex) in policyConfigByChannelGroup || []" :key="cIndex" :title="channel.channelName" :name="String(cIndex)" :lazy="false">
            <wd-tabs v-model="activeTerminalSourceTabKey" custom-class="custom-rate-tabs">
              <wd-tab v-for="(tab, tabIndex) in channel.channelData || []" :key="tabIndex" :title="tab.label" :name="String(tab.key)" :lazy="false">
                <view class="gap-primary" />
                <wd-cell-group title="服务费返现规则" border />
                <template v-if="tab.serviceFeeData?.length">
                  <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                    <wd-cell-group v-if="item.show" border custom-class="custom-rule-group">
                      <view class="gap-primary" />
                      <wd-cell>
                        <template #title>
                          <wd-text :text="item.policyName" type="warning" />
                        </template>
                      </wd-cell>
                      <wd-cell title="返现金额" :value="`${item.cashbackAmt}元`" />
                    </wd-cell-group>
                  </template>
                </template>
                <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />

                <view class="gap-primary" />
                <wd-cell-group title="通讯费返现规则" border />
                <template v-if="tab.simFeeData?.some((s:any) => s.data.length)">
                  <view v-for="(period, idx) in tab.simFeeData" :key="idx">
                    <template v-if="period.show">
                      <view class="gap-primary" />
                      <wd-cell :title="period.name" />
                      <template v-for="(item, key) in period.data || []" :key="key">
                        <wd-cell-group v-if="item.show" border custom-class="custom-rule-group">
                          <view class="h-6px bg-primary" />
                          <wd-cell>
                            <template #title>
                              <wd-text :text="item.policyName" type="warning" />
                            </template>
                          </wd-cell>
                          <wd-cell title="返现金额" :value="`${item.cashbackAmt}元`" />
                        </wd-cell-group>
                      </template>
                    </template>
                  </view>
                </template>
                <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />
              </wd-tab>
            </wd-tabs>
          </wd-tab>
        </wd-tabs>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';
import { deepClone } from '@/utils';

const simFeePolicyPeriodGroupDef: any = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
  },
];

const policyConfigByTerminalSourceDef: any = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: [],
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: [],
  },
];

interface IPolicyConfigByChannelGroupItem {
  channelCode: string;
  channelName: string;
  channelData: any[];
}

const currentTab = ref(0);

const currentChannelTab = ref(0);

// 费率政策
const rateMapDTOList = ref<any>([]);

// 活动政策
const activeChannelTabKey = ref('0');
const activeTerminalSourceTabKey = ref('1');
const policyConfigByChannelGroup = ref<any>([]);

let channels: any = [];

onLoad(async () => {
  await getChannelList();
  getRatePolicy();
});

function onChangePolicyType({ index }: { index: number }) {
  if (index === 0) {
    getRatePolicy();
  }
  else {
    geCashbackPolicy();
  }
}

async function getRatePolicy() {
  const detailData = await RatePolicyApi.detailPolicy({});
  const { rateMap } = detailData || {};

  const setedChannel = Object.keys(rateMap);

  const rateList = Object.values(rateMap || {}).flat();
  rateList.forEach((item: any) => {
    item.rateInfoDTO = item.rateInfoDTO || {};
    item.isSame = 1;
  });

  rateMapDTOList.value = [];

  setedChannel.forEach((channelCode) => {
    const channel = channels.find((item: any) => item.channelCode === channelCode);
    if (channel) {
      rateMapDTOList.value.push({
        channelCode,
        channelName: channel.channelName,
        rateDTOList: rateMap[channelCode],
      });
    }
  });
}

async function geCashbackPolicy() {
  const data = await RatePolicyApi.selfCashbackPolicyDetail();
  const { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};

  const serviceFeePolicyDTOListMap = serviceFeePolicyDTOList || [];
  serviceFeePolicyDTOListMap.forEach((i: any) => {
    i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
  });

  const simFeePolicyMap = simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key] || [];
        period.data.forEach((item: any) => {
          item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
        });
        period.checkedList = [];
      }
    });
  });

  // 找出所有通道
  let allChannelCodes: any = [];
  try {
    const channelCodes = new Set(
      [...serviceFeePolicyDTOListMap, ...Object.values(simFeePolicyMap || {})]
        .flat(Infinity)
        .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
        .filter(code => code?.trim())
        .map(code => code.trim()),
    );
    allChannelCodes = [...channelCodes];
  }
  catch (error) {
    console.log(error);
  }

  const policyConfigByChannelGroupMap: IPolicyConfigByChannelGroupItem[] = [];
  allChannelCodes.forEach((item: any) => {
    const channelItem = channels.find((channel: any) => channel.channelCode === item);
    policyConfigByChannelGroupMap.push({
      channelCode: item,
      channelName: channelItem?.channelName,
      channelData: deepClone(policyConfigByTerminalSourceDef),
    });
  });

  policyConfigByChannelGroupMap.forEach((channel: IPolicyConfigByChannelGroupItem) => {
    channel.channelData.forEach((tab) => {
      tab.serviceFeeData = serviceFeePolicyDTOListMap.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

      const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
        const periodData = period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);
        return {
          ...period,
          data: periodData,
          show: periodData.some((item: any) => item.show),
        };
      });
      tab.simFeeData = simFeeData;
    });
  });

  policyConfigByChannelGroup.value = policyConfigByChannelGroupMap;

  activeChannelTabKey.value = '0';
  activeTerminalSourceTabKey.value = '1';
}

async function getChannelList() {
  const data = await CommonApi.getChannelList({});
  channels = data || [];
}
</script>

<style lang="scss" scoped>
:deep(.custom-rule-group){
  background-color: transparent;

  .wd-cell-group__body{
    background-color: transparent;
  }
}

:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
