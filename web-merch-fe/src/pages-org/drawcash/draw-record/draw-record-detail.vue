<template>
  <view class="p-30rpx">
    <view class="rounded-lg bg-primary py-30rpx">
      <view class="text-28rpx">
        <wd-row :gutter="10">
          <wd-col :span="8" custom-class="col-flex text-#999">
            <text>提现类型</text>
            <text>账户姓名</text>
            <text>到账银行</text>
            <text>到账卡号</text>
            <text>账户类型</text>
            <text>提现金额</text>
            <text>到账金额</text>
            <text>提现时间</text>
            <text>提现状态</text>
            <text v-if="detail.receivedStatus === '3'">
              提现状态说明
            </text>
          </wd-col>
          <wd-col :span="16" custom-class="col-flex items-end">
            <text>{{ walletTypeMap[detail.walletType] || '--' }}</text>
            <text>{{ detail.bankAccountName || '--' }}</text>
            <text>{{ detail.bankName || '--' }}</text>
            <text>{{ detail.bankAccountNoMask || '--' }}</text>
            <text>{{ bankAccountTypeMap[detail.bankAccountType] || '--' }}</text>
            <text>{{ detail.withdrawAmount || '--' }}</text>
            <text>{{ detail.receivedAmount || '--' }}</text>
            <text>{{ detail.createTime || '--' }}</text>
            <text>{{ receivedStatusMap[detail.receivedStatus] || '--' }}</text>
            <text v-if="detail.receivedStatus === '3'">
              {{ detail.resDesc || '--' }}
            </text>
          </wd-col>
        </wd-row>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { decodeUrlParams } from '@/utils';

const detail = ref<any>({});

const receivedStatusMap: EnumMap = {
  0: '发起提现',
  1: '处理中',
  2: '已到账',
  3: '到账失败',
};

const walletTypeMap: EnumMap = {
  600: '交易分润',
  300: '活动分润',
  800: '奖励分润',
};

const bankAccountTypeMap: EnumMap = {
  G: '对公',
  S: '对私',
};

onLoad((query: any) => {
  query = decodeUrlParams(query);
  detail.value = Object.assign({}, query);
});
</script>

<style lang="scss" scoped>
 :deep(.col-flex){
  @apply flex flex-col;

  text,view{
    @apply mb-20px break-all;
  }
}
</style>
