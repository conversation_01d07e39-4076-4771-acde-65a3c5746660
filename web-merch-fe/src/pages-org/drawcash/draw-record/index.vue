<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <template #top>
        <view class="w-50%">
          <wd-drop-menu>
            <wd-drop-menu-item
              v-model="where.receivedStatus" :options="receivedStatusOptions"
              :[receivedStatusTitleProp]="'提现状态'"
              @change="onChangeWhereStatus"
            />
            <wd-drop-menu-item
              v-model="where.businessType" :options="businessTypeOptions"
              :[businessTypeTitleProp]="'业务类型'"
              @change="onChangeWhereType"
            />
          </wd-drop-menu>
        </view>

        <view class="bg-primary p-20rpx">
          <view class="flex flex-col items-start rounded-md bg-#4D80F0 p-20rpx text-28rpx text-white">
            <custom-datetime-picker
              v-model="whereDate"
              type="date"
              :default-value="whereDate"
              custom-value-class="!text-white"
              @confirm="handleConfirmDate"
            />

            <view class="mt-12px w-full">
              <view class="flex flex-col items-start">
                <text>提现金额(元)</text>
                <view class="mt-6px">
                  <text class="font-bold">
                    {{ withdrawInfo.amountTotal }}
                  </text>
                  <text class="ml-6px">
                    共{{ withdrawInfo.countTotal }}笔
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>

      <view class="overflow-hidden bg-primary">
        <view v-for="(item, key) in datasource" :key="key" class="bg-white pt-20rpx" @click="toDetail(item)">
          <view class="w-full flex items-start">
            <view class="ml-20rpx mt-10rpx shrink-0">
              <i class="i-mdi-storefront size-60rpx shrink-0 text-#800000" />
            </view>

            <view class="ml-10px flex grow flex-col items-start">
              <view class="w-full flex pr-20rpx">
                <view class="flex grow flex-col">
                  <text>
                    {{ item.bankName }}
                    {{ item.bankAccountNoMask ? `(尾号${item.bankAccountNoMask.slice(-4)})` : '' }}
                  </text>
                  <text class="mt-5px">
                    {{ walletTypeMap[item.walletType] || '--' }}
                  </text>
                </view>
                <view class="w-33% flex shrink-0 flex-col items-end">
                  <text class="break-all font-bold">
                    {{ item.withdrawAmount }}
                  </text>
                  <text class="mt-5px text-28rpx" :class="receivedStatusMapColor[item.receivedStatus]">
                    {{ receivedStatusMap[item.receivedStatus] || '--' }}
                  </text>
                </view>
              </view>
              <text class="mt-5px text-#999">
                {{ item.createTime }}
              </text>

              <view class="mt-20rpx h-1px w-full bg-#edf0f3" />
            </view>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { buildUrlWithParams, deepClone } from '@/utils';
import { DrawcashApi } from '@/api-org/drawcash';

const pagingRef = ref();

const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive<any>({
  businessType: -1, // 业务类型 600 交易分润、300 活动分润、800 奖励分润
  receivedStatus: -1, // 到账状态 0 待出款、1 出款中、2 成功、3 失败
  searchBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  searchEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});

const receivedStatusTitleProp = computed(() => {
  const prop = where.receivedStatus === -1 ? 'title' : '';
  return prop;
});
const businessTypeTitleProp = computed(() => {
  const prop = where.businessType === -1 ? 'title' : '';
  return prop;
});

const receivedStatusMap: EnumMap = {
  0: '发起提现',
  1: '处理中',
  2: '已到账',
  3: '到账失败',
};

const receivedStatusOptions = [
  { label: '全部', value: -1 },
  ...Object.entries(receivedStatusMap).map(([key, value]) => ({
    label: value,
    value: Number(key),
  })),
];

const receivedStatusMapColor: EnumMap = {
  0: 'text-orange',
  1: 'text-blue',
  2: 'text-green',
  3: 'text-red',
};

const walletTypeMap: EnumMap = {
  600: '交易分润',
  300: '活动分润',
  800: '奖励分润',
};

const businessTypeOptions = [
  { label: '全部', value: -1 },
  ...Object.entries(walletTypeMap).map(([key, value]) => ({
    label: value,
    value: Number(key),
  })),
];

const datasource = ref<any[]>([]);

const withdrawInfo = ref<any>({
  countTotal: 0, // 总笔数
  amountTotal: 0, // 提现总金额
});

onLoad((query: any) => {
  if (query.walletType) {
    where.businessType = Number(query.walletType);
  }
});

function toDetail(item: any) {
  const url = buildUrlWithParams('/pages-org/drawcash/draw-record/draw-record-detail', {
    ...item,
  });
  uni.navigateTo({ url });
}

async function queryWithdrawInfo(params = {}) {
  const data = await DrawcashApi.withdrawListTotal({
    ...params,
  });
  withdrawInfo.value = Object.assign({}, data);
}

function handleConfirmDate({ value }: { value: string[] }) {
  [where.searchBeginTime, where.searchEndTime] = value;
  pagingRef.value.reload();
}

function onChangeWhereStatus() {
  pagingRef.value.reload();
}

function onChangeWhereType() {
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.receivedStatus = where.receivedStatus === -1 ? null : where.receivedStatus;
  formatWhere.businessType = where.businessType === -1 ? null : where.businessType;

  DrawcashApi.withdrawListPage({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });

  pageNo === 1 && queryWithdrawInfo(formatWhere);
}
</script>
