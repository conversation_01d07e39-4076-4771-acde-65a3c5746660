<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text class="font-medium">
              {{ item.walletTypeName }}
            </text>
          </view>

          <view class="p-30rpx">
            <view class="flex flex-col items-center">
              <text>可提现余额</text>
              <text class="mt-4px text-32rpx">
                {{ item.profitBalance }}
              </text>
            </view>
          </view>

          <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
            <view class="grow py-20rpx" @click="handleFindRecord(item)">
              提现记录
            </view>
            <view class="w-1px bg-#edf0f3" />
            <view class="grow py-20rpx text-#4d80f0" @click="handleInitiate(item)">
              发起提现
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { TransApi } from '@/api-org/trans';

const pagingRef = ref();

const datasource = ref<any[]>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function queryDataSource() {
  TransApi.profitRewardBalance()
    .then((data) => {
      // 组装数据
      const assembleData = [
        {
          walletType: '600',
          walletTypeName: '交易分润钱包',
          profitBalance: data.profitBalance || '0.00',
        },
        {
          walletType: '300',
          walletTypeName: '活动分润钱包',
          profitBalance: data.rewardBalance || '0.00',
        },
      ];
      pagingRef.value.complete(assembleData);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

function handleInitiate(item: any) {
  uni.navigateTo({
    url: `/pages-org/drawcash/initiate/index?walletType=${item.walletType}`,
  });
}

function handleFindRecord(item: any) {
  uni.navigateTo({
    url: `/pages-org/drawcash/draw-record/index?walletType=${item.walletType}`,
  });
}
</script>
