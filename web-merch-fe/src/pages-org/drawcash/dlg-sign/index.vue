<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <wd-cell-group title="基本信息" border custom-class="mt-10px">
      <wd-cell title="姓名" :value="form.name" />
      <wd-cell title="身份证" :value="form.idCardNoMask" />
      <wd-cell title="手机号" :value="form.mobileMask" />
    </wd-cell-group>

    <wd-cell-group title="图片信息" custom-class="mt-10px">
      <view class="pb-12px">
        <view class="flex flex-wrap justify-center">
          <template v-for="(item, key) in fileList" :key="key">
            <view class="basis-1/2">
              <GivenUpload
                v-model:file-data="item.fileData"
                :file-name="item.fileName"
                :placeholder="item.placeholder"
                disabled
                custom-class="w-full"
              />
            </view>
          </template>
        </view>
      </view>
    </wd-cell-group>

    <view class="px-12px py-40rpx">
      <wd-button type="primary" size="large" block @click="save">
        签约
      </wd-button>
    </view>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni';
import { DrawcashApi } from '@/api-org/drawcash';
import type { GivenUploadProps } from '@/components/given-upload/type';

const toast = useToast();

const form = ref<any>({});

// 文件列表
const fileList = ref <GivenUploadProps[] > (
  [
    {
      fileName: '人像面',
      placeholder: require('@/static/images/card_face.png'),
      fileData: '',
      fileType: 1,
    },
    {
      fileName: '国徽面',
      placeholder: require('@/static/images/card_back.png'),
      fileData: '',
      fileType: 2,
    },
  ],
);

onLoad(() => {
  queryOrgAuthInfo();
});

async function save() {
  const parsms = {
    name: form.value.name, // 姓名
    idCardNo: form.value.idCardNo, // 身份证
    mobile: form.value.mobile, // 手机号
  };

  await DrawcashApi.signDlg(parsms);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

async function queryOrgAuthInfo() {
  const data = await DrawcashApi.queryOrgAuthInfo();
  form.value = Object.assign({}, data);

  const fileListMap = form.value.imageList || [];
  if (fileListMap.length) {
    fileList.value.forEach((item) => {
      fileListMap.forEach((item2: any) => {
        if (item2?.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    },
    );
  }
}
</script>
