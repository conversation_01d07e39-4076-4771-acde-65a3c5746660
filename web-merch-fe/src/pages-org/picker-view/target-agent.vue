<template>
  <view>
    <page-paging ref="pagingRef" v-model="dataSource" :default-page-size="30" @query="queryDataSource">
      <!-- 头部固定 -->
      <template #top>
        <view class="h-10px bg-primary" />
        <wd-search placeholder="请输入代理商名称" placeholder-left hide-cancel @search="onSearch" />
      </template>

      <!-- 列表 -->
      <wd-cell-group border>
        <wd-cell v-for="(item, key) in dataSource" :key="key" clickable title-width="100%" @click="onSelect(item)">
          <template #title>
            <view class="text-center font-medium">
              {{ item.agentName }}
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </page-paging>
  </view>
</template>

<script setup lang="ts">
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { emitter } from '@/utils/emitter';

const dataSource = ref<any[]>([]);

const pagingRef = ref();

const where = reactive({
  agentName: '', // 代理商名称
});

onUnmounted(() => {
  emitter.off('picker-target-agent');
});

function onSelect(item: any) {
  emitter.emit('picker-target-agent', item);
  uni.navigateBack();
}

function onSearch({ value } = { value: '' }) {
  where.agentName = value;
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  TerminalManageApi.findAgentByParentNo({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>
