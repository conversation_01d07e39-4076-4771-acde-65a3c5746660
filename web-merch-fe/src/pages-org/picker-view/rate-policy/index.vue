<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx rounded-lg bg-white"
          @click="onSelect(item)"
        >
          <view class="p-20rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="政策名称" :value="item.policyDesc" />
              <wd-cell title="政策编号" :value="item.policyNo" />
              <wd-cell title="创建时间" :value="item.createTime" />
            </wd-cell-group>
          </view>

          <view class="flex justify-center pb-20rpx">
            <wd-button>
              选择
            </wd-button>
          </view>
        </view>
      </view>
    </page-paging>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';
import { emitter } from '@/utils/emitter';

// 分页ref
const pagingRef = ref();

// 数据源
const datasource = ref<any[]>([]);

// 渠道列表
const channelList = ref<any>([]);

onUnmounted(() => {
  emitter.off('picker-rate-policy');
});

function onSelect(item: any) {
  emitter.emit('picker-rate-policy', item);
  uni.navigateBack();
}

async function getChannelList() {
  const data = await CommonApi.getChannelList({});
  channelList.value = data || [];
  return Promise.resolve();
}

async function queryDataSource(pageNo: number, pageSize: number) {
  if (!channelList.value.length) {
    await getChannelList();
  }

  RatePolicyApi.findPolicyPage({
    policyType: 1,
    pageNo,
    pageSize,
  })
    .then((res) => {
      const rows = res?.rows || [];
      rows.forEach((item: any) => {
        const rateDTOList = item.rateDTOList || [];
        const channelCodes = rateDTOList.map((c: any) => c.channelCode) || [];
        const allChannel = Array.from(new Set(channelCodes));

        const rateMapDTOList: any = [];
        allChannel.forEach((channel: any) => {
          const channelItem = channelList.value.find((c: any) => c.channelCode === channel);
          const sameChannelData = rateDTOList.filter((r: any) => r.channelCode === channel);

          rateMapDTOList.push({
            channelCode: channel,
            channelName: channelItem?.channelName,
            rateDTOList: sameChannelData,
          });
        });

        item.rateMapDTOList = rateMapDTOList;
      });

      pagingRef.value.completeByTotal(rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
