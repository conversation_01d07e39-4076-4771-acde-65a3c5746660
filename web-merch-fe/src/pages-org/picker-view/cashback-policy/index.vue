<template>
  <view>
    <page-paging ref="pagingRef" v-model="dataSource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 列表 -->
      <wd-cell-group border>
        <wd-cell v-for="(item, key) in dataSource" :key="key" clickable title-width="100%" @click="onSelect(item)">
          <template #title>
            <view class="font-medium">
              {{ item.templateName }}
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </page-paging>
  </view>
</template>

<script setup lang="ts">
import { RatePolicyApi } from '@/api-org/rate-policy';
import { emitter } from '@/utils/emitter';

const dataSource = ref<any[]>([]);

const pagingRef = ref();

onUnmounted(() => {
  emitter.off('picker-cashback-policy');
});

function onSelect(item: any) {
  emitter.emit('picker-cashback-policy', item);
  uni.navigateBack();
}

function queryDataSource() {
  RatePolicyApi.querySelfCashbackTemplate()
    .then((data) => {
      pagingRef.value.complete(data);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}
</script>
