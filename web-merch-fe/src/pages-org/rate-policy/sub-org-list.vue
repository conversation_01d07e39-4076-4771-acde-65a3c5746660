<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <wd-checkbox-group v-model="checkedSubOrgs" custom-class="!bg-transparent">
          <view
            v-for="(item, key) in datasource" :key="key"
            class="mb-20rpx rounded-lg bg-white p-20rpx"
          >
            <view class="mb-4px flex items-center justify-between p-2px">
              <text class="text-14px text-#666">
                {{ userTypeMap[item.userType] }}
              </text>
              <wd-checkbox :model-value="item.userNo" custom-class="!mb-0" :disabled="item.disabled" />
            </view>
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="用户名称" :value="item.userName" />
              <wd-cell title="用户编号" :value="item.userNo" />
              <wd-cell title="政策名称" :value="item.policyName" />
              <wd-cell title="是否相同政策">
                <text> {{ item.markPolicySame === 1 ? '是' : '否' }}</text>
              </wd-cell>
            </wd-cell-group>
          </view>
        </wd-checkbox-group>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="bg-white px-40rpx pb-40rpx pt-20rpx">
          <view class="flex items-center">
            <view class="grow">
              <wd-checkbox v-model="isAllChecked">
                全选
              </wd-checkbox>
            </view>
            <wd-button @click="onConfirm">
              确认({{ checkedSubOrgs.length }})
            </wd-button>
          </view>
        </view>
      </template>
    </page-paging>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { emitter } from '@/utils/emitter';

const toast = useToast();

const pagingRef = ref();

const datasource = ref<any[]>([]);

const where = reactive({
  id: '',
});

const checkedSubOrgs = ref<any[]>([]);

const userTypeMap: Record<string, string> = {
  1: '大区',
  2: '运营中心',
  3: '代理商',
};

// 全选/反选
const isAllChecked = computed({
  get() {
    return checkedSubOrgs.value.length && checkedSubOrgs.value.length === datasource.value.length;
  },
  set(val) {
    // 全选
    if (val) {
      checkedSubOrgs.value = datasource.value.map((item: any) => item.userNo);
    }
    // 排除后全不选
    else {
      checkedSubOrgs.value = [];
      datasource.value.forEach((item: any) => {
        if (item.disabled) {
          checkedSubOrgs.value.push(item.userNo);
        }
      });
    }
  },
});

onLoad((query) => {
  where.id = query?.id;
});

onUnmounted(() => {
  emitter.off('picker-sub-org');
});

function onConfirm() {
  if (checkedSubOrgs.value.length === 0) {
    toast.warning('请选择机构');
    return;
  }

  const subOrgList = datasource.value.filter((item: any) => checkedSubOrgs.value.includes(item.userNo));
  emitter.emit('picker-sub-org', subOrgList);

  uni.navigateBack();
}

function queryDataSource() {
  RatePolicyApi.getSubOrgList(where)
    .then((data) => {
      pagingRef.value.complete(data);

      // 初始化选中
      data.forEach((item: any) => {
        if (item.markPolicySame === 1) {
          checkedSubOrgs.value.push(item.userNo);
          item.disabled = true;
        }
      });
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
