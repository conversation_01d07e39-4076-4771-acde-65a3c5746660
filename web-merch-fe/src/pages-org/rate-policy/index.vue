<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <view>
          <wd-tabs v-model="currentTab" @change="onChangeTab">
            <wd-tab title="机构政策" :name="0" />
            <wd-tab title="活动返现模板" :name="1" />
            <wd-tab v-if="[3, 5].includes(loginUser.orgType as number)" title="商户政策" :name="2" />
          </wd-tabs>
        </view>
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx rounded-lg bg-white"
        >
          <template v-if="currentTab === 0">
            <view class="p-20rpx">
              <wd-cell-group custom-class="cell-group">
                <wd-cell title="政策名称" :value="item.policyDesc" />
                <wd-cell title="政策编号" :value="item.policyNo" />
                <wd-cell title="创建时间" :value="item.createTime" />
              </wd-cell-group>
            </view>

            <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
              <view class="grow py-20rpx" @click="handleSyncPolicy(item)">
                同步政策
              </view>
              <view class="border-1px border-#edf0f3 border-r-solid" />
              <view class="grow py-20rpx" @click="handleEditPolicy(item)">
                编辑政策
              </view>
            </view>
          </template>

          <template v-if="currentTab === 1">
            <view class="p-20rpx">
              <wd-cell-group custom-class="cell-group">
                <wd-cell title="返现模板名称" :value="item.activityCashbackPolicyName" />
                <wd-cell title="创建时间" :value="item.createTime" />
              </wd-cell-group>
            </view>
            <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
              <view class="grow py-20rpx" @click="handleEditCashbackPolicy(item)">
                编辑政策
              </view>
            </view>
          </template>

          <template v-if="currentTab === 2">
            <view class="p-20rpx">
              <wd-cell-group custom-class="cell-group">
                <wd-cell title="通道名称" :value="foramtChanelCode2Name(item.channelCode)" />
                <wd-cell title="政策名称" :value="item.policyDesc" />
                <wd-cell title="政策编号" :value="item.policyNo" />
                <wd-cell title="创建时间" :value="item.createTime" />
              </wd-cell-group>
            </view>
            <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
              <view class="flex grow items-center justify-center py-20rpx text-[#1890ff]" @click="handleAddPromotionCode(item)">
                <view class="mr-6rpx">
                  生成推广码
                </view>
                <wd-icon name="qrcode" size="32rpx" />
              </view>
              <view class="border-1px border-#edf0f3 border-r-solid" />
              <view class="grow py-20rpx" @click="handleEditPolicy(item)">
                编辑政策
              </view>
            </view>
          </template>
        </view>
      </view>

      <!-- 固定底部 -->
      <template #bottom>
        <view class="bg-white p-40rpx pt-20rpx">
          <wd-button type="primary" size="large" block @click="handleAdd">
            新增
          </wd-button>
        </view>
      </template>
    </page-paging>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';
import { useUserStore } from '@/store';
import { buildUrlWithParams } from '@/utils';

const loginUser = computed(() => useUserStore().info);

const currentTab = ref(0);

// 分页ref
const pagingRef = ref();

// 数据源
const datasource = ref<any[]>([]);

const channelList = ref<any>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function onChangeTab() {
  pagingRef.value.reload();
}

function handleAddPromotionCode(item: any) {
  uni.navigateTo({ url: `/pages-org/rate-policy/merch-promotion-code?id=${item.id}&extensionName=${item.policyDesc}` });
}

function handleAdd() {
  switch (currentTab.value) {
    case 0:
      uni.navigateTo({ url: `/pages-org/rate-policy/rate-policy-edit?policyType=${1}` });
      break;
    case 1:
      uni.navigateTo({ url: '/pages-org/rate-policy/activity-cashback-template-edit' });
      break;
    case 2:
      uni.navigateTo({ url: `/pages-org/rate-policy/rate-policy-edit?policyType=${2}` });
      break;
  }
}

function handleEditPolicy(item: any) {
  const url = buildUrlWithParams('/pages-org/rate-policy/rate-policy-edit', {
    id: item.id,
    policyDesc: item.policyDesc,
    policyType: item.policyType,
  });
  uni.navigateTo({ url });
}

function handleSyncPolicy(item: any) {
  const url = buildUrlWithParams('/pages-org/rate-policy/rate-policy-sync', {
    id: item.id,
    policyDesc: item.policyDesc,

  });
  uni.navigateTo({ url });
}

function handleEditCashbackPolicy(item: any) {
  uni.navigateTo({ url: `/pages-org/rate-policy/activity-cashback-template-edit?id=${item.id}` });
}

function foramtChanelCode2Name(code: string) {
  const item = channelList.value.find((item: any) => item.channelCode === code);
  return item?.channelName || '--';
}

async function getChannelList() {
  const data = await CommonApi.getChannelList({});
  channelList.value = data || [];
  return Promise.resolve();
}

async function queryDataSource(pageNo: number, pageSize: number) {
  let result = null;

  switch (currentTab.value) {
    case 0:
      // 机构政策
      result = RatePolicyApi.findPolicyPage({
        policyType: 1,
        pageNo,
        pageSize,
      });
      break;
    case 1:
      // 活动返现模板
      result = RatePolicyApi.findActivityCashbackTemplatePage({
        pageNo,
        pageSize,
      });
      break;
    case 2:
      // 商户政策
      if (!channelList.value.length) {
        await getChannelList();
      }

      result = RatePolicyApi.findPolicyPage({
        policyType: 2,
        pageNo,
        pageSize,
      });
      break;
  }

  result!
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
