<template>
  <view class="h-full flex flex-col">
    <view class="shrink-0">
      <view class="gap-primary" />
      <wd-form ref="formRef" :model="form">
        <wd-cell-group border>
          <wd-cell
            title="选择下级机构" :value="subOrgStr" title-width="100px" is-link
            @click="onSelectSubOrg"
          />
          <wd-input v-model="form.policyDesc" label="政策名称" placeholder="" label-width="80px" readonly />
          <!-- 渠道复选框 -->
          <wd-cell title="开通渠道" title-width="80px" center>
            <wd-checkbox-group v-model="checkedChannels" disabled inline custom-class="flex items-center flex-wrap">
              <wd-checkbox v-for="(item, key) in channelOptions" :key="key" :model-value="item.channelCode">
                {{ item.channelName }}
              </wd-checkbox>
            </wd-checkbox-group>
          </wd-cell>
        </wd-cell-group>
      </wd-form>
    </view>

    <view class="grow overflow-hidden">
      <wd-tabs v-model="currentChannelTab" slidable="always" custom-class="custom-rate-tabs">
        <wd-form ref="rateFormRef" :model="rateForm">
          <template v-for="(item, key) in rateForm.rateMapDTOList" :key="key">
            <wd-tab v-if="checkedChannels.includes(item.channelCode)" :title="item.channelName" :name="item.channelCode">
              <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                <RateModule
                  :rate-item="rateItem"
                  :form-prop-prefix="['rateMapDTOList', key, 'rateDTOList', keyr]"
                  :readonly="true"
                />
              </template>
            </wd-tab>
          </template>
        </wd-form>
      </wd-tabs>
    </view>

    <view class="shrink-0 p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        同步
      </wd-button>
    </view>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';
import { decodeUrlParams } from '@/utils';
import { emitter } from '@/utils/emitter';

const currentChannelTab = ref<undefined | string>(undefined);
type ChannelItem = {
  channelCode: string;
  channelName: string;
};
const channelOptions = ref<ChannelItem[]>([]);

const checkedChannels = ref<string[]>([]);

const loading = ref(false);

const toast = useToast();

const formRef = ref<FormInstance | null>(null);

const form = reactive({
  id: '',
  policyDesc: '',
  orgBaseInfoDTOList: [],
});

const rateFormRef = ref<FormInstance | null>(null);

const rateForm = reactive<any>({
  rateMapDTOList: [],
});

const subOrgStr = computed(() => {
  if (form.orgBaseInfoDTOList.length === 0) {
    return '';
  }

  return `已选中${form.orgBaseInfoDTOList.length}个下级机构`;
});

onLoad((query) => {
  query = decodeUrlParams(query || {});

  Object.assign(form, query);

  init();
});

function onSelectSubOrg() {
  uni.navigateTo({
    url: `/pages-org/rate-policy/sub-org-list?id=${form.id}`,
  });

  emitter.on('picker-sub-org', (data: any) => {
    form.orgBaseInfoDTOList = data;
  });
}

function save() {
  if (form.orgBaseInfoDTOList.length === 0) {
    toast.warning({
      msg: '请选择下级机构',
    });
    return;
  }

  const params: any = {
    id: form.id, // 政策主键id
    orgBaseInfoDTOList: form.orgBaseInfoDTOList,
  };

  loading.value = true;

  // 提交
  RatePolicyApi.syncPolicy(params)
    .then(() => {
      toast.success({
        msg: '操作成功',
        closed: () => {
          uni.navigateBack();
        },
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

async function init() {
  const channels = await CommonApi.getChannelList({});

  const detailData = await RatePolicyApi.detailItemPolicy({
    id: form.id,
  });
  const { rateDTOMap: rateMap } = detailData || {};

  const setedChannel = Object.keys(rateMap || {});

  const rateList = Object.values(rateMap || {}).flat();
  rateList.forEach((item: any) => {
    item.rateInfoDTO = item.rateInfoDTO || {};
    item.isSame = 1;
  });

  rateForm.rateMapDTOList = [];

  setedChannel.forEach((channelCode) => {
    const channel = channels.find((item: ChannelItem) => item.channelCode === channelCode);
    if (channel) {
      rateForm.rateMapDTOList.push({
        channelCode,
        channelName: channel.channelName,
        rateDTOList: rateMap[channelCode],
      });
      channelOptions.value.push(channel);
    }
  });

  checkedChannels.value = setedChannel;
}
</script>

<style lang="scss" scoped>
:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
