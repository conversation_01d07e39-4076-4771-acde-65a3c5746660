<template>
  <view class="h-full flex flex-col">
    <view class="shrink-0">
      <view class="gap-primary" />
      <wd-form ref="formRef" :model="form" :rules="rules">
        <wd-cell-group border>
          <wd-select-picker v-model="form.policyType" label="政策类型" label-width="80px" :columns="policyTypeOptions" type="radio" readonly />
          <wd-input v-model="form.policyDesc" label="政策名称" placeholder="请输入结算政策名称" prop="policyDesc" label-width="80px" />
          <!-- 渠道复选框 -->
          <wd-cell title="开通渠道" title-width="80px" center>
            <wd-checkbox-group v-model="checkedChannels" custom-class="flex items-center flex-wrap" inline>
              <wd-checkbox v-for="(item, key) in channelOptions" :key="key" :model-value="item.channelCode" :disabled="item.disabled">
                {{ item.channelName }}
              </wd-checkbox>
            </wd-checkbox-group>
          </wd-cell>
        </wd-cell-group>
      </wd-form>
    </view>

    <view class="grow overflow-hidden">
      <wd-tabs ref="rateTabsRef" v-model="currentChannelTab" slidable="always" custom-class="custom-rate-tabs">
        <wd-form ref="rateFormRef" :model="rateForm">
          <template v-for="(item, key) in rateForm.rateMapDTOList" :key="key">
            <wd-tab v-if="checkedChannels.includes(item.channelCode)" :title="item.channelName" :name="`${item.channelCode}`">
              <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                <RateModule
                  :rate-item="rateItem"
                  :form-prop-prefix="['rateMapDTOList', key, 'rateDTOList', keyr]"
                  :show-d0-single-fee="showD0SingleFee"
                />
              </template>
            </wd-tab>
          </template>
        </wd-form>
      </wd-tabs>
    </view>

    <view class="shrink-0 p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        保存
      </wd-button>
    </view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';
import { buildUrlWithParams, decodeUrlParams } from '@/utils';

const message = useMessage();

const policyTypeOptions = [
  {
    label: '下级团队结算政策',
    value: 1,
  },
  {
    label: '商户结算政策',
    value: 2,
  },
];

const showD0SingleFee = ref<boolean>(false);

const currentChannelTab = ref(0);
type ChannelItem = {
  channelCode: string;
  channelName: string;
  disabled?: boolean;
};
const channelOptions = ref<ChannelItem[]>([]);

const checkedChannels = ref<string[]>([]);

const loading = ref(false);

const toast = useToast();

const isUpate = ref<boolean>(false);

const formRef = ref<FormInstance | null>(null);

const rateTabsRef = ref<any>(null);

const form = reactive<any>({
  id: '',
  policyDesc: '',
  policyType: '',
});

// 规则
const rules: FormRules = {
  policyDesc: [{ required: true, message: '请输入政策名称' }],
};

const rateFormRef = ref<FormInstance | null>(null);

const rateForm = reactive<any>({
  rateMapDTOList: [],
});

onLoad((query) => {
  query = decodeUrlParams(query || {});

  if (query.id) {
    isUpate.value = true;
    form.id = query.id;
  }
  if (query.policyDesc) {
    form.policyDesc = query.policyDesc;
  }
  if (query.policyType) {
    form.policyType = Number(query.policyType);
    if (form.policyType === 2) {
      showD0SingleFee.value = true;
    }
  }

  init();
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: isUpate.value ? '编辑费率政策' : '新增费率政策',
  });
});

async function save() {
  // 校验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 检验费率表单
  const { valid: rateValid, errors: rateErrors } = await rateFormRef.value!.validate();
  if (!rateValid)
    return Promise.reject(rateErrors);

  // 校验是否有选中渠道
  if (checkedChannels.value.length === 0) {
    toast.warning('请至少选择一个渠道');
    return Promise.reject();
  }

  const rateDTOList: any = [];
  checkedChannels.value.forEach((channelCode) => {
    const rateMapDTOItem = rateForm.rateMapDTOList.find((item: any) => item.channelCode === channelCode);
    if (rateMapDTOItem) {
      rateDTOList.push(...rateMapDTOItem.rateDTOList);
    }
  });

  const params: any = {
    policyType: form.policyType,
    policyDesc: form.policyDesc,
    rateDTOList,
  };

  loading.value = true;

  let result = null;

  if (isUpate.value) {
    params.id = form.id;
    result = RatePolicyApi.editPolicy(params);
  }
  else {
    result = RatePolicyApi.addPolicy(params);
  }

  // 提交
  result
    .then(() => {
      if (isUpate.value) {
        if (form.policyType === 2) {
          message
            .confirm({
              msg: '费率政策修改成功, 需要进行商户费率政策同步才能生效, 是否立即同步商户费率政策?',
              title: '提示',
              confirmButtonText: '同步政策',
            })
            .then(async () => {
              await RatePolicyApi.syncMerchantRatePolicy({
                id: form.id,
              });
              toast.success({
                msg: '操作成功',
                closed: () => {
                  uni.navigateBack();
                },
              });
            })
            .catch(() => {
              uni.navigateBack();
            });
        }
        else {
          message
            .confirm({
              msg: '费率政策修改成功, 需要进行政策同步才能生效, 是否立即前往同步政策?',
              title: '提示',
              confirmButtonText: '同步政策',
            })
            .then(() => {
              const url = buildUrlWithParams('/pages-org/rate-policy/rate-policy-sync', {
                id: form.id,
                policyDesc: form.policyDesc,

              });
              uni.redirectTo({ url });
            })
            .catch(() => {
              uni.navigateBack();
            });
        }
      }
      else {
        toast.success({
          msg: '操作成功',
          closed: () => {
            uni.navigateBack();
          },
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

async function init() {
  const channels = await CommonApi.getChannelList({});
  const data = await RatePolicyApi.getOwnerRateList();

  const { selfRateMap: allRateMap, sysTempMap: merchRateTempMap } = data || {};

  const allChannels: any = Object.keys(allRateMap || {});
  let setedChannels: any = [];

  let rateListMap: any = {};

  if (isUpate.value) {
    const detailData = await RatePolicyApi.detailItemPolicy({
      id: form.id,
    });
    const { rateDTOMap: rateDetailMap } = detailData || {};

    setedChannels = Object.keys(rateDetailMap || {});

    const newChannels = allChannels.filter((key: string) => !setedChannels.includes(key));

    if (form.policyType === 2) {
      allChannels.forEach((channelCode: string) => {
        allRateMap[channelCode].forEach((item: any) => {
          item.rateInfoDTO.rateType = item.rateType || item.rateInfoDTO.rateType;

          if (merchRateTempMap && merchRateTempMap[channelCode]) {
            merchRateTempMap[channelCode].forEach((mItem: any) => {
              if (item.rateInfoDTO.rateType === mItem.rateInfoDTO.rateType) {
                item.rateInfoDTO = Object.assign({}, mItem.rateInfoDTO);
              }
            });
          }
        });
      });
    }

    setedChannels.forEach((code: string) => {
      if (allChannels.includes(code)) {
        const setedTemplateNoList = rateDetailMap[code].map((r: any) => r.templateNo);
        const allTemplateNoList = allRateMap[code].map((a: any) => a.templateNo);
        const newTemplateNoList = allTemplateNoList.filter((key: string) => !setedTemplateNoList.includes(key));

        const newTemplateList = allRateMap[code].filter((i: any) => newTemplateNoList.includes(i.templateNo));
        newTemplateList.forEach((t: any) => {
          t.isAddedTemp = true;
        });

        rateDetailMap[code].push(...newTemplateList);
      }
    });

    newChannels.forEach((code: string) => {
      rateDetailMap[code] = allRateMap[code];
    });

    Object.keys(rateDetailMap || {}).forEach((key: string) => {
      rateDetailMap[key].sort((a: any, b: any) => {
        return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
      });
    });

    rateListMap = rateDetailMap;
  }
  else {
    rateListMap = allRateMap;

    if (form.policyType === 2) {
      allChannels.forEach((channelCode: string) => {
        rateListMap[channelCode].forEach((item: any) => {
          item.rateInfoDTO.rateType = item.rateType || item.rateInfoDTO.rateType;

          if (merchRateTempMap && merchRateTempMap[channelCode]) {
            merchRateTempMap[channelCode].forEach((mItem: any) => {
              if (item.rateInfoDTO.rateType === mItem.rateInfoDTO.rateType) {
                item.rateInfoDTO = Object.assign({}, mItem.rateInfoDTO);
              }
            });
          }
        });

        rateListMap[channelCode].sort((a: any, b: any) => {
          return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
        });
      });
    }
  }

  const rateList = Object.values(rateListMap || {}).flat();
  rateList.forEach((item: any) => {
    item.rateInfoDTO = item.rateInfoDTO || {};
    item.isSame = 1;
  });

  rateForm.rateMapDTOList = [];

  Object.keys(rateListMap || {}).forEach((channelCode) => {
    const channel = channels.find((item: ChannelItem) => item.channelCode === channelCode);
    if (channel) {
      rateForm.rateMapDTOList.push({
        channelCode,
        channelName: channel.channelName,
        rateDTOList: rateListMap[channelCode],
      });

      if (isUpate.value) {
        if (setedChannels.includes(channelCode)) {
          checkedChannels.value.push(channelCode);
          channel.disabled = true;
        }
      }
      else {
        checkedChannels.value.push(channelCode);
      }
      channelOptions.value.push(channel);
    }
  });

  currentChannelTab.value = checkedChannels.value[0];
}

function onCheckedChannelChange({ value }: { value: any }) {
  // 取差集 (A-B)∪(B-A), 即变化的项
  const a = value;
  const b = checkedChannels.value;
  const diffValue = a.reduce((u, va) => (u.every(vu => vu != va) ? u.concat(va) : u.filter(vu => vu != va)), b);
  const diffItem = channelOptions.value.find(c => c.channelCode === diffValue[0]);

  if (diffItem) {
    const isAdd = value.length > b.length;
    if (isAdd) {
      currentChannelTab.value = `${diffItem.channelCode}`;
    }
    else if (`${diffItem.channelCode}` === currentChannelTab.value) {
      const index = b.indexOf(diffItem.channelCode);
      const nextTab = b[index + 1] || b[index - 1];
      if (nextTab) {
        const nextTabItem = channelOptions.value.find(c => c.channelCode === nextTab);
        currentChannelTab.value = `${nextTabItem?.channelCode}`;
      }
    }
  }
  console.log('currentChannelTab', currentChannelTab.value);

  rateTabsRef.value?.setActive(currentChannelTab.value, true);

  checkedChannels.value = value;
}
</script>

<style lang="scss" scoped>
:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
