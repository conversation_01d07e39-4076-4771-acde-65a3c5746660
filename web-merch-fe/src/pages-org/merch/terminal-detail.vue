<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="gap-primary" />
    <wd-cell-group title="基础信息" custom-class="cell-group">
      <wd-cell title="终端SN" :value="detail.terminalSn || '--'" />
      <wd-cell title="终端型号" :value="detail.modelName || '--'" />
      <wd-cell title="终端状态" :value="useStatusMap[detail.useStatus] || '--'" />
      <wd-cell title="商户名称" :value="detail.merchantName || '--'" />
      <wd-cell title="商户编号" :value="detail.merchantNo || '--'" />
      <wd-cell title="代理商编号" :value="detail.agentNo || '--'" />
      <wd-cell title="入库时间" :value="detail.createTime || '--'" />
      <wd-cell title="绑定时间" :value="detail.bindTime || '--'" />
    </wd-cell-group>

    <view class="gap-primary" />
    <wd-cell-group title="活动信息" custom-class="cell-group" use-slot>
      <template #value>
        <view v-if="detail.useStatus === 2" class="flex items-center justify-between">
          <text class="text-primary" @click="viewEquityOrders">
            查看权益订单
          </text>
        </view>
      </template>

      <view class="h-10rpx bg-primary" />

      <wd-cell title="服务费" :value="detail.serviceFee || '--'" />
      <wd-cell title="通讯费收取开关" :value="simSwitchMap[detail.simSwitch] || '--'" />

      <view class="h-10rpx bg-primary" />

      <template v-if="detail.simSwitch === 1">
        <wd-cell title="免费使用天数" :value="`${detail.simFreeDay || '--'}天`" />
        <template v-for="(period, key) in simFeePeriodConfig" :key="key">
          <wd-cell :title="`${period.periodName}(金额/天数)`" :value="`${period.periodFeeField}元/${period.periodDayField}天`" />
        </template>
      </template>
    </wd-cell-group>
  </view>
</template>

<script lang="ts" setup>
import { MerchApi } from '@/api-org/merch';
import { buildUrlWithParams, decodeUrlParams } from '@/utils';

// 流量费周期配置
const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
  },
  {
    periodName: '二期',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
  },
  {
    periodName: '三期',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
  },
  {
    periodName: '标准期',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
  },
];

const detail = ref<any>({});

const simFeePeriodConfig = ref<any>(simFeePeriodConfigDef);

type RouteParams = {
  merchantNo: string;
  terminalSn: string;
};

const useStatusMap: EnumMap = {
  0: '闲置',
  1: '已分配',
  2: '已绑机',
};

const simSwitchMap: EnumMap = {
  0: '关闭',
  1: '打开',
};

onLoad((query) => {
  query = decodeUrlParams(query || {});
  queryDetail(query as RouteParams);
});

async function queryDetail(where: RouteParams) {
  const res = await MerchApi.getTerminalBaseInfoBySn({ ...where });
  detail.value = Object.assign({}, res);

  simFeePeriodConfig.value.forEach((period: any) => {
    period.periodFeeField = detail.value[period.periodFeeField] || '--';
    period.periodDayField = detail.value[period.periodDayField] || '--';
  });
}

/** 查看权益订单 */
function viewEquityOrders() {
  const url = buildUrlWithParams('/pages-org/terminal-manage/terminals/equity-orders', { terminalSn: detail.value.terminalSn, modelName: detail.value.modelName });
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  overflow: hidden;

  .wd-cell {
    @apply py-0 mb-10px;
  }

  .wd-cell__wrapper {
    @apply py-0;
  }
}
</style>
