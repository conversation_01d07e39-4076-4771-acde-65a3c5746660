<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部搜索栏 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class"
          safe-area-inset-top
          @click-left="handlePageHome"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left :placeholder="`请输入${orgTypeStr}编号查询`" custom-class="w-full"
                @search="onSearch"
              />
            </view>
          </template>
        </wd-navbar>

        <view class="px-20rpx">
          <view class="flex flex-col items-center py-20rpx">
            <text>累计商户数</text>
            <text class="mt-10rpx">
              {{ merchCountSummary.totalCount }}
            </text>
          </view>
          <view class="w-full flex items-center rounded-2 pb-20rpx">
            <view class="w-33.3% flex flex-col items-center">
              <text>今日新增数</text>
              <text class="mt-10rpx">
                {{ merchCountSummary.todayMerchAddCount }}
              </text>
            </view>
            <view class="lr-border w-33.3% flex flex-col items-center">
              <text>本月新增数</text>
              <text class="mt-10rpx">
                {{ merchCountSummary.thisMonthMerchAddCount }}
              </text>
            </view>
            <view class="w-33.3% flex flex-col items-center">
              <text>上月新增数</text>
              <text class="mt-10rpx">
                {{ merchCountSummary.lastMonthMerchAddCount }}
              </text>
            </view>
          </view>
        </view>

        <view v-if="[3, 5].includes(loginUser.orgType as number)">
          <wd-tabs v-model="where.selType" @change="onChangeWhereSelType">
            <wd-tab title="自营商户" name="1" />
            <wd-tab title="团队商户" name="2" />
          </wd-tabs>
        </view>
      </template>

      <view class="mt-20rpx bg-white">
        <wd-drop-menu custom-class="flex">
          <wd-drop-menu-item v-model="whereAuthStatus" :[authStatusTitleProp]="'入网状态'" :options="authStatusOptions" @change="onChangeWhereAuthStatus" />
          <wd-drop-menu-item v-if="[3, 5].includes(loginUser.orgType as number)" v-model="whereRatePolicy" :[ratePolicyTitleProp]="'政策筛选'" :options="ratePolicyOptions" @change="onChangeWhereRatePolicy" />
        </wd-drop-menu>
      </view>

      <view class="mt-20rpx">
        <wd-search
          hide-cancel placeholder-left placeholder="请输入商户名或手机号查询" custom-class="w-full"
          @search="onSearchByMobile"
        />
      </view>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-xl bg-white p-26rpx shadow"
          @click="onClickItem(item)"
        >
          <view>
            <view class="flex items-center font-bold">
              {{ item.merchantName }}
              <i
                v-if="item.merchantName"
                class="copy-icon"
                @click.stop="copyData(item.merchantName)"
              />
            </view>
            <view class="mt-20rpx flex items-center">
              <view class="grow">
                <view class="cell">
                  <text class="cell-label">
                    用户编号:
                  </text>
                  <view class="cell-value flex items-center">
                    {{ item.merchantNo }}
                    <i
                      v-if="item.merchantNo"
                      class="copy-icon"
                      @click.stop="copyData(item.merchantNo)"
                    />
                  </view>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    加入时间:
                  </text>
                  <text class="cell-value">
                    {{ item.createTime }}
                  </text>
                </view>
                <view v-if="[1, 2].includes(loginUser.orgType as number)" class="cell">
                  <text class="cell-label">
                    所属{{ orgTypeStr }}:
                  </text>
                  <text class="cell-value">
                    {{ item.directTeamName }}
                  </text>
                </view>
                <view v-if="[3, 5].includes(loginUser.orgType as number) && where.selType === '2'" class="cell">
                  <text class="cell-label">
                    团队名称
                  </text>
                  <text class="cell-value">
                    {{ item.directTeamName }}
                  </text>
                </view>
              </view>
              <i class="i-mdi-chevron-right shrink-0 text-50rpx text-#00000040" />
            </view>

            <view class="mt-8px">
              <wd-tag custom-class="custom-tag-class" mark>
                {{ authStatusMap[item.authStatus] || '--' }}
              </wd-tag>
            </view>

            <!-- 其他信息 -->
            <view class="mt-20rpx rounded-2 bg-primary p-20rpx text-28rpx">
              <view class="flex items-start">
                <view class="w-50%">
                  <text>当日交易:</text>
                  <text>{{ item.todayTotalTransAmt }}</text>
                </view>
                <view class="w-50%">
                  <text>本月交易:</text>
                  <text>{{ item.thisMonthTotalTransAmt }}</text>
                </view>
              </view>
              <!-- <view class="mt-10rpx flex items-start">
                <view class="w-50%">
                  <text>上月交易:</text>
                  <text>{{ item.lastMonthTotalTransAmt }}</text>
                </view>
                <view class="w-50%">
                  <text>累计交易:</text>
                  <text>{{ item.totalTransAmt }}</text>
                </view>
              </view> -->
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';
import { MerchApi } from '@/api-org/merch';
import { buildUrlWithParams } from '@/utils';
import { useUserStore } from '@/store';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CHANNEL_CODE } from '@/config/setting';

const loginUser = computed(() => useUserStore().info);

const orgTypeStr = computed(() => {
  if (loginUser.value?.orgType === 1) {
    return '运营中心';
  }
  return '代理商';
});

// 分页器ref
const pagingRef = ref();

// 列表查询条件
const where = reactive<any>({
  nameOrMobile: '', // 商户名称或者手机号(模糊匹配)
  selType: '1', // 查询类型 1-自营 2-团队 默认查询 1
  userNo: '',
  authStatus: '', // 入网状态 (选填) 1认证待审核 2审核不通过 3认证通过 4入网成功
  policyNo: '', // 政策编号(选填)
  orderBy: '',
});

const merchCountSummary = ref<any>({
  totalCount: 0, // 商户总数
  todayMerchAddCount: 0, // 今日新增商户数
  thisMonthMerchAddCount: 0, // 本月新增商户数
  lastMonthMerchAddCount: 0, // 上月新增商户数
});

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const whereAuthStatus = ref('0');
const authStatusOptions = [
  { label: '全部', value: '0' },
  { label: '认证待审核', value: '1' },
  { label: '审核不通过', value: '2' },
  { label: '认证通过', value: '3' },
  { label: '入网成功', value: '4' },
];

const authStatusMap: EnumMap = {
  1: '认证待审核',
  2: '审核不通过',
  3: '认证通过',
  4: '入网成功',
};

const whereRatePolicy = ref('0');
const ratePolicyOptions = ref<any>([]);
function onChangeWhereRatePolicy({ value }: { value: string }) {
  where.ratePolicyId = value === '0' ? null : whereRatePolicy.value;
  pagingRef.value.reload();
}

const authStatusTitleProp = computed(() => {
  const prop = whereAuthStatus.value === '0' ? 'title' : '';
  return prop;
});

const ratePolicyTitleProp = computed(() => {
  const prop = whereRatePolicy.value === '0' ? 'title' : '';
  return prop;
});

onShow(() => {
  queryMerchCountSummary();
  pagingRef.value?.reload();

  if ([3, 5].includes(loginUser.value?.orgType as number)) {
    queryRatePolicy();
  }
});

async function queryRatePolicy() {
  let data = await RatePolicyApi.getRatePolicylistOfOrg({
    policyType: 2,
    channelCode: CHANNEL_CODE,
  });
  data = data || [];
  data.unshift({
    policyNo: '0',
    policyDesc: '全部',
  });

  const formatData = data.map((item: any) => {
    return {
      label: item.policyDesc,
      value: item.policyNo,
    };
  });

  ratePolicyOptions.value = formatData || [];
}

/** 根据状态查询数据 */
function onChangeWhereAuthStatus({ value }: { value: any }) {
  where.authStatus = value === '0' ? null : Number(value);
  pagingRef.value.reload();
}

function onChangeWhereSelType() {
  pagingRef.value.reload();
}

/** 搜索查询数据 */
function onSearch({ value }: any) {
  where.userNo = value;
  pagingRef.value?.reload();
}

/** 搜索查询数据 */
function onSearchByMobile({ value }: any) {
  where.nameOrMobile = value;
  pagingRef.value?.reload();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

async function queryMerchCountSummary() {
  const data = await MerchApi.queryMerchantCount({});
  merchCountSummary.value = Object.assign({}, data);
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  MerchApi.findPage({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

/**
 * 点击列表项
 */
function onClickItem(item: any) {
  const url = buildUrlWithParams('/pages-org/merch/merch-detail', {
    merchantNo: item.merchantNo,
  });
  uni.navigateTo({ url });
}

function handleAddAgent() {
  uni.navigateTo({ url: '/pages-org/team/agent/agent-update' });
}

function resubmitMerch(item: any) {
  uni.navigateTo({ url: `/pages/report/merch-dredge/index?handleType=3&id=${item.id}&isMicro=${item.isMicro}` });
}

function handlePageHome() {
  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }

:deep(.custom-tag-class){
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.lr-border{
  position: relative;
  overflow: hidden;

  &::before,
  &::after{
   position: absolute;
   top: 0;
   width: 1px;
   height:60%;
   background: #e6e6e6;
   content: '';
   transform: translateY(50%);
  }

  &::before{
   left: 0;

  }

  &::after{
   right: 0;
  }

}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
