<template>
  <view class="h-full overflow-y-scroll">
    <view class="rounded-lg bg-primary py-20px">
      <view class="flex flex-wrap justify-center">
        <template v-for="(item, key) in fileList" :key="key">
          <view v-if="item.show" class="basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full"
              @choose="(value) => onChooseFile(value, item)"
            />
          </view>
        </template>
      </view>
    </view>

    <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
      <wd-cell-group border>
        <wd-cell title="账户类型" title-width="100px" center>
          <wd-radio-group v-model="form.accountType" :disabled="isUpdate || [1, 2].includes(cardOptType)" shape="dot" inline custom-class="flex items-center">
            <wd-radio value="S">
              对私
            </wd-radio>
            <wd-radio value="G">
              对公
            </wd-radio>
          </wd-radio-group>
        </wd-cell>
        <wd-input
          v-model="form.bankAccountName"
          label="账户名" label-width="100px" placeholder="账户名"
          readonly align-right
        />
        <wd-input
          v-model="form.bankAccountNo" prop="bankAccountNo"
          label="结算卡卡号" :placeholder="form.accountType === 'S' ? '可图片识别' : '结算卡卡号'" label-width="88px"
          align-right clearable
        />
        <wd-cell
          title="开户行所在地" title-width="100px"
          prop="bankCity" is-link
          @click="onSelectArea"
        >
          <wd-textarea
            v-model="bankArea"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行总行" title-width="100px"
          prop="typeCode" is-link
          @click="onSelectBankType"
        >
          <wd-textarea
            v-model="form.typeName"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行支行" title-width="100px"
          prop="bankSubName" is-link
          @click="onSelectBankSub"
        >
          <wd-textarea
            v-model="form.bankSubName"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-input
          v-model="form.mobile" prop="mobile"
          label="预留手机号" label-width="100px" placeholder="请输入预留手机号"
          align-right
        />
        <wd-cell />
      </wd-cell-group>
      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { emitter } from '@/utils/emitter';
import type { BankAccountInfo } from '@/api/bank-card/types';
import { Toast } from '@/utils';
import { CommonApi } from '@/api/common';
import { CHANNEL_CODE } from '@/config/setting';
import { SettleCardApi } from '@/api-org/settle-card';
import { UserApi } from '@/api-org/user';

const bankArea = ref('');
const toast = useToast();

const isUpdate = ref(false);
const cardOptType = ref<number>(-1);

// 表单
const form: BankAccountInfo = reactive<any>({
  accountType: 'G',
  bankAccountNo: '',
});
// 规则
const rules: FormRules = {
  bankCity: [{ required: true, message: '请选择' }],
  typeCode: [{ required: true, message: '请选择' }],
  bankSubName: [{ required: true, message: '请选择' }],
  bankAccountNo: [{ required: true, message: '请输入结算卡卡号' }],
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
};
const formRef = ref<FormInstance | null>(null);

const accountType = computed(() => form.accountType);

// 文件列表
const fileList = ref <GivenUploadProps[] > (
  [
    {
      fileName: '银行卡正面',
      placeholder: require('@/static/images/bank_card.png'),
      fileData: '',
      fileType: 3,
      show: computed(() => accountType.value === 'S'),
    },
    {
      fileName: '开户许可证',
      placeholder: require('@/static/images/bank_card.png'),
      fileData: '',
      fileType: 16,
      show: computed(() => accountType.value === 'G'),
    },
  ],
);

onLoad((query) => {
  isUpdate.value = query?.isUpdate === '1';
  cardOptType.value = Number(query?.cardOptType);
  if (!isUpdate.value) {
    if (cardOptType.value === 1) {
      form.accountType = 'S';
    }
  }
  else {
    queryDetail(query?.id);
  }
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: isUpdate.value ? '修改结算卡' : '新增结算卡',
  });
});

onMounted(() => {
  queryOrgUserInfo();
});

async function queryDetail(id: any) {
  const data = await SettleCardApi.detail({ id });

  Object.assign(form, data, {});
  bankArea.value = [data.bankProvinceName, data.bankCityName].join('');
  form.typeName = data.bankName;

  fileList.value.forEach((item) => {
    const imageList = data.imageList || [];
    const imageItem = imageList.find((i: any) => i.imageType === item.fileType);
    if (imageItem) {
      item.fileData = imageItem.imagePath;
      item.id = imageItem.id;
    }
  });
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageJsonList = await uploadFile();
  form.imageJsonList = imageJsonList;

  // 默认借记卡
  form.cardType = form.cardType || 1;

  let result;
  if (isUpdate.value) {
    result = SettleCardApi.edit;
  }
  else {
    result = SettleCardApi.add;
  }
  await result(form);
  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

/*
 * 校验文件
 */
async function checkFile() {
  const isCheckPass = fileList.value.every((f) => {
    if (!f.fileData && f.show !== false) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileListHasVal = fileList.value.filter(i => !!i.fileData);
  const noChangeFiles: any = [];
  const changeedFiles: any = [];
  fileListHasVal.forEach((i: any) => {
    if (/^(https?:)/.test(i.fileData)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList: any = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

async function onChooseFile(value: string, item: GivenUploadProps) {
  if (item.fileType === 3) {
    const res = await CommonApi.ocrBankCard({ imgFile: value });
    form.bankAccountNo = res?.cardNum;

    const cardTypeMap: Record<string, number> = {
      DC: 1,
      CC: 2,
      SCC: 3,
      PC: 4,
    };
    form.cardType = cardTypeMap[res?.cardType] as any;
  }
  else if (item.fileType === 16) {
    form.cardType = null as any;
  }
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index' });
  emitter.on('picker-area', (data: any) => {
    const [province, city] = data;
    bankArea.value = data.map((item: any) => item.areaName).join('');
    form.bankProvince = province.areaCode;
    form.bankCity = city.areaCode;

    form.bankSubName = '';
    form.bankChannelNo = '';
  });
}

function onSelectBankType() {
  uni.navigateTo({ url: `/pages/picker-view/bank/bank-type` });
  emitter.on('picker-bank-type', (data: any) => {
    form.typeName = data.typeName;
    form.typeCode = data.typeCode;

    form.bankSubName = '';
    form.bankChannelNo = '';
  });
}

function onSelectBankSub() {
  const { typeCode, bankCity, bankProvince } = form;
  if (!bankCity)
    return Toast('请选择开户行所在地');
  if (!typeCode)
    return Toast('请选择开户行总行');

  const query = {
    typeCode,
    provinceCode: bankProvince,
    cityCode: bankCity,
  };
  uni.navigateTo({
    url: `/pages/picker-view/bank/bank-sub?where=${encodeURIComponent(JSON.stringify(query))}`,
  });

  emitter.on('picker-bank-sub', (data: any) => {
    form.bankSubName = data.bankName;
    form.bankChannelNo = data.clearChannelNo;
  });
}

async function queryOrgUserInfo() {
  const data = await UserApi.getOrgUserInfo();
  form.bankAccountName = data?.legalName;
}
</script>
