<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx"
          @click="handleToDetail(item)"
        >
          <view class="p-20rpx text-center text-#909399">
            <text>{{ item.createTime }}</text>
          </view>

          <view class="flex flex-col rounded-md bg-white px-30rpx pt-30rpx">
            <view class="text-32rpx">
              <wd-text :text="item.announceTitle" bold :lines="1" color="#555" />
            </view>
            <view class="mb-20rpx">
              <wd-text :text="item.announceContent" :lines="2" />
            </view>
            <view class="flex items-center justify-end border border-t-#edf0f3 border-t-solid py-30rpx">
              <text class="text-26rpx text-#909399">
                查看详情>>
              </text>
            </view>
          </view>
        </view>
      </view>
    </page-paging>
  </view>
</template>

<script lang="ts" setup>
import { NoticeApi } from '@/api-org/notice';

const pagingRef = ref();

const datasource = ref<any[]>([]);

function queryDataSource() {
  NoticeApi.queryList()
    .then((data) => {
      pagingRef.value.complete(data);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

function handleToDetail(item: any) {
  uni.navigateTo({
    url: `/pages/common/notice/notice-detail?id=${item.id}`,
  });
}
</script>
