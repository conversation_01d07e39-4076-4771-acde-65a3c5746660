<template>
  <view class="h-full overflow-y-scroll">
    <view class="gap-primary" />
    <view class="p-20rpx">
      <wd-text :text="detail.announceContent" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { NoticeApi } from '@/api-org/notice';

const detail = ref<any>({});

onLoad((option) => {
  getDetail(option?.id);
});

function getDetail(id: string) {
  NoticeApi.queryDetail({ id })
    .then((data) => {
      detail.value = data;
    });
}
</script>
