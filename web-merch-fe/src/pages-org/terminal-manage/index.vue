<template>
  <view class="h-full overflow-y-scroll">
    <page-paging ref="pagingRef" refresher-only @on-refresh="getTerminalSummary">
      <view class="p-30rpx">
        <view class="flex flex-col justify-center rounded-8px bg-#517cf0 py-30rpx text-white">
          <view class="flex flex-col items-center">
            <text>总库存</text>
            <text class="mt-10rpx">
              {{ terminalSummary.terminalCount || 0 }}
            </text>
          </view>

          <view class="mt-30rpx flex">
            <view class="flex grow">
              <view class="w-50% flex flex-col items-center">
                <text>已绑定</text>
                <text class="mt-16rpx">
                  {{ terminalSummary.bindingTerminal || 0 }}
                </text>
              </view>
              <view class="w-50% flex flex-col items-center">
                <text>未绑定</text>
                <text class="mt-16rpx">
                  {{ terminalSummary.unbindingTerminal || 0 }}
                </text>
              </view>
            </view>

            <view class="w-1px flex-shrink-0 bg-white" />

            <view class="flex grow">
              <view class="w-50% flex flex-col items-center">
                <text>已激活</text>
                <text class="mt-16rpx">
                  {{ terminalSummary.paidTerminal || 0 }}
                </text>
              </view>
              <view class="w-50% flex flex-col items-center">
                <text>未激活</text>
                <text class="mt-16rpx">
                  {{ terminalSummary.unpaidTerminal || 0 }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 菜单 -->
        <view class="pos-relative mt-50rpx bg-transparent">
          <wd-grid :column="4" bg-color="transparent" clickable custom-class="custom-grid-class">
            <template v-for="(item, key) in menus" :key="key">
              <wd-grid-item v-if="!item.hidden" use-slot @itemclick="toMenuUrl(item)">
                <view class="flex flex-col items-center">
                  <view class="rounded-2xl p-12rpx">
                    <i class="size-100rpx text-#517cf0" :class="item.icon_class" />
                  </view>
                  <text class="mt-20rpx text-28rpx">
                    {{ item.menu_name }}
                  </text>
                </view>
              </wd-grid-item>
            </template>
          </wd-grid>
        </view>
      </view>
    </page-paging>
  </view>
</template>

<script lang="ts" setup>
import { TerminalManageApi } from '@/api-org/terminal-manage';

const pagingRef = ref();

const terminalSummary = ref<any>({});

// 菜单列表
interface IMenu {
  menu_name: string;
  icon_class: string;
  to: string; // 菜单跳转地址,
  hidden?: boolean; // 是否隐藏菜单项 默认不隐藏
};
const menus: IMenu[] = [
  {
    menu_name: '机具列表',
    icon_class: 'i-mdi-clipboard-minus-outline',
    to: '/pages-org/terminal-manage/terminals/index',
  },
  {
    menu_name: '机具下发',
    icon_class: 'i-mdi-briefcase-upload-outline',
    to: '/pages-org/terminal-manage/terminal-delivery/terminal-delivery-type',
  },
  {
    menu_name: '费率设置',
    icon_class: 'i-mdi-rename-box-outline',
    to: '/pages-org/terminal-manage/rate-setting/index',
  },
  {
    menu_name: '活动设置',
    icon_class: 'i-mdi-credit-card-check-outline',
    to: '/pages-org/terminal-manage/activity-setting/index',
  },
  {
    menu_name: '机具回拨',
    icon_class: 'i-mdi-briefcase-download-outline',
    to: '/pages-org/terminal-manage/terminal-callback/index',
  },
  {
    menu_name: '调拨记录',
    icon_class: 'i-mdi-list-box-outline',
    to: '/pages-org/terminal-manage/transfer-record/index',
  },
  {
    menu_name: '奖励政策',
    icon_class: 'i-mdi-progress-star-four-points',
    to: '/pages-org/terminal-manage/reward-policy/index',
  },
];

onMounted(() => {
  getTerminalSummary();
});

async function getTerminalSummary() {
  const data = await TerminalManageApi.terminalSummary();
  terminalSummary.value = data || {};
  pagingRef.value?.complete();
}

function toMenuUrl({ to: url }: IMenu) {
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
:deep(.custom-grid-class){
  .wd-grid-item{
    background-color: transparent !important;
  }

  .wd-grid-item__content{
    padding: 0;
    margin-bottom: 12px;
    background-color: transparent !important;
  }
}
</style>
