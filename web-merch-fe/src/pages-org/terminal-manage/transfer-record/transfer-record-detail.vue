<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class"
          safe-area-inset-top
          @click-left="handleBack"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入机具SN号或商户编号" custom-class="w-full"
                @search="onSearchByTermSn"
              />
            </view>
          </template>
        </wd-navbar>
        <view class="gap-primary" />
      </template>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-lg bg-primary p-20rpx"
        >
          <view class="flex">
            <view class="flex flex-1 items-center">
              <text>SN号：{{ item.terminalSn }}</text>
              <i
                v-if="item.terminalSn"
                class="copy-icon"
                @click.stop="copyData(item.terminalSn)"
              />
            </view>
            <view class="ml-10px shrink-0">
              <text :class="{ 'text-red-6': item.status === 0 }">
                {{ statusMap[item.status] }}
              </text>
            </view>
          </view>

          <view v-if="item.status === 0" class="mt-20rpx border-t-#edf0f3 border-t-solid pt-20rpx text-red-6">
            <text>失败原因：{{ item.failedDesc || '--' }}</text>
          </view>
        </view>
      </view>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';
import { TerminalManageApi } from '@/api-org/terminal-manage';

// 分页器ref
const pagingRef = ref();

// 查询条件
const where = reactive<any>({
  batchNo: '', // 批次号
  terminalSn: '', // 终端序列号(SN)
});

// 列表数据
const datasource = ref<any> ([]);

const statusMap: any = {
  0: '操作失败',
  1: '操作成功',
};

onLoad((options) => {
  where.batchNo = options?.batchNo || '';
});

/** 搜索数据 */
function onSearchByTermSn({ value }: any) {
  where.terminalSn = value;
  pagingRef.value?.reload();
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  TerminalManageApi.terminalTransferPage({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

function handleBack() {
  uni.navigateBack();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>

<style lang="scss" scoped>
:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
