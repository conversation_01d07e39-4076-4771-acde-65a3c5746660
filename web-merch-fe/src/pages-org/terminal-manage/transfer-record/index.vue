<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <wd-tabs v-model="where.optType" @change="reload">
          <wd-tab title="下发记录" :name="0" />
          <wd-tab title="回拨记录" :name="1" />
        </wd-tabs>
      </template>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-xl bg-white shadow"
        >
          <template v-if="item.optType === 0">
            <view class="p-26rpx">
              <view class="cell">
                <text class="cell-label">
                  下发机构名称:
                </text>
                <text class="cell-value">
                  {{ item.targAgentName }}
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  下发机构编号:
                </text>
                <view class="cell-value flex items-center">
                  {{ item.targAgentNo }}
                  <i
                    v-if="item.targAgentNo"
                    class="copy-icon"
                    @click.stop="copyData(item.targAgentNo)"
                  />
                </view>
              </view>
              <view class="cell">
                <text class="cell-label">
                  操作类型:
                </text>
                <text class="cell-value">
                  {{ optTypeMap[item.optType] }}
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  操作时间:
                </text>
                <text class="cell-value">
                  {{ item.createTime }}
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  台数:
                </text>
                <text class="cell-value">
                  {{ `${item.count || 0}台/${Number(item.count || 0) - Number(item.failedCount || 0)}台` }} (成功)
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  状态:
                </text>
                <text class="cell-value">
                  {{ callbackStatusMap[item.isCallback] }}
                </text>
              </view>
            </view>
          </template>

          <template v-else-if="item.optType === 1">
            <view class="p-26rpx">
              <view class="cell">
                <text class="cell-label">
                  回拨机构名称:
                </text>
                <text class="cell-value">
                  {{ item.targAgentName }}
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  回拨机构编号:
                </text>
                <view class="cell-value flex items-center">
                  {{ item.targAgentNo }}
                  <i
                    v-if="item.targAgentNo"
                    class="copy-icon"
                    @click.stop="copyData(item.targAgentNo)"
                  />
                </view>
              </view>
              <view class="cell">
                <text class="cell-label">
                  操作类型:
                </text>
                <text class="cell-value">
                  {{ optTypeMap[item.optType] }}
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  操作时间:
                </text>
                <text class="cell-value">
                  {{ item.createTime }}
                </text>
              </view>
              <view class="cell">
                <text class="cell-label">
                  台数:
                </text>
                <text class="cell-value">
                  {{ `${item.count || 0}台/${Number(item.count || 0) - Number(item.failedCount || 0)}台` }} (成功)
                </text>
              </view>
            </view>
          </template>

          <!-- 操作栏 -->
          <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
            <view v-if="item.optType === 0 && item.batchStatus !== 2" class="grow py-20rpx text-#4d80f0" @click="callbackTermByBatchNo(item)">
              批次回拨
            </view>
            <view class="border-1px border-#edf0f3 border-r-solid" />
            <view class="grow py-20rpx" @click="handleDetail(item)">
              查看详情
            </view>
          </view>
        </view>
      </view>
    </page-paging>
  </view>

  <!-- 挂载点 -->
  <wd-message-box />
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { useClipboard } from '@/hooks';
import { TerminalManageApi } from '@/api-org/terminal-manage';

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 查询条件
const where = reactive<any>({
  optType: 0, // 0 划拨（下发） 1回拨 (必填)
});

// 列表数据
const datasource = ref<any> ([]);

const optTypeMap: any = {
  0: '下发',
  1: '回拨',
};

const callbackStatusMap: any = {
  0: '不可回拨',
  1: '正常',
  2: '已回拨',
};

/**
 * 批次回拨
 */
function callbackTermByBatchNo(item: any) {
  message
    .confirm({
      msg: `确定执行批次回拨吗?`,
    }).then(async () => {
      await TerminalManageApi.callbackTermByBatchNo({ batchNo: item.batchNo });
      message.alert({
        msg: '批次回拨成功',
        title: '提示',
      });
      reload();
    });
}

function reload() {
  pagingRef.value.reload();
}

/** 查看详情 */
function handleDetail(item: any) {
  uni.navigateTo({
    url: `/pages-org/terminal-manage/transfer-record/transfer-record-detail?batchNo=${item.batchNo}`,
  });
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  TerminalManageApi.terminalTransferBatchPage({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>

<style lang="scss" scoped>
.cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
