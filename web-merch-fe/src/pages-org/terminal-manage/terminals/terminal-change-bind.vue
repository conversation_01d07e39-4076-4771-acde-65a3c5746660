<template>
  <view>
    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-input
          v-model="form.newTerminalSn"
          prop="newTerminalSn"
          label="序列号"
          label-width="60px"
          placeholder="请输入或选择机器背后的条形码"
          clearable use-suffix-slot align-right
        >
          <template #suffix>
            <i
              class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
              @click="scanCode"
            />
          </template>
        </wd-input>
        <wd-cell />
      </wd-cell-group>
      <view class="mt40px px-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          换绑
        </wd-button>
      </view>
    </wd-form>

    <!-- H5扫码组件 -->
    <!-- #ifdef H5 -->
    <cshaptx4869-scancode
      v-if="showH5ScanCode"
      @success="onScanSuccess"
      @fail="onScanFail"
      @close="onScanClose"
    />
    <!-- #endif -->

    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { decodeUrlParams } from '@/utils';

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive({
  merchantNo: '', // 商户编号必填
  originTerminalSn: '', // 旧终端SN号必填
  newTerminalSn: '', // 新终端SN号必填
  channelCode: '',
});
// 规则
const rules: FormRules = {
  newTerminalSn: [{ required: true, message: '请输入序列号' }],
};

const showH5ScanCode = ref(false);

onLoad((query: any) => {
  query = decodeUrlParams(query || {});

  form.originTerminalSn = query.terminalSn || '';
  form.merchantNo = query.merchantNo || '';
  form.channelCode = query.channelCode || '';
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  await TerminalManageApi.changeBindTermSn(form);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

function scanCode() {
  // #ifdef H5
  showH5ScanCode.value = true;
  // #endif
  // #ifndef H5
  uni.scanCode({
    success: (res) => {
      form.newTerminalSn = res.result || '';
    },
  });
  // #endif
}

// #region h5扫码相关
function onScanSuccess(res: string) {
  showH5ScanCode.value = false;
  form.newTerminalSn = res || '';
}
function onScanFail(err: { errName: string;errMsg: string }) {
  uni.showModal({
    title: '提示',
    content: '浏览器不支持, 请您手动输入或切换浏览器重试哦~',
    showCancel: false,
    complete: () => {
      showH5ScanCode.value = false;
    },
  });
  console.error(err.errName, err.errMsg);
}
function onScanClose() {
  showH5ScanCode.value = false;
}
// #endregion
</script>
