<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <template #top>
        <view class="bg-white p-20rpx">
          <view class="rounded-lg bg-primary p-20rpx">
            <view class="flex items-center justify-between">
              <text>终端SN</text>
              <text>{{ routeParams.terminalSn }}</text>
            </view>
            <view class="mt-20rpx flex items-center justify-between">
              <text>终端型号</text>
              <text>{{ routeParams.modelName }}</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-lg bg-white p-20rpx"
        >
          <wd-cell-group custom-class="cell-group">
            <wd-cell title="订单类型" :value=" orderTypeMap[item.orderType] || '--'" />
            <wd-cell v-if="item.orderType === 2" title="扣费期数" :value="`${item.cycleDay}期`" />
            <wd-cell title="订单金额" :value="item.originPayAmt" />
            <wd-cell title="支付状态" :value="payStatusMap[item.payStatus] || '--'" />
            <wd-cell title="扣费发起时间" :value="item.validBeginTime || '--'" />
            <wd-cell title="支付完成时间" :value="item.payTime || '--'" />
            <wd-cell title="返现状态" :value="settleStatusMap[item.settleStatus] || '--'" />
            <wd-cell title="返现入账时间" :value="item.settleTime || '--'" />
          </wd-cell-group>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { decodeUrlParams } from '@/utils';

const pagingRef = ref();

const datasource = ref<any[]>([]);

const where = reactive({
  terminalSn: '',
});

const routeParams = ref<any>({});

const orderTypeMap: Record<number, string> = {
  1: '服务费',
  2: '通讯费',
};

const payStatusMap: Record<number, string> = {
  0: '初始化订单',
  1: '未支付',
  2: '已支付',
  3: '支付失败',
};

const settleStatusMap: Record<number, string> = {
  0: '未返现',
  1: '已返现',
};

onLoad((query) => {
  routeParams.value = decodeUrlParams(query || {});
  where.terminalSn = routeParams.value?.terminalSn || '';
});

function queryDataSource() {
  TerminalManageApi.terminalActivePayOrderPage(where)
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
