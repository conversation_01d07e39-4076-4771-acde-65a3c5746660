<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部搜索栏 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class" safe-area-inset-top
          @click-left="handlePageHome"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入机具SN号或商户编号" custom-class="w-full"
                @search="onSearchByKeywords"
              />
            </view>
          </template>
        </wd-navbar>

        <!-- 下拉查询 -->
        <view class="my-2px bg-white">
          <wd-drop-menu>
            <wd-drop-menu-item
              v-model="where.useStatus" :options="useStatusOptions" :[useStatusTitleProp]="'绑定状态'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.serviceFeeId" :options="serviceFeeIdOptions" :[serviceFeeIdTitleProp]="'激活政策'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.modelId" :options="modelIdOptions" :[modelIdTitleProp]="'型号'"
              @change="reload"
            />
          </wd-drop-menu>
        </view>

        <view class="gap-primary" />
      </template>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key" class="mb-30rpx rounded-xl bg-white shadow"
        >
          <view class="flex items-center rounded-t-xl bg-#f4f4f4 p-24rpx font-bold">
            <text class="text-15px">
              {{ item.terminalSn }}
              <text class="text-blue">
                ({{ item.modelName }})
              </text>
            </text>
            <i v-if="item.terminalSn" class="copy-icon" @click="copyData(item.terminalSn)" />
          </view>
          <view class="py-24rpx">
            <view class="px-24rpx">
              <view class="cell">
                <text class="cell-label">
                  所属代理商编号:
                </text>
                <view class="cell-value flex items-center">
                  {{ item.agentNo }}
                  <i v-if="item.agentNo" class="copy-icon" @click.stop="copyData(item.agentNo)" />
                </view>
              </view>
              <view class="cell">
                <text class="cell-label">
                  所属代理商简称:
                </text>
                <view class="cell-value flex items-center">
                  {{ item.agentSname }}
                  <i v-if="item.agentSname" class="copy-icon" @click.stop="copyData(item.agentSname)" />
                </view>
              </view>
              <template v-if="[1, 2].includes(item.useStatus)">
                <view class="cell">
                  <text class="cell-label">
                    商户编号:
                  </text>
                  <view class="cell-value flex items-center">
                    {{ item.merchantNo }}
                    <i v-if="item.merchantNo" class="copy-icon" @click.stop="copyData(item.merchantNo)" />
                  </view>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    商户名称:
                  </text>
                  <view class="cell-value flex items-center">
                    {{ item.merchantName }}
                    <i v-if="item.merchantName" class="copy-icon" @click.stop="copyData(item.merchantName)" />
                  </view>
                </view>
              </template>
              <template v-if="item.useStatus === 2">
                <view class="cell">
                  <text class="cell-label">
                    通道商户编号:
                  </text>
                  <view class="cell-value flex items-center">
                    {{ item.chnMerchNo }}
                    <i v-if="item.chnMerchNo" class="copy-icon" @click.stop="copyData(item.chnMerchNo)" />
                  </view>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    绑定时间:
                  </text>
                  <text class="cell-value">
                    {{ item.bindTime }}
                  </text>
                </view>
              </template>
            </view>

            <view class="mt-8rpx px-24rpx">
              <wd-tag custom-class="custom-tag-class" mark>
                {{ useStatusMap[item.useStatus] }}
              </wd-tag>
              <wd-tag custom-class="custom-tag-class" mark>
                {{ activateStatusMap[item.activateStatus] }}
              </wd-tag>
              <wd-tag custom-class="custom-tag-class" mark>
                {{ item.serviceFee }}元激活
              </wd-tag>
            </view>

            <!-- 操作按钮 -->
            <view class="mt-20rpx border border-t-#edf0f3 border-t-solid px-24rpx pt-10rpx">
              <!-- <wd-tag v-if="[0, 1].includes(item.useStatus)" custom-class="space" round @click="modifyRate(item)">
                修改费率
              </wd-tag> -->
              <wd-tag v-if="[0, 1].includes(item.useStatus)" custom-class="space" round @click="modifyActivity(item)">
                修改活动
              </wd-tag>
              <wd-tag v-if="item.useStatus === 2" custom-class="space" round @click="unbindTerminal(item)">
                解绑终端
              </wd-tag>
              <wd-tag v-if="item.useStatus === 2" custom-class="space" round @click="changeBindTerminal(item)">
                终端换绑
              </wd-tag>
              <wd-tag v-if="item.useStatus === 2" custom-class="space" round @click="viewEquityOrders(item)">
                查看权益订单
              </wd-tag>
              <wd-tag v-if="item.useStatus === 1" custom-class="space" round @click="recycleTerminal(item)">
                回收终端
              </wd-tag>
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { useClipboard } from '@/hooks';
import { NavigationHelper, buildUrlWithParams, deepClone } from '@/utils';

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 列表查询条件
const where = reactive<any>({
  terminalSn: '', // 搜索框（终端序列号 或 商户编号）
  useStatus: -1, // 绑定状态 0 闲置 1已分配 2已绑定
  modelId: -1, // 型号ID
  serviceFeeId: -1, // 激活政策id
});

// 列表数据
const datasource = ref<Record<string, any>[]>([]);

const useStatusOptions = [
  { label: '全部', value: -1 },
  { label: '闲置', value: 0 },
  { label: '已分配', value: 1 },
  { label: '已绑定', value: 2 },
];
const useStatusMap: Record<number, string> = {
  0: '闲置',
  1: '已分配',
  2: '已绑定',
};
const useStatusTitleProp = computed(() => {
  const prop = where.useStatus === -1 ? 'title' : '';
  return prop;
});

const serviceFeeIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const serviceFeeIdTitleProp = computed(() => {
  const prop = where.serviceFeeId === -1 ? 'title' : '';
  return prop;
});

const modelIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const modelIdTitleProp = computed(() => {
  const prop = where.modelId === -1 ? 'title' : '';
  return prop;
});

const activateStatusMap: any = {
  0: '未激活',
  1: '激活',
};

onMounted(() => {
  queryModels();
  queryServicePolicy();
});

onShow(() => {
  pagingRef.value?.reload();
});

/** 回收终端 */
async function recycleTerminal(item: any) {
  message
    .confirm({
      title: '提示',
      msg: `确定回收终端 “${item.terminalSn}” 吗？`,
    }).then(async () => {
      await TerminalManageApi.singleRecycleTerminal({ id: item.id });
      message.alert({
        msg: '终端回收成功',
        title: '提示',
      });
      reload();
    });
}

/** 修改费率 */
function modifyRate(item: any) {
  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminals/modify-rate', {
    terminalSn: item.terminalSn,
    channelCode: item.channelCode,
    policyId: item.policyId,
    rateInfo: item.rateInfo || '',
  });
}

/** 修改活动 */
function modifyActivity(item: any) {
  const params: any = {
    ids: [item.id],
    channelCode: item.channelCode,
    serviceFee: item.serviceFee,
    simFreeDay: item.simFreeDay,
    simSwitch: item.simSwitch,
    terminalSource: item.terminalSource,
  };
  const simFeePeriodSuffixKeys = ['SimFee', 'SimPeriodDay'];
  const filterSimFeePeriodKeys = Object.keys(item).filter(key => simFeePeriodSuffixKeys.some(suffix => key.endsWith(suffix)));
  filterSimFeePeriodKeys.forEach((key) => {
    params[key] = item[key];
  });

  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminals/modify-activity', params);
}

/** 查看权益订单 */
function viewEquityOrders(item: any) {
  const url = buildUrlWithParams('/pages-org/terminal-manage/terminals/equity-orders', { terminalSn: item.terminalSn, modelName: item.modelName });
  uni.navigateTo({ url });
}

/** 终端换绑 */
function changeBindTerminal(item: any) {
  uni.navigateTo({ url: buildUrlWithParams('/pages-org/terminal-manage/terminals/terminal-change-bind', { terminalSn: item.terminalSn, merchantNo: item.merchantNo, channelCode: item.channelCode }) });
}

/** 解绑终端 */
async function unbindTerminal(item: any) {
  message
    .confirm({
      title: '提示',
      msg: `确定解绑终端 “${item.terminalSn}” 吗？`,
    }).then(async () => {
      await TerminalManageApi.abortTermSn({ merchantNo: item.merchantNo, terminalSn: item.terminalSn });
      message.alert({
        msg: '终端解绑成功',
        title: '提示',
      });
      reload();
    });
}

/** 重载 */
function reload() {
  pagingRef.value.reload();
}

/** 搜索 */
function onSearchByKeywords({ value }: any) {
  where.terminalSn = value;
  pagingRef.value?.reload();
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  const whereMap = deepClone(where);
  whereMap.useStatus = whereMap.useStatus === -1 ? null : whereMap.useStatus;
  whereMap.modelId = whereMap.modelId === -1 ? null : whereMap.modelId;
  whereMap.serviceFeeId = whereMap.serviceFeeId === -1 ? null : whereMap.serviceFeeId;

  TerminalManageApi.terminalInfoPage({ ...whereMap, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

async function queryModels() {
  let data = await TerminalManageApi.terminalModelList({});
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.modelName,
      value: item.id,
    };
  });
  modelIdOptions.value = [modelIdOptions.value[0], ...formatData];
}

async function queryServicePolicy() {
  let data = await TerminalManageApi.serviceFeePolicySelfOpenList();
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.policyName,
      value: item.configId,
    };
  });

  serviceFeeIdOptions.value = [serviceFeeIdOptions.value[0], ...formatData];
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

function handlePageHome() {
  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell {
  @apply flex items-center;

  &:not(:last-child) {
    @apply mb-8rpx
  }

  .cell-label {
    @apply shrink-0;
  }

  .cell-value {
    @apply grow ml-20rpx flex items-center text-#333;
  }
}

:deep(.custom-tag-class) {
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-navbar-class) {
  padding-top: 8px;

  .wd-navbar__title {
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.lr-border {
  position: relative;
  overflow: hidden;

  &::before,
  &::after {
    position: absolute;
    top: 0;
    width: 1px;
    height: 60%;
    background: #e6e6e6;
    content: '';
    transform: translateY(50%);
  }

  &::before {
    left: 0;

  }

  &::after {
    right: 0;
  }

}

.copy-icon {
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}

:deep(){
.space {
  margin: 16rpx 16rpx 0 0 !important;
}
}
</style>
