<template>
  <view class="h-full overflow-y-scroll">
    <view class="gap-primary" />

    <view class="p-15px">
      <view class="mt-15px font-medium">
        请选择终端下发类型:
      </view>

      <wd-radio-group v-model="terminalType" custom-class="custom-radio-group-class">
        <wd-radio v-for="(item, key) in terminalTypeOptions" :key="key" :value="item.value">
          <view class="flex items-center">
            <text class="text-28rpx font-medium">
              {{ item.label }}
            </text>
          </view>
        </wd-radio>
      </wd-radio-group>

      <view class="mt-50px">
        <wd-button type="primary" size="large" block @click="handleNext">
          下一步
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const terminalTypeOptions = [
  {
    label: '普通终端下发',
    value: 0,
  },
  {
    label: '活动终端下发',
    value: 1,
  },
];

const terminalType = ref<number>(0);

function handleNext() {
  uni.navigateTo({
    url: `/pages-org/terminal-manage/terminal-delivery/index?terminalType=${terminalType.value}`,
  });
}
</script>

<style lang="scss" scoped>
:deep(.custom-radio-group-class) {
  .wd-radio{
    padding: 50rpx 40rpx;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .is-checked{
    background-color: #edf6ff;
    border-color:transparent
  }

  .wd-radio__label{
    width: 100%;
  }

  .wd-radio__shape{
    background-color: transparent;
  }
}
</style>
