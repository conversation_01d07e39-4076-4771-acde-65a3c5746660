<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="mt-10px">
      <wd-form ref="formRef" :model="form">
        <wd-cell-group title="激活奖励配置" border>
          <wd-cell title="激活奖励配置" center custom-class="custom-switch-cell-class">
            <wd-switch v-model="switchChecked" size="24px" disabled />
          </wd-cell>
          <wd-cell-group v-for="(item, key) in form.activeRuleList" :key="key" custom-class="custom-policy-cell">
            <wd-cell title="激活奖励" :prop="`activeRuleList.${key}.cashbackAmount`" :rules="makeAmountRules(item.parentCashbackAmount)" center>
              <wd-input v-model="item.cashbackAmount" type="digit" placeholder="请输入" /> <text>元</text>
            </wd-cell>

            <view class="tips-class">
              <text>
                说明: 可设置金额低于或等于{{ item.parentCashbackAmount }}元
              </text>
              <text class="mt-5px">
                条件: 绑机后{{ item.timeCycleStart }}-{{ item.timeCycleEnd }}天, 累计交易满{{ item.tradeVolume }}元
              </text>
            </view>
          </wd-cell-group>
        </wd-cell-group>

        <view class="gap-primary" />

        <wd-cell-group title="达标奖励配置" border>
          <wd-cell title="达标奖励配置" center custom-class="custom-switch-cell-class">
            <wd-switch v-model="switchChecked" size="24px" disabled />
          </wd-cell>
          <wd-cell-group v-for="(item, key) in form.cashbackRuleList" :key="key" custom-class="custom-policy-cell">
            <wd-cell title="达标奖励" :prop="`cashbackRuleList.${key}.cashbackAmount`" :rules="makeAmountRules(item.parentCashbackAmount)" center>
              <wd-input v-model="item.cashbackAmount" type="digit" placeholder="请输入" /> <text>元</text>
            </wd-cell>

            <view class="tips-class">
              <text>
                说明: 可设置金额低于或等于{{ item.parentCashbackAmount }}元
              </text>
              <text class="mt-5px">
                条件: 绑机后{{ item.timeCycleStart }}-{{ item.timeCycleEnd }}天, 累计交易满{{ item.tradeVolume }}元
              </text>
            </view>
          </wd-cell-group>
        </wd-cell-group>

        <view class="mt-40rpx px-40rpx">
          <wd-button type="primary" size="large" block @click="save">
            提交
          </wd-button>
        </view>
      </wd-form>
    </view>

    <!-- 挂载点 -->
    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule } from 'wot-design-uni/components/wd-form/types';
import { useMessage } from 'wot-design-uni';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { NavigationHelper, deepClone } from '@/utils';

const message = useMessage();

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive<any>({
  activeRuleList: [],
  cashbackRuleList: [],
});

const switchChecked = ref<boolean>(true);

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    const query = deepClone(transferredData);
    const { activityInfo } = query;

    form.targAgentNo = query.targAgentNo;
    form.terminalSnList = query.terminalSnList;
    form.termActiveSwitch = query.termActiveSwitch;
    form.channelCode = query.channelCode;

    form.activePolicyNo = activityInfo.activePolicyNo;
    form.cashbackPolicyNo = activityInfo.cashbackPolicyNo;

    form.activeRuleList = activityInfo.activePolicyRuleList || [];
    form.cashbackRuleList = activityInfo.cashPolicyRuleList || [];
  }
});

/**
 * 生成金额校验规则
 */
function makeAmountRules(maxValue: number): FormItemRule[] {
  return [
    {
      required: true,
      message: '请填写金额',
      validator: (value: number) => {
        if (Number(value) > Number(maxValue)) {
          return Promise.reject('Exceed the limit');
        }
        return Promise.resolve();
      },
    },
  ];
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  await TerminalManageApi.transferTerminal(form);

  message.alert({
    msg: '操作成功',
    title: '提示',
  }).then(() => {
    // 跳转下发记录
    uni.navigateTo({ url: '/pages-org/terminal-manage/transfer-record/index' });
  });
}
</script>

<style lang="scss" scoped>
:deep(.custom-switch-cell-class) {
  background-color: #f8f8f8 !important;
}

:deep(.custom-policy-cell){
margin-bottom: 5px;

  .wd-cell__wrapper{
    justify-content: flex-start;
  }

  .wd-cell__value{
    display: flex;
    align-items: center;
  }

  .wd-cell__left,.wd-cell__right{
    max-width: 100%;
    flex: none;
  }

  .wd-cell__error-message{
    display: none;
  }

  .wd-input__body{
    margin-right: 4px;
    width: 100px;
    text-align: center;
    flex: none;
    flex-shrink: 0;
  }

  .tips-class{
    display: flex;
    padding: 10px 15px;
    font-size: 13px;
    color: rgb(0 0 0 / 45%);
    flex-direction: column;
    border-top: 1px solid #dadada;
  }

}
</style>
