<template>
  <view class="h-full overflow-scroll bg-primary">
    <wd-form ref="formRef" :model="form" :rules="rules">
      <!-- <view class="my-8px">
        <wd-picker
          v-model="form.channelCode"
          prop="channelCode"
          label="支付通道"
          label-width="100px"
          placeholder="请先选择支付通道"
          :columns="channelColumns"
          align-right
          @confirm="onConfirmChannel"
        />
      </view> -->

      <wd-cell-group title="选择政策" border>
        <wd-cell title-width="0" custom-class="custom-radios-cell" prop="policyId">
          <wd-radio-group v-model="form.policyId" shape="dot" @change="onChangePolicyId">
            <wd-radio v-for="(item, key) in ratePolicyList" :key="key" :value="item.id">
              {{ item.policyDesc }}
            </wd-radio>
          </wd-radio-group>
        </wd-cell>
      </wd-cell-group>
    </wd-form>

    <!-- 费率表单 -->
    <wd-form ref="rateFormRef" :model="rateForm">
      <view class="mb-8px mt-10px">
        <wd-cell-group title="费率信息" />
      </view>

      <wd-cell-group border custom-class="custom-rate-cell">
        <wd-cell title="信用卡费率" prop="rateInfoDTO.creditRate" :rules="makeRateRules(rateFormUpperLimit.creditRate)" center>
          <wd-input v-model="rateForm.rateInfoDTO.creditRate" type="digit" placeholder="请输入" /> <text>%</text>
        </wd-cell>
        <wd-cell :label="`设置范围: 不得高于${rateFormUpperLimit.creditRate || '--'}%`" />
      </wd-cell-group>

      <wd-cell-group border custom-class="custom-rate-cell">
        <wd-cell title="借记卡费率" prop="rateInfoDTO.debitRate" :rules="makeRateRules(rateFormUpperLimit.debitRate)" center>
          <wd-input v-model="rateForm.rateInfoDTO.debitRate" type="digit" placeholder="请输入" /> <text>%</text>
        </wd-cell>
        <wd-cell :label="`设置范围: 不得高于${rateFormUpperLimit.debitRate || '--'}%`" />
      </wd-cell-group>

      <wd-cell-group border custom-class="custom-rate-cell">
        <wd-cell title="借记卡封顶" prop="rateInfoDTO.debitFeeMax" :rules="makeRateRules(rateFormUpperLimit.debitFeeMax)" center>
          <wd-input v-model="rateForm.rateInfoDTO.debitFeeMax" type="number" placeholder="请输入" /> <text>元</text>
        </wd-cell>
        <wd-cell :label="`设置范围: 不得高于${rateFormUpperLimit.debitFeeMax || '--'}元`" />
      </wd-cell-group>

      <wd-cell-group border custom-class="custom-rate-cell">
        <wd-cell title="扫码费率" prop="rateInfoDTO.wechatRate" :rules="makeRateRules(rateFormUpperLimit.wechatRate)" center>
          <wd-input v-model="rateForm.rateInfoDTO.wechatRate" type="digit" placeholder="请输入" /> <text>%</text>
        </wd-cell>
        <wd-cell :label="`设置范围: 不得高于${rateFormUpperLimit.wechatRate || '--'}%`" />
      </wd-cell-group>

      <wd-cell-group border custom-class="custom-rate-cell">
        <wd-cell title="贷记卡D0附加单笔" prop="rateInfoDTO.creditQrD0SingleFee" :rules="makeRateRules(rateFormUpperLimit.creditQrD0SingleFee)" center>
          <wd-input v-model="rateForm.rateInfoDTO.creditQrD0SingleFee" type="number" placeholder="请输入" /> <text>元</text>
        </wd-cell>
        <wd-cell :label="`设置范围: 不得高于${rateFormUpperLimit.creditQrD0SingleFee || '--'}元`" />
      </wd-cell-group>

      <wd-cell-group border custom-class="custom-rate-cell">
        <wd-cell title="借记卡D0附加单笔" prop="rateInfoDTO.debitQrD0SingleFee" :rules="makeRateRules(rateFormUpperLimit.debitQrD0SingleFee)" center>
          <wd-input v-model="rateForm.rateInfoDTO.debitQrD0SingleFee" type="number" placeholder="请输入" /> <text>元</text>
        </wd-cell>
        <wd-cell :label="`设置范围: 不得高于${rateFormUpperLimit.debitQrD0SingleFee || '--'}元`" />
      </wd-cell-group>
    </wd-form>

    <view class="my-40px px-40rpx">
      <wd-button type="primary" size="large" block @click="save">
        确定
      </wd-button>
    </view>

    <!-- 挂载点 -->
    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { NavigationHelper, deepClone } from '@/utils';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive<any>({
  terminalSnList: [],
});

// 规则
const rules: FormRules = {
  // channelCode: [{ required: true, message: '请选择通道' }],
};

// 费率表单
const rateFormRef = ref<FormInstance | null>(null);
const rateForm = reactive<any>({
  rateInfoDTO: {},
});

// 费率上限
const rateFormUpperLimit = ref<any>({});

const channelColumns = ref<any>([]);

const ratePolicyList = ref<any>([]);

// 原始费率 用于对比
const originalRateForm = ref<any>({});

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    form.channelCode = transferredData.channelCode;
    form.terminalSnList = transferredData.terminalSnList;
  }

  // getChannelList();

  getOwnerRateList();
  getRatePolicylistOfOrg();
});

function onConfirmChannel() {
  form.policyId = null;
  rateForm.rateInfoDTO = {};

  getOwnerRateList();
  getRatePolicylistOfOrg();
}

async function getChannelList() {
  let data = await CommonApi.getChannelList();
  data = data || [];

  const formatData = data.map((item: any) => ({
    label: item.channelName,
    value: item.channelCode,
  }));
  channelColumns.value = formatData;
}

/**
 * 切换政策
 */
function onChangePolicyId({ value: id }: { value: string }) {
  const policyItem = ratePolicyList.value.find((item: any) => item.id === id);
  form.policyNo = policyItem?.policyNo;

  const rateItem = policyItem?.rateDTOList[0] || {};
  rateItem.rateInfoDTO = rateItem.rateInfoDTO || {};

  Object.assign(rateForm, deepClone(rateItem));

  originalRateForm.value = deepClone(rateItem);
  originalRateForm.value.isSame = 1;
}

/**
 * 获取费率政策
 */
async function getRatePolicylistOfOrg() {
  let data = await RatePolicyApi.getRatePolicylistOfOrg({
    policyType: 2, // 政策类型  2-商户费率政策 (必填)
    channelCode: form.channelCode, // 通道编码 （policyType = 2时，必填）
  });

  data = data || [];

  const filterData = data.filter((item: any) => {
    // 过滤费率类型为1的
    const filterRateDTOListByRateType = item.rateDTOList?.filter((rate: any) => rate.rateType === 1);
    if (!filterRateDTOListByRateType?.length) {
      return false;
    }
    item.rateDTOList = filterRateDTOListByRateType;
    return true;
  });

  ratePolicyList.value = filterData || [];
}

/**
 * 生成费率校验规则
 */
function makeRateRules(maxValue: number): FormItemRule[] {
  return [
    {
      required: true,
      message: '必填',
      validator: (value: number) => {
        if (Number(value) > Number(maxValue)) {
          return Promise.reject('超出上限');
        }
        return Promise.resolve();
      },
    },
  ];
}

/**
 * 获取费率阈值
 */
async function getOwnerRateList() {
  const data = await TerminalManageApi.ownerRateList({
    channelCode: form.channelCode,
  });

  const sysTempList = data?.sysTempList || [];

  // 费率上限
  const upperLimitItem = sysTempList.find((item: any) => item.limitType === 2 && item.rateType === 1);
  rateFormUpperLimit.value = upperLimitItem?.rateInfoDTO || {};
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  if (!form.policyId) {
    toast.warning({
      msg: '请选择政策',
    });
    return;
  }

  // 检验费率表单
  const { valid: rateValid, errors: rateErrors } = await rateFormRef.value!.validate();
  if (!rateValid)
    return Promise.reject(rateErrors);

  if (originalRateForm.value.isSame === 1) {
    // 对比费率是否变化
    const isSameBool = JSON.stringify(rateForm.rateInfoDTO) === JSON.stringify(originalRateForm.value.rateInfoDTO);
    rateForm.isSame = isSameBool ? 1 : 0;
  }

  const params: any = {
    ...form,
    rateInfo: { ...rateForm },
  };

  await TerminalManageApi.modifyPolicyRateInfo(params);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}
</script>

<style lang="scss" scoped>
:deep(.custom-radios-cell) {
  padding-left: 0;

  .wd-cell__value{
    text-align: left;
  }

}

:deep(.custom-rate-cell){
  margin-bottom: 5px;

  .wd-cell__wrapper{
    justify-content: flex-start;
  }

  .wd-cell__label{
    font-size: 14px;
  }

  .wd-cell__value{
    display: flex;
    align-items: center;
  }

  .wd-cell__left,.wd-cell__right{
    max-width: 100%;
    flex: none;
  }

  .wd-cell__error-message{
    width: 70px;
    text-align: center;
  }

  .wd-input__body{
    margin-right: 4px;
     width: 66px;
    text-align: center;
    flex: none;
    flex-shrink: 0;
  }

}
</style>
