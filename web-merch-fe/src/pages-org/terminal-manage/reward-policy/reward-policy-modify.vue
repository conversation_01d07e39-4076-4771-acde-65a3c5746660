<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="mt-10px">
      <wd-form ref="formRef" :model="form">
        <wd-cell-group border>
          <wd-cell
            title="选择政策类型" title-width="100px"
            is-link
          >
            <wd-textarea
              :model-value="cashbackTypeMap[form.cashbackType]"
              placeholder="政策类型"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
          <wd-cell
            title="选择开通政策" title-width="100px"
            is-link
          >
            <wd-textarea
              v-model="form.policyName"
              placeholder="开通政策"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
        </wd-cell-group>

        <view class="mt-10px">
          <wd-cell-group v-for="(item, key) in form.rulePolicyList || []" :key="key" custom-class="custom-policy-cell">
            <wd-cell :title="form.cashbackType === 0 ? '激活奖励' : '达标奖励'" :prop="`rulePolicyList.${key}.cashbackAmount`" :rules="makeAmountRules(item.parentCashbackAmount)" center>
              <wd-input v-model="item.cashbackAmount" type="digit" placeholder="请输入" /> <text>元</text>
            </wd-cell>

            <view class="tips-class">
              <text>
                说明: 可设置金额低于或等于{{ item.parentCashbackAmount }}元
              </text>
              <text class="mt-5px">
                条件: 绑机后{{ item.timeCycleStart }}-{{ item.timeCycleEnd }}天, 累计交易满{{ item.tradeVolume }}元
              </text>
            </view>
          </wd-cell-group>
        </view>

        <view class="mt-40rpx px-40rpx">
          <wd-button type="primary" size="large" block @click="save">
            提交
          </wd-button>
        </view>
      </wd-form>
    </view>

    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { CashbackPolicyApi } from '@/api-org/cashback-policy';
import { NavigationHelper } from '@/utils';
import { useUserStore } from '@/store';

const toast = useToast();

const loginUser = computed(() => useUserStore().info);

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive<any>({
  cashbackType: '', // 返现政策类型 必填
  cashCalculateType: '', // 达标计算方式 cashbackType=1时，必填 0-金额优先 1-时间优先

  policyNo: '', // 政策编号 必填
  orgNoList: [], // 机构编号列表 必填
  rulePolicyList: [], // 规则列表
});

const cashbackTypeMap: any = {
  0: '激活奖励政策',
  1: '达标奖励政策',
};

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    Object.assign(form, transferredData);
  }

  findPolicyListByPolicyNo();
});

async function findPolicyListByPolicyNo() {
  Promise.allSettled([
    // 查询下级反显
    CashbackPolicyApi.findListByPolicyNo({ policyNo: form.policyNo, cashbackType: form.cashbackType, orgNo: form.orgNo }),
    // 查询自身设置金额上限
    CashbackPolicyApi.findListByPolicyNo({ policyNo: form.policyNo, cashbackType: form.cashbackType, orgNo: loginUser.value.orgCode }),
  ]).then(([{ value: subData }, { value: selfData }]: any) => {
    subData = subData || [];
    selfData = selfData || [];

    selfData.forEach((selfItem: any) => {
      subData.forEach((subItem: any) => {
        if (subItem.ruleNo === selfItem.ruleNo) {
          subItem.parentCashbackAmount = selfItem.cashbackAmount;
        }
      });
    });

    form.rulePolicyList = subData || [];
  });
}
/**
 * 生成金额校验规则
 */
function makeAmountRules(maxValue: number): FormItemRule[] {
  return [
    {
      required: true,
      message: '必填',
      validator: (value: number) => {
        if (Number(value) > Number(maxValue)) {
          return Promise.reject('超出上限');
        }
        return Promise.resolve();
      },
    },
  ];
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  form.orgNoList = [form.orgNo];
  await CashbackPolicyApi.subAgentEditCashbackRulePolicy(form);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}
</script>

<style lang="scss" scoped>
:deep(.custom-policy-cell){
margin-bottom: 5px;

  .wd-cell__wrapper{
    justify-content: flex-start;
  }

  .wd-cell__value{
    display: flex;
    align-items: center;
  }

  .wd-cell__left,.wd-cell__right{
    max-width: 100%;
    flex: none;
  }

  .wd-cell__error-message{
    width: 104px;
    text-align: center;
  }

  .wd-input__body{
    margin-right: 4px;
    width: 100px;
    text-align: center;
    flex: none;
    flex-shrink: 0;
  }

  .tips-class{
    display: flex;
    padding: 10px 15px;
    font-size: 13px;
    color: rgb(0 0 0 / 45%);
    flex-direction: column;
    border-top: 1px solid #dadada;
  }

}
</style>
