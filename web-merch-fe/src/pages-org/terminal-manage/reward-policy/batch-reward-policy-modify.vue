<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="mt-10px">
      <wd-form ref="formRef" :model="form">
        <wd-cell-group border>
          <wd-picker
            v-model="form.cashbackType"
            prop="cashbackType"
            label="选择政策类型"
            label-width="100px"
            placeholder="政策类型"
            :columns="cashbackTypeColumns"
            align-right
            @confirm="onConfirmCashbackType"
          />
          <wd-picker
            v-model="form.policyNo"
            prop="policyNo"
            label="选择开通政策"
            label-width="100px"
            placeholder="请选择"
            :columns="policyNoColumns"
            align-right
            @open="onOpenPolicyNoPicker"
            @confirm="onConfirmPolicyNo"
          />
        </wd-cell-group>

        <view class="mt-10px">
          <wd-cell-group v-for="(item, key) in form.rulePolicyList || []" :key="key" custom-class="custom-policy-cell">
            <wd-cell :title="form.cashbackType === 0 ? '激活奖励' : '达标奖励'" :prop="`rulePolicyList.${key}.cashbackAmount`" :rules="makeAmountRules(item.parentCashbackAmount)" center>
              <wd-input v-model="item.cashbackAmount" type="digit" placeholder="请输入" /> <text>元</text>
            </wd-cell>

            <view class="tips-class">
              <text>
                说明: 可设置金额低于或等于{{ item.parentCashbackAmount }}元
              </text>
              <text class="mt-5px">
                条件: 绑机后{{ item.timeCycleStart }}-{{ item.timeCycleEnd }}天, 累计交易满{{ item.tradeVolume }}元
              </text>
            </view>
          </wd-cell-group>
        </view>

        <view class="mt-40rpx px-40rpx">
          <wd-button type="primary" size="large" block @click="save">
            选择要{{ isBatchOpen === 1 ? '开通' : '编辑' }}的机构
          </wd-button>
        </view>
      </wd-form>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule } from 'wot-design-uni/components/wd-form/types';
import { View } from 'XrFrame/kanata/lib/frontend';
import { CashbackPolicyApi } from '@/api-org/cashback-policy';
import { useUserStore } from '@/store';
import { NavigationHelper } from '@/utils';

const loginUser = computed(() => useUserStore().info);

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive<any>({
  cashbackType: 0, // 返现政策类型 必填
  policyNo: '', // 政策编号 必填
  rulePolicyList: [], // 规则列表
});

// 0 批量编辑; 1 批量开通
const isBatchOpen = ref<any>(0);

const cashbackTypeColumns: any = [
  { label: '激活奖励政策', value: 0 },
  { label: '达标奖励政策', value: 1 },
];

const policyNoColumns: any = ref<any>([]);

onLoad((query: any) => {
  isBatchOpen.value = query.isBatchOpen ? Number(query.isBatchOpen) : 0;
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: isBatchOpen.value === 1 ? '批量开通政策' : '批量编辑政策',
  });
});

function onConfirmCashbackType() {
  form.policyNo = '';
  form.rulePolicyList = [];
}

function onOpenPolicyNoPicker() {
  findPolicyNameList();
}

function onConfirmPolicyNo() {
  findPolicyListByPolicyNo();
}

async function findPolicyListByPolicyNo() {
  Promise.allSettled([
    // 查询自身反显并设置金额上限
    CashbackPolicyApi.findListByPolicyNo({ policyNo: form.policyNo, cashbackType: form.cashbackType, orgNo: loginUser.value.orgCode }),
  ]).then(([{ value: selfData }]: any) => {
    selfData = selfData || [];

    selfData.forEach((selfItem: any) => {
      selfItem.parentCashbackAmount = selfItem.cashbackAmount;
      selfItem.cashbackAmount = '';
    });

    form.rulePolicyList = selfData;
  });
}

async function findPolicyNameList() {
  policyNoColumns.value = [];

  let data = await CashbackPolicyApi.findPolicyNameList({ cashbackType: form.cashbackType, orgNo: loginUser.value.orgCode });
  data = data || [];
  const formatData = data.map((item: any) => ({
    label: item.policyName,
    value: item.policyNo,
  }));

  policyNoColumns.value = formatData;
}

/**
 * 生成金额校验规则
 */
function makeAmountRules(maxValue: number): FormItemRule[] {
  return [
    {
      required: true,
      message: '必填',
      validator: (value: number) => {
        if (Number(value) > Number(maxValue)) {
          return Promise.reject('超出上限');
        }
        return Promise.resolve();
      },
    },
  ];
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  NavigationHelper.redirectToWithData('/pages-org/terminal-manage/reward-policy/select-org-list', {
    cashbackType: form.cashbackType,
    cashCalculateType: form.cashCalculateType,
    policyNo: form.policyNo,
    rulePolicyList: form.rulePolicyList,
    isBatchOpen: isBatchOpen.value,
    fetchType: isBatchOpen.value === 1 ? 0 : 1,
  });
}
</script>

<style lang="scss" scoped>
:deep(.custom-policy-cell){
margin-bottom: 5px;

  .wd-cell__wrapper{
    justify-content: flex-start;
  }

  .wd-cell__value{
    display: flex;
    align-items: center;
  }

  .wd-cell__left,.wd-cell__right{
    max-width: 100%;
    flex: none;
  }

  .wd-cell__error-message{
    width: 104px;
    text-align: center;
  }

  .wd-input__body{
    margin-right: 4px;
    width: 100px;
    text-align: center;
    flex: none;
    flex-shrink: 0;
  }

  .tips-class{
    display: flex;
    padding: 10px 15px;
    font-size: 13px;
    color: rgb(0 0 0 / 45%);
    flex-direction: column;
    border-top: 1px solid #dadada;
  }

}
</style>
