<template>
  <view class="h-full overflow-hidden bg-primary">
    <view class="h-full flex flex-col">
      <!-- 顶部固定 -->
      <view class="shrink-0">
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class" safe-area-inset-top
          @click-left="handlePageBack"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入下级编号搜索" custom-class="w-full"
                @search="onSearchByOrgNo"
              />
            </view>
          </template>
        </wd-navbar>

        <wd-tabs v-model="where.cashbackType" @change="onChangeCashbackType">
          <wd-tab title="激活奖励政策" />
          <wd-tab title="达标奖励政策" />
        </wd-tabs>
      </view>
      <view class="grow">
        <page-paging ref="pagingRef" v-model="datasource" :fixed="false" @query="queryDataSource">
          <!-- 头部 -->
          <template #top>
            <view class="mt-1px bg-white">
              <wd-drop-menu custom-class="flex">
                <wd-drop-menu-item
                  v-if="where.cashbackType === 0"
                  v-model="where.policyNo" :options="policyNoOptions" :[policyNoTitleProp]="'政策名称'"
                  @change="reload"
                />
                <wd-drop-menu-item
                  v-else-if="where.cashbackType === 1"
                  v-model="where.policyNo" :options="policyNoOptions" :[policyNoTitleProp]="'政策名称'"
                  @change="reload"
                />
              </wd-drop-menu>
            </view>
          </template>

          <!-- 列表 -->
          <view class="p-20rpx">
            <view v-for="(item, key) in datasource" :key="key" class="mb-20rpx rounded-lg bg-white py-20rpx">
              <view class="px-20rpx">
                <wd-cell-group custom-class="cell-group">
                  <wd-cell title="政策名称" :value="item.policyName" />
                  <wd-cell title="政策编号" :value="item.policyNo" />
                  <wd-cell title="机构编号" :value="item.orgNo" />
                  <wd-cell title="创建时间" :value="item.createTime" />
                </wd-cell-group>
              </view>

              <view class="mt-20rpx flex justify-end border border-#f4f4f4 border-t-solid pt-20rpx">
                <wd-button type="info" size="small" plain hairline @click="handleEdit(item)">
                  编辑奖励政策
                </wd-button>
              </view>
            </view>
          </view>

          <!-- 底部 -->
          <template #bottom>
            <view class="pb-40rpx pt-20rpx">
              <view class="flex justify-between">
                <wd-button type="info" :round="false" @click="handleBatchEdit">
                  批量编辑
                </wd-button>
                <wd-button :round="false" @click="handleBatchOpen">
                  批量开通
                </wd-button>
              </view>
            </view>
          </template>
        </page-paging>
      </view>
    </view>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { NavigationHelper, deepClone } from '@/utils';
import { useUserStore } from '@/store';
import { CashbackPolicyApi } from '@/api-org/cashback-policy';

const loginUser = computed(() => useUserStore().info);

// 分页器ref
const pagingRef = ref();

// 查询条件
const where = reactive<any>({
  cashbackType: 0, // 0-激活返现政策 1-达标返现政策
  policyNo: -1, // 政策编号 选填
  orgNo: '', // 机构编号 选填
});

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const policyNoOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const policyNoTitleProp = computed(() => {
  const prop = where.policyNo === -1 ? 'title' : '';
  return prop;
});

onLoad(() => {
  findPolicyNameList();
});

onShow(() => {
  pagingRef.value?.reload();
});

function onChangeCashbackType({ index }: any) {
  where.policyNo = -1;
  where.cashbackType = index;

  findPolicyNameList();

  reload();
}

function handleEdit(item: any) {
  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/reward-policy/reward-policy-modify', { ...item });
}

function handleBatchEdit() {
  uni.navigateTo({
    url: '/pages-org/terminal-manage/reward-policy/batch-reward-policy-modify',
  });
}

function handleBatchOpen() {
  uni.navigateTo({
    url: '/pages-org/terminal-manage/reward-policy/batch-reward-policy-modify?isBatchOpen=1',
  });
}

async function findPolicyNameList() {
  let data = await CashbackPolicyApi.findPolicyNameList({ cashbackType: where.cashbackType, orgNo: loginUser.value.orgCode });
  data = data || [];
  const formatData = data.map((item: any) => ({
    label: item.policyName,
    value: item.policyNo,
  }));

  policyNoOptions.value = [policyNoOptions.value[0], ...formatData];
}

/** 搜索数据 */
function onSearchByOrgNo({ value }: any) {
  where.orgNo = value;
  reload();
}

function reload() {
  pagingRef.value?.reload();
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);

  formatWhere.policyNo = formatWhere.policyNo === -1 ? null : formatWhere.policyNo;

  CashbackPolicyApi.cashbackRulePolicyPage({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

function handlePageBack() {
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.copy-icon{
  @apply i-mdi-content-copy ml--5px size-28rpx text-#b51e1e
}
</style>
