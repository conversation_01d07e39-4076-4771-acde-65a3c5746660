<template>
  <view class="h-full overflow-hidden bg-primary">
    <page-paging ref="pagingRef" refresher-only @on-refresh="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <wd-search
          hide-cancel placeholder-left placeholder="请输入机构编号搜索" custom-class="w-full"
          @search="onSearchByOrgNo"
        />
      </template>

      <!-- 列表 -->
      <view class="p-20rpx">
        <wd-checkbox-group v-model="form.orgNoList" cell border>
          <view v-for="(item, key) in datasource" :key="key" class="mb-20rpx rounded-lg bg-white">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="请选择" center>
                <view class="flex items-center justify-end">
                  <wd-checkbox :model-value="item.agentNo" />
                </view>
              </wd-cell>
              <wd-cell title="机构名称" :value="item.agentName" />
              <wd-cell title="机构编号" :value="item.agentNo" />
            </wd-cell-group>
          </view>
        </wd-checkbox-group>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="bg-white px-30rpx pb-40rpx pt-20rpx">
          <view class="flex items-center">
            <view class="w-70%">
              <wd-checkbox v-model="isCheckedAll">
                全选
              </wd-checkbox>
            </view>
            <view class="w-30% flex items-center">
              <wd-button size="small" @click="save">
                确认({{ form.orgNoList.length }})
              </wd-button>
            </view>
          </view>
        </view>
      </template>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { NavigationHelper } from '@/utils';
import { CashbackPolicyApi } from '@/api-org/cashback-policy';

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 列表查询条件
const where = reactive<any>({
  cashbackType: null,
  // 0-开通/补开通调用直属下级 1-其他
  fetchType: null,
  policyNo: null,
  orgNo: '',
});

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const form = reactive<any>({
  orgNoList: [],
});

// 全选/反选
const isCheckedAll = computed({
  get() {
    return form.orgNoList.length && form.orgNoList.length === datasource.value.length;
  },
  set(val) {
    if (val) {
      form.orgNoList = datasource.value.map((item: any) => item.agentNo);
    }
    else {
      form.orgNoList = [];
    }
  },
});

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    Object.assign(form, transferredData);

    where.cashbackType = transferredData.cashbackType;
    where.fetchType = transferredData.fetchType;
    where.policyNo = transferredData.policyNo;
  }

  queryDataSource();
});

async function save() {
  if (!form.orgNoList.length) {
    message.alert({
      msg: '请选择机构',
      title: '提示',
    });
    return;
  }

  if (form.isBatchOpen === 0) {
    await CashbackPolicyApi.subAgentEditCashbackRulePolicy(form);
  }
  else if (form.isBatchOpen === 1) {
    await CashbackPolicyApi.subAgentOpen(form);
  }

  message.alert({
    msg: '操作成功',
    title: '提示',
  }).then(() => {
    uni.navigateBack();
  });
}

/** 搜索数据 */
function onSearchByOrgNo({ value }: any) {
  where.orgNo = value;
  reload();
}

function reload() {
  form.orgNoList = [];
  queryDataSource();
}

/** 查询数据 */
function queryDataSource() {
  CashbackPolicyApi.getDirectAgentOpenSt({ ...where })
    .then((res: any) => {
      let data = res || [];

      const openStatus = where.fetchType !== 0;
      data = data.filter((item: any) => item.openSt === openStatus);
      datasource.value = data;

      pagingRef.value?.complete();
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox-group){
  background-color: transparent !important;
}

:deep(.cell-group) {
  background-color: transparent !important;

  .wd-cell-group,.wd-cell,.wd-cell-group__body{
    background-color: transparent !important;
  }

  .wd-checkbox.is-cell-box{
    padding: 0;
  }
}
</style>
