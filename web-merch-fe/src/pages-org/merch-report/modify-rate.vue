<template>
  <view class="h-full flex flex-col">
    <view class="shrink-0">
      <view class="gap-primary" />
      <wd-form ref="formRef" :model="form" :rules="rules">
        <wd-cell-group border>
          <wd-input v-if="form.policyNameDesc" v-model="form.policyNameDesc" label="政策名称" placeholder="结算政策名称" label-width="80px" readonly />
          <!-- 渠道复选框 -->
          <wd-cell title="开通渠道" title-width="80px" center>
            <wd-checkbox-group v-model="checkedChannels" custom-class="flex items-center flex-wrap" disabled inline>
              <wd-checkbox v-for="(item, key) in channelOptions" :key="key" :model-value="item.channelCode">
                {{ item.channelName }}
              </wd-checkbox>
            </wd-checkbox-group>
          </wd-cell>
        </wd-cell-group>
      </wd-form>
    </view>

    <view class="grow overflow-hidden">
      <wd-tabs v-model="currentChannelTab" slidable="always" custom-class="custom-rate-tabs">
        <wd-form ref="rateFormRef" :model="rateForm">
          <template v-for="(item, key) in rateForm.rateMapDTOList" :key="key">
            <wd-tab v-if="checkedChannels.includes(item.channelCode)" :title="item.channelName" :name="item.channelCode">
              <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                <RateModule
                  :rate-item="rateItem"
                  :form-prop-prefix="['rateMapDTOList', key, 'rateDTOList', keyr]"
                  :show-d0-single-fee="showD0SingleFee"
                />
              </template>
            </wd-tab>
          </template>
        </wd-form>
      </wd-tabs>
    </view>

    <view class="shrink-0 p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        保存
      </wd-button>
    </view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { CommonApi } from '@/api/common';
import { decodeUrlParams } from '@/utils';
import { MerchReportApi } from '@/api-org/merch-report';

const showD0SingleFee = ref<boolean>(true);

const currentChannelTab = ref<undefined | string>(undefined);
type ChannelItem = {
  channelCode: string;
  channelName: string;
  disabled?: boolean;
};
const channelOptions = ref<ChannelItem[]>([]);

const checkedChannels = ref<string[]>([]);

const chnMerchNo = ref('');

const loading = ref(false);

const toast = useToast();

const formRef = ref<FormInstance | null>(null);

const form = reactive<any>({
});

// 规则
const rules: FormRules = {};
const rateFormRef = ref<FormInstance | null>(null);

const rateForm = reactive<any>({
  rateMapDTOList: [],
});

onLoad((query) => {
  query = decodeUrlParams(query || {});

  if (query.chnMerchNo) {
    chnMerchNo.value = query.chnMerchNo;
  }
  if (query.policyNameDesc) {
    form.policyNameDesc = query.policyNameDesc === 'undefined' ? '' : query.policyNameDesc;
  }

  init();
});

async function save() {
  // 检验费率表单
  const { valid: rateValid, errors: rateErrors } = await rateFormRef.value!.validate();
  if (!rateValid)
    return Promise.reject(rateErrors);

  // 校验是否有选中渠道
  if (checkedChannels.value.length === 0) {
    toast.warning('请至少选择一个渠道');
    return Promise.reject();
  }

  const rateDTOList: any = [];
  checkedChannels.value.forEach((channelCode) => {
    const rateMapDTOItem = rateForm.rateMapDTOList.find((item: any) => item.channelCode === channelCode);
    if (rateMapDTOItem) {
      rateDTOList.push(...rateMapDTOItem.rateDTOList);
    }
  });

  const params: any = {
    ...rateDTOList[0],
  };

  loading.value = true;

  // 提交
  MerchReportApi.chlMerchRateInfUpdate(params)
    .then(() => {
      toast.success({
        msg: '操作成功',
        closed: () => {
          uni.navigateBack();
        },
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

async function init() {
  const channels = await CommonApi.getChannelList({});
  const data = await MerchReportApi.queryChlMerchRateInf({
    chnMerchNo: chnMerchNo.value,
  });

  if (data) {
    const rateMap: any = {
      [data.channelCode]: [data],
    };

    const setedChannel = Object.keys(rateMap);

    checkedChannels.value = setedChannel;

    const rateList = Object.values(rateMap || {}).flat();
    rateList.forEach((item: any) => {
      item.rateInfoDTO = item.rateInfoDTO || {};
      item.isSame = 1;
    });

    rateForm.rateMapDTOList = [];

    setedChannel.forEach((channelCode) => {
      const channel = channels.find((item: ChannelItem) => item.channelCode === channelCode);
      if (channel) {
        rateForm.rateMapDTOList.push({
          channelCode,
          channelName: channel.channelName,
          rateDTOList: rateMap[channelCode],
        });
        channelOptions.value.push(channel);
      }
    });
  }
}
</script>

<style lang="scss" scoped>
:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
