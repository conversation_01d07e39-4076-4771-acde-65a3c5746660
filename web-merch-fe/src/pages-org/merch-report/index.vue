<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部搜索栏 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class" safe-area-inset-top
          @click-left="handlePageHome"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入商户名称或手机号" custom-class="w-full"
                @search="onSearch"
              />
            </view>
          </template>
        </wd-navbar>

        <!-- 下拉查询 -->
        <view class="my-2px bg-white">
          <wd-drop-menu>
            <wd-drop-menu-item
              v-model="where.reportRespCode" :options="reportRespCodeOptions"
              :[reportRespCodeTitleProp]="'入网状态'" @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.alipayOpenStatus" :options="alipayOpenStatusOptions" :[alipayOpenStatusTitleProp]="'支付宝'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.wechatOpenStatus" :options="wechatOpenStatusOptions" :[wechatOpenStatusTitleProp]="'微信'"
              @change="reload"
            />
            <wd-drop-menu-item
              v-model="where.unionpayOpenStatus" :options="unionpayOpenStatusOptions" :[unionpayOpenStatusTitleProp]="'银联'"
              @change="reload"
            />
          </wd-drop-menu>
        </view>

        <view v-if="[3, 5].includes(loginUser.orgType as number)">
          <wd-tabs v-model="where.selType" @change="reload">
            <wd-tab title="自营商户" name="1" />
            <wd-tab title="团队商户" name="2" />
          </wd-tabs>
        </view>
      </template>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key" class="mb-20rpx rounded-xl bg-white p-26rpx shadow"
        >
          <view>
            <view class="flex items-center font-bold">
              {{ item.merchantName }}
              <i v-if="item.merchantName" class="copy-icon" @click.stop="copyData(item.merchantName)" />
            </view>
            <view class="mt-20rpx flex items-center">
              <view class="grow">
                <view class="cell">
                  <text class="cell-label">
                    报备简称:
                  </text>
                  <view class="cell-value flex items-center">
                    {{ item.chnMerchName }}
                    <i v-if="item.chnMerchName" class="copy-icon" @click.stop="copyData(item.chnMerchName)" />
                  </view>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    报备通道:
                  </text>
                  <text class="cell-value">
                    {{ channelMap[item.channelCode] || '--' }}
                  </text>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    通道商户编号:
                  </text>
                  <view class="cell-value flex items-center">
                    {{ item.chnMerchNo || '--' }}
                    <i v-if="item.chnMerchNo" class="copy-icon" @click.stop="copyData(item.chnMerchNo)" />
                  </view>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    入网时间:
                  </text>
                  <text class="cell-value">
                    {{ item.createTime }}
                  </text>
                </view>
                <view class="cell">
                  <text class="cell-label">
                    入网状态:
                  </text>
                  <text class="cell-value">
                    {{ reportRespCodeMap[item.reportRespCode] }}
                  </text>
                </view>
                <!-- <view v-if="[3, 5].includes(loginUser.orgType as number) && item.reportRespCode === 2" class="cell">
                  <text class="cell-label">
                    结算政策:
                  </text>
                  <view class="cell-value">
                    <wd-tag custom-class="custom-tag-class" mark>
                      {{ item.policyNameDesc || '--' }}
                    </wd-tag>
                  </view>
                </view> -->
                <view v-if="item.reportRespCode === 3 && item.reportRespMsg" class="cell !items-start">
                  <text class="cell-label">
                    失败原因:
                  </text>
                  <text class="cell-value">
                    {{ item.reportRespMsg }}
                  </text>
                </view>
              </view>
              <view v-if="[3, 5].includes(loginUser.orgType as number) && item.reportRespCode === 2" class="mt-20rpx">
                <wd-button size="small" @click="toModifyRate(item)">
                  修改费率
                </wd-button>
              </view>
            </view>

            <!-- 渠道开通状态 -->
            <view
              v-if="item.reportRespCode === 2"
              class="mt-20rpx"
            >
              <wd-tag custom-class="custom-status-class" mark @click.stop="showErrorMsg('union', item.unionpayOpenStatus === 3, item.unionMessage)">
                <view class="flex items-center">
                  <text>
                    {{ formatTagDesc('union', item.unionpayOpenStatus) }}
                  </text>
                  <view v-if="item.unionpayOpenStatus === 3" class="ml-1px">
                    <wd-icon name="help-circle" size="28rpx" />
                  </view>
                </view>
              </wd-tag>
              <wd-tag custom-class="custom-status-class" mark @click.stop="showErrorMsg('wechat', item.wechatOpenStatus === 3, item.wechatMessage)">
                <view class="flex items-center">
                  <text>
                    {{ formatTagDesc('wechat', item.wechatOpenStatus) }}
                  </text>
                  <view v-if="item.wechatOpenStatus === 3" class="ml-1px">
                    <wd-icon name="help-circle" size="28rpx" />
                  </view>
                </view>
              </wd-tag>
              <wd-tag custom-class="custom-status-class" mark @click.stop="showErrorMsg('alipay', item.alipayOpenStatus === 3, item.alipayMessage)">
                <view class="flex items-center">
                  <text>
                    {{ formatTagDesc('alipay', item.alipayOpenStatus) }}
                  </text>
                  <view v-if="item.alipayOpenStatus === 3" class="ml-1px">
                    <wd-icon name="help-circle" size="28rpx" />
                  </view>
                </view>
              </wd-tag>
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { MerchReportApi } from '@/api-org/merch-report';
import { useClipboard } from '@/hooks';
import { useUserStore } from '@/store';
import { buildUrlWithParams, deepClone } from '@/utils';
import { CommonApi } from '@/api/common';

const loginUser = computed(() => useUserStore().info);

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 列表查询条件
const where = reactive<any>({
  selType: '1', // 查询类型 1-自营 2-团队 默认查询 1
  nameOrMobile: '', // 商户名称或者手机号(模糊匹配)
  reportRespCode: -1, // 入网状态
  unionpayOpenStatus: -2, // 银联报备状态
  wechatOpenStatus: -2, // 微信开通状态
  alipayOpenStatus: -2, // 支付宝开通状态
});

// 列表数据
const datasource = ref<Record<string, any>[]>([]);

const reportRespCodeTitleProp = computed(() => {
  const prop = where.reportRespCode === -1 ? 'title' : '';
  return prop;
});
const reportRespCodeOptions = [
  { label: '全部', value: -1 },
  { label: '审核中', value: 1 },
  { label: '有效', value: 2 },
  { label: '未通过', value: 3 },
];
const reportRespCodeMap: Record<number, string> = {
  1: '审核中',
  2: '有效',
  3: '未通过',
  99: '已关闭',
  10: '电子协议待签署',
  15: '待用户选择结算卡',
  12: '通道审核失败',
};

const alipayOpenStatusTitleProp = computed(() => {
  const prop = where.alipayOpenStatus === -2 ? 'title' : '';
  return prop;
});
const alipayOpenStatusOptions = [
  { label: '全部', value: -2 },
  { label: '暂不支持', value: -1 },
  { label: '未开通', value: 0 },
  { label: '已开通', value: 1 },
  { label: '申请中', value: 2 },
  { label: '开通失败', value: 3 },
];

const wechatOpenStatusTitleProp = computed(() => {
  const prop = where.wechatOpenStatus === -2 ? 'title' : '';
  return prop;
});
const wechatOpenStatusOptions = [
  { label: '全部', value: -2 },
  { label: '暂不支持', value: -1 },
  { label: '未开通', value: 0 },
  { label: '已开通', value: 1 },
  { label: '申请中', value: 2 },
  { label: '开通失败', value: 3 },
];

const unionpayOpenStatusTitleProp = computed(() => {
  const prop = where.unionpayOpenStatus === -2 ? 'title' : '';
  return prop;
});
const unionpayOpenStatusOptions = [
  { label: '全部', value: -2 },
  { label: '暂不支持', value: -1 },
  { label: '未开通', value: 0 },
  { label: '已开通', value: 1 },
  { label: '申请中', value: 2 },
  { label: '开通失败', value: 3 },
  { label: '待选择结算卡', value: 4 },
];

// 通道开通状态映射
const channelOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '2': '申请中',
  '3': '开通失败',
};
// 银联开通状态映射
const unionpayOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '2': '申请中',
  '3': '开通失败',
  '4': '-选择结算卡',
};
type ChannelType = 'union' | 'wechat' | 'alipay';
function formatTagDesc(type: ChannelType, status: number) {
  if (status === null) {
    return '--';
  }
  switch (type) {
    case 'union':
      return `银联${unionpayOpenStatusMap[status]}`;
    case 'wechat':
      return `微信${channelOpenStatusMap[status]}`;
    case 'alipay':
      return `支付宝${channelOpenStatusMap[status]}`;
    default:
      return '--';
  }
}

function showErrorMsg(type: ChannelType, hasError: boolean, msg: string) {
  if (!hasError) {
    return;
  }
  let title = '';
  const errorMsg = msg || '开通失败';

  switch (type) {
    case 'union':
      title = '银联';
      break;
    case 'wechat':
      title = '微信';
      break;
    case 'alipay':
      title = '支付宝';
      break;
    default:
      title = '';
      break;
  }

  title += '失败说明';
  message.alert({
    msg: errorMsg,
    title,
  });
}

const channelMap: any = {};

onShow(() => {
  getChannelList();
  pagingRef.value?.reload();
});

function reload() {
  pagingRef.value.reload();
}

/** 搜索查询数据 */
function onSearch({ value }: any) {
  where.nameOrMobile = value;
  pagingRef.value?.reload();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.reportRespCode = formatWhere.reportRespCode === -1 ? null : formatWhere.reportRespCode;
  formatWhere.unionpayOpenStatus = formatWhere.unionpayOpenStatus === -2 ? null : formatWhere.unionpayOpenStatus;
  formatWhere.wechatOpenStatus = formatWhere.wechatOpenStatus === -2 ? null : formatWhere.wechatOpenStatus;
  formatWhere.alipayOpenStatus = formatWhere.alipayOpenStatus === -2 ? null : formatWhere.alipayOpenStatus;

  MerchReportApi.findPage({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

function toModifyRate(item: any) {
  const url = buildUrlWithParams('/pages-org/merch-report/modify-rate', {
    chnMerchNo: item.chnMerchNo,
    policyNameDesc: item.policyNameDesc,
  });
  uni.navigateTo({ url });
}

async function getChannelList() {
  let data = await CommonApi.getChannelList();
  data = data || [];

  data.forEach((item: any) => {
    channelMap[item.channelCode] = item.channelName;
  });
}

function handlePageHome() {
  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell {
  @apply flex items-center;

  &:not(:last-child) {
    @apply mb-8rpx
  }

  .cell-label {
    @apply shrink-0;
  }

  .cell-value {
    @apply grow ml-20rpx flex items-center text-#333;
  }
}

:deep(.custom-tag-class) {
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-status-class){
  margin-right: 10rpx;
  font-size: 25rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;

  &:nth-last-child(1){
    margin-right: 0;
  }
}

:deep(.custom-navbar-class) {
  padding-top: 8px;

  .wd-navbar__title {
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.lr-border {
  position: relative;
  overflow: hidden;

  &::before,
  &::after {
    position: absolute;
    top: 0;
    width: 1px;
    height: 60%;
    background: #e6e6e6;
    content: '';
    transform: translateY(50%);
  }

  &::before {
    left: 0;

  }

  &::after {
    right: 0;
  }

}

.copy-icon {
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
