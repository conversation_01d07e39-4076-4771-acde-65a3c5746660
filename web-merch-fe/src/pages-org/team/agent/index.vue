<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部搜索栏 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class"
          safe-area-inset-top
          @click-left="handleBack"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入机构名称搜索" custom-class="w-full"
                @search="onSearch"
              />
            </view>
          </template>
        </wd-navbar>

        <view class="bg-white p-20rpx">
          <view class="mb-10rpx font-500">
            团队汇总
          </view>
          <view class="w-full flex items-center rounded-2 bg-primary p-20rpx">
            <view class="w-33.3% flex flex-col items-center">
              <text>代理总数</text>
              <text class="mt-10rpx">
                {{ orgSummary.agentCount }}
              </text>
            </view>
            <view class="lr-border w-33.3% flex flex-col items-center">
              <text>直属代理</text>
              <text class="mt-10rpx">
                {{ orgSummary.directAgentCount }}
              </text>
            </view>
            <view class="w-33.3% flex flex-col items-center">
              <text>非直属代理</text>
              <text class="mt-10rpx">
                {{ orgSummary.subAgentCount }}
              </text>
            </view>
          </view>
        </view>

        <!-- tabs -->
        <view class="mt-20rpx">
          <wd-tabs v-model="whereQueryMethod" @change="onChangeWhereQueryMethod">
            <wd-tab title="直属代理" name="1" />
            <wd-tab title="非直属代理" name="2" />
          </wd-tabs>
        </view>
      </template>

      <view class="mt-20rpx bg-white">
        <template v-if="where.queryMethod === 1">
          <wd-drop-menu custom-class="w-50%">
            <wd-drop-menu-item v-model="whereCheckStatus" :[checkStatusTitleProp]="'入网状态'" :options="checkStatusOptions" @change="onChangeWhereCheckStatus" />
            <wd-drop-menu-item v-model="whereRatePolicy" :[ratePolicyTitleProp]="'政策筛选'" :options="ratePolicyOptions" @change="onChangeWhereRatePolicy" />
          </wd-drop-menu>
        </template>

        <template v-if="where.queryMethod === 2">
          <wd-search
            hide-cancel placeholder-left placeholder="请输入上级团队编号查询" custom-class="w-full"
            @search="onSearchByOrgCode"
          />
        </template>
      </view>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-xl bg-white p-26rpx shadow"
          @click="onClickItem(item)"
        >
          <view>
            <template v-if="where.queryMethod === 1">
              <view class="flex items-center font-bold">
                {{ item.agentName }}
                <i
                  v-if="item.agentName"
                  class="copy-icon"
                  @click.stop="copyData(item.agentName)"
                />
              </view>
              <view class="mt-20rpx flex items-center">
                <view class="grow">
                  <view class="cell">
                    <text class="cell-label">
                      用户编号:
                    </text>
                    <view class="cell-value flex items-center">
                      {{ item.agentNo }}
                      <i
                        v-if="item.agentNo"
                        class="copy-icon"
                        @click.stop="copyData(item.agentNo)"
                      />
                    </view>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      加入时间:
                    </text>
                    <text class="cell-value">
                      {{ item.createTime }}
                    </text>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      入网状态:
                    </text>
                    <text class="cell-value">
                      {{ checkStatusMap[item.checkStatus] || '--' }}
                    </text>
                  </view>
                </view>
                <i class="i-mdi-chevron-right shrink-0 text-50rpx text-#00000040" />
              </view>

              <!-- 结算政策 -->
              <view class="mt-8px">
                <wd-tag custom-class="custom-tag-class" mark>
                  {{ item.policyDesc || '--' }}
                </wd-tag>
              </view>
            </template>

            <template v-if="where.queryMethod === 2">
              <view class="flex items-center font-bold">
                {{ item.agentName }}
                <i
                  v-if="item.agentName"
                  class="copy-icon"
                  @click.stop="copyData(item.agentName)"
                />
              </view>
              <view class="mt-20rpx flex items-center">
                <view class="grow">
                  <view class="cell">
                    <text class="cell-label">
                      用户编号:
                    </text>
                    <view class="cell-value flex items-center">
                      {{ item.agentNo }}
                      <i
                        v-if="item.agentNo"
                        class="copy-icon"
                        @click.stop="copyData(item.agentNo)"
                      />
                    </view>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      上级团队:
                    </text>
                    <view class="cell-value flex items-center">
                      {{ item.teamOrgNo }}
                      <i
                        v-if="item.teamOrgNo"
                        class="copy-icon"
                        @click.stop="copyData(item.teamOrgNo)"
                      />
                    </view>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      加入时间:
                    </text>
                    <text class="cell-value">
                      {{ item.createTime }}
                    </text>
                  </view>
                </view>
                <i class="i-mdi-chevron-right shrink-0 text-50rpx text-#00000040" />
              </view>
            </template>

            <!-- 其他信息 -->
            <view class="mt-20rpx rounded-2 bg-primary p-20rpx text-28rpx">
              <view class="w-full flex items-start">
                <view class="w-37%">
                  <text>商户数:</text>
                  <text>{{ item.merchantCount }}</text>
                </view>
                <view class="w-37%">
                  <text>激活终端数:</text>
                  <text>{{ item.activeCount }}</text>
                </view>
                <view class="w-26%">
                  <text>团队数:</text>
                  <text>{{ item.agentCount }}</text>
                </view>
              </view>
              <view class="mt-10rpx flex items-start">
                <view class="w-50%">
                  <text>本月交易量:</text>
                  <text>{{ item.thisMonthTransAmt }}</text>
                </view>
                <view class="w-50%">
                  <text>上月交易量:</text>
                  <text>{{ item.lastMonthTransAmt }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="bg-white px-40rpx pb-40rpx pt-20rpx">
          <wd-button type="primary" size="large" block @click="handleAddAgent">
            新增代理商
          </wd-button>
        </view>
      </template>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';
import { TeamApi } from '@/api-org/team';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { buildUrlWithParams } from '@/utils';
import { useUserStore } from '@/store';

const loginUser = computed(() => useUserStore().info);

// 分页器ref
const pagingRef = ref();

// 列表查询条件
const where = reactive<any>({
  queryMethod: 1, // 查询方式（0-一级代理 1-直属 2-非直属）

  orgName: '', // 企业名称（选填）
  orgNo: '', // 机构编号（选填）
  ratePolicyId: null, // 费率政策ID 根据政策筛选上送ID（选填）
  checkStatus: null, // 代理商状态 3-正常 4-审核中 5-驳回编辑 默认null-全部（选填）

  orderBy: '', // 排序方式（选填）不填默认ID倒序
});

const orgSummary = ref<any>({
  branchCount: 0, // 运营中心总数
  oneLevelAgentCount: 0, // 一级代理总数

  agentCount: 0, // 代理总数
  directAgentCount: 0, // 直属代理总数
  subAgentCount: 0, // 非直属代理总数
});

const whereQueryMethod = ref('1');
function onChangeWhereQueryMethod({ name }: { name: string }) {
  where.queryMethod = Number(name);
  pagingRef.value.reload();
}

const whereRatePolicy = ref('0');
const ratePolicyOptions = ref<any>([]);
function onChangeWhereRatePolicy({ value }: { value: string }) {
  where.ratePolicyId = value === '0' ? null : whereRatePolicy.value;
  pagingRef.value.reload();
}

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const whereCheckStatus = ref('0');
const checkStatusOptions = [
  { label: '全部', value: '0' },
  { label: '正常', value: 3 },
  { label: '审核中', value: 4 },
  { label: '驳回', value: 5 },
];
const checkStatusMap: EnumMap = {
  3: '正常',
  4: '审核中',
  5: '驳回',
};

const ratePolicyTitleProp = computed(() => {
  const prop = whereRatePolicy.value === '0' ? 'title' : '';
  return prop;
});
const checkStatusTitleProp = computed(() => {
  const prop = whereCheckStatus.value === '0' ? 'title' : '';
  return prop;
});

onShow(() => {
  queryOrgSummary();
  queryRatePolicy();
  pagingRef.value?.reload();
});

async function queryRatePolicy() {
  let data = await RatePolicyApi.listOwnerPolicy();
  data = data || [];
  data.unshift({
    id: '0',
    policyDesc: '全部',
  });

  const formatData = data.map((item: any) => {
    return {
      label: item.policyDesc,
      value: item.id,
    };
  });

  ratePolicyOptions.value = formatData || [];
}

/** 根据状态查询数据 */
function onChangeWhereCheckStatus({ value }: { value: any }) {
  where.checkStatus = value === '0' ? null : Number(value);
  pagingRef.value.reload();
}

/** 搜索查询数据 */
function onSearch({ value }: any) {
  where.orgName = value;
  pagingRef.value?.reload();
}

/** 搜索查询数据 */
function onSearchByOrgCode({ value }: any) {
  where.orgNo = value;
  pagingRef.value?.reload();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

async function queryOrgSummary() {
  const data = await TeamApi.queryOrgSummary();
  orgSummary.value = Object.assign({}, data);
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  TeamApi.findOrgAgentPage({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

/**
 * 点击列表项
 */
function onClickItem(item: any) {
  switch (item.checkStatus) {
    case 3:
      // 详情
      // eslint-disable-next-line no-case-declarations
      const url = buildUrlWithParams('/pages-org/team/agent/agent-detail', {
        orgCode: item.agentNo,
        orgType: item.orgType,
      });
      uni.navigateTo({ url });
      break;
    case 4:
    case 5:
      // 驳回重提
      uni.navigateTo({ url: `/pages-org/team/agent/agent-update?id=${item.id}` });
      break;
  }
}

function handleAddAgent() {
  if (loginUser.value.orgType === 2) {
    uni.navigateTo({ url: '/pages-org/team/agent/agent-update' });
  }
  else {
    uni.navigateTo({ url: '/pages-org/team/agent/agent-update-personal' });
  }
}

function handleBack() {
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }

:deep(.custom-tag-class){
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.lr-border{
  position: relative;
  overflow: hidden;

  &::before,
  &::after{
   position: absolute;
   top: 0;
   width: 1px;
   height:60%;
   background: #e6e6e6;
   content: '';
   transform: translateY(50%);
  }

  &::before{
   left: 0;

  }

  &::after{
   right: 0;
  }

}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
