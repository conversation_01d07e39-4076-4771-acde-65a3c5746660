<template>
  <view class="h-full flex flex-col overflow-hidden" :class="{ 'bg-primary': show }">
    <view class="grow overflow-hidden">
      <scroll-view
        scroll-y :show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
        class="h-full"
      >
        <wd-tabs v-model="currentInfoTypeTab" custom-class="!bg-transparent" sticky @change="onChangeInfoTypeTab">
          <block v-if="show">
            <!-- ! 结算政策 -->
            <wd-tab title="结算政策">
              <wd-form ref="rateFormRef" :model="rateForm" :rules="rateFormRules" custom-class="error-message__align-right">
                <view class="mb-20rpx">
                  <wd-cell
                    title="交易结算政策" title-width="100px"
                    prop="ratePolicyId" is-link
                    @click="onSelectRatePolicy"
                  >
                    <wd-textarea
                      v-model="rateForm.ratePolicyName"
                      placeholder="请选择"
                      auto-height no-border readonly
                      custom-textarea-class="text-right"
                    />
                  </wd-cell>
                </view>
              </wd-form>

              <wd-tabs v-model="currentChannelTab" slidable="always">
                <template v-for="(item, key) in rateMapDTOList" :key="key">
                  <wd-tab :title="item.channelName">
                    <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                      <RateModule
                        :rate-item="rateItem"
                        :readonly="true"
                      />
                    </template>
                  </wd-tab>
                </template>
              </wd-tabs>
            </wd-tab>

            <!-- ! 返现政策 -->
            <wd-tab title="返现政策" :lazy="false">
              <view v-if="!isResubmit" class="mb-20rpx">
                <wd-cell
                  title="活动返现政策" title-width="100px"
                  is-link
                  @click="onSelectCashbackPolicy"
                >
                  <wd-textarea
                    v-model="ruleForm.cashbackPolicyName"
                    placeholder="请选择"
                    auto-height no-border readonly
                    custom-textarea-class="text-right"
                  />
                </wd-cell>
              </view>

              <wd-form ref="ruleFormRef" :model="ruleForm">
                <wd-checkbox-group v-model="checkedServiceRules" custom-class="custom-checkbox-group">
                  <wd-tabs v-model="activeChannelTabKey" custom-class="custom-rate-tabs" slidable="always">
                    <wd-tab v-for="(channel, cIndex) in ruleForm.policyConfigByChannelGroup || []" :key="cIndex" :title="channel.channelName" :name="String(cIndex)" :lazy="false">
                      <wd-tabs v-model="activeTerminalSourceTabKey" custom-class="custom-rate-tabs">
                        <wd-tab v-for="(tab, tabIndex) in channel.channelData || []" :key="tabIndex" :title="tab.label" :name="String(tab.key)" :lazy="false">
                          <view class="gap-primary" />
                          <wd-cell-group title="服务费返现规则" border />
                          <template v-if="tab.serviceFeeData?.length">
                            <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                              <wd-cell-group v-if="item.show" border custom-class="custom-rule-group">
                                <view class="gap-primary" />
                                <wd-cell center custom-class="rule-cell">
                                  <template #title>
                                    <wd-checkbox :model-value="item.configId">
                                      {{ item.policyName }}
                                    </wd-checkbox>
                                  </template>
                                  <template v-if="checkedServiceRules.includes(item.configId)">
                                    <wd-input
                                      v-model="item.cashbackAmt" type="text" placeholder="设置返现金额" use-suffix-slot
                                      :prop="`policyConfigByChannelGroup.${cIndex}.channelData.${tabIndex}.serviceFeeData.${key}.cashbackAmt`"
                                      :rules="serviceCashbackAmtRules(item.configId, item.parentCashbackAmt)"
                                    >
                                      <template #suffix>
                                        <text>元</text>
                                      </template>
                                    </wd-input>
                                  </template>
                                </wd-cell>
                                <template v-if="checkedServiceRules.includes(item.configId)">
                                  <wd-cell :label="`注: 下级必须低于或等于${item.parentCashbackAmt}元`" custom-class="rule-desc" />
                                </template>
                              </wd-cell-group>
                            </template>
                          </template>
                          <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />

                          <view class="gap-primary" />
                          <wd-cell-group title="通讯费返现规则" border />
                          <template v-if="tab.simFeeData?.some((s:any) => s.data.length)">
                            <view v-for="(period, idx) in tab.simFeeData" :key="idx">
                              <template v-if="period.show">
                                <view class="gap-primary" />
                                <wd-cell :title="period.name" />
                                <wd-checkbox-group v-model="period.checkedList" custom-class="custom-checkbox-group">
                                  <template v-for="(item, key) in period.data || []" :key="key">
                                    <wd-cell-group v-if="item.show" border custom-class="custom-rule-group">
                                      <view class="h-6px bg-primary" />
                                      <wd-cell center custom-class="rule-cell">
                                        <template #title>
                                          <wd-checkbox :model-value="item.configId">
                                            {{ item.policyName }}
                                          </wd-checkbox>
                                        </template>
                                        <template v-if="period.checkedList.includes(item.configId)">
                                          <wd-input
                                            v-model="item.cashbackAmt" type="text" placeholder="设置返现金额" use-suffix-slot
                                            :prop="`policyConfigByChannelGroup.${cIndex}.channelData.${tabIndex}.simFeeData.${idx}.data.${key}.cashbackAmt`"
                                            :rules="simFeeCashbackAmtRules(item.configId, item.parentCashbackAmt, period)"
                                          >
                                            <template #suffix>
                                              <text>元</text>
                                            </template>
                                          </wd-input>
                                        </template>
                                      </wd-cell>
                                      <template v-if="period.checkedList.includes(item.configId)">
                                        <wd-cell :label="`注: 下级必须低于或等于${item.parentCashbackAmt}元`" custom-class="rule-desc" />
                                      </template>
                                    </wd-cell-group>
                                  </template>
                                </wd-checkbox-group>
                              </template>
                            </view>
                          </template>
                          <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />
                        </wd-tab>
                      </wd-tabs>
                    </wd-tab>
                  </wd-tabs>
                </wd-checkbox-group>
              </wd-form>
            </wd-tab>

            <!-- ! 企业信息 -->
            <wd-tab title="企业信息" :lazy="false">
              <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
                <wd-input
                  v-model="form.contactsTel" prop="contactsTel"
                  label="登录手机号" label-width="100px"
                  placeholder="该手机号将作为登录账号"
                  align-right clearable
                />

                <view class="p-12px text-14px font-medium">
                  企业信息
                </view>
                <view class="flex justify-center bg-white py-12px">
                  <view v-for="(item, key) in [fileMap[7]]" :key="key" class="w-50%">
                    <GivenUpload
                      v-model:file-data="item.fileData"
                      :file-name="item.fileName"
                      :placeholder="item.placeholder"
                      custom-class="w-full"
                      @choose="onChooseBusinessLicense"
                    />
                  </view>
                </view>
                <wd-cell-group border>
                  <wd-input
                    v-model="form.agentSname" prop="agentSname"
                    label="机构简称" placeholder="请输入团队名或企业简称" label-width="100px"
                    clearable align-right
                  />
                  <wd-input
                    v-model="form.agentName" prop="agentName"
                    label="企业名称" placeholder="可图片自动识别" label-width="100px"
                    clearable align-right
                    @input="onChangeOrgName"
                  />
                  <wd-textarea
                    v-model="form.licenseNo" prop="licenseNo"
                    label="统一社会信用代码" placeholder="可图片自动识别"
                    clearable auto-height
                    custom-textarea-class="text-right"
                  />
                  <wd-textarea
                    v-model="form.licenseAddr" prop="licenseAddr"
                    label="营业执照注册地址" placeholder="可图片自动识别"
                    auto-height
                    custom-textarea-class="text-right"
                  />
                  <wd-datetime-picker
                    v-model="licenseDateRegion" prop="licenseEndDate"
                    label="营业执照有效期"
                    type="date" align-right
                    :min-date="minDateLicense" :max-date="maxDateLicense"
                    :default-value="defaultDateRegionValue"
                    @confirm="onConfirmLicenseDateRegion"
                  />
                  <wd-cell
                    title="经营所在地区" title-width="100px"
                    prop="districtCode" is-link
                    @click="onSelectArea"
                  >
                    <wd-textarea
                      v-model="businessArea"
                      placeholder="请选择省/市/区"
                      auto-height no-border readonly
                      custom-textarea-class="text-right"
                    />
                  </wd-cell>
                  <wd-textarea
                    v-model="form.officeAddr" prop="officeAddr"
                    label="经营详细地址" placeholder="请输入详细地址" label-width="100px"
                    auto-height
                    custom-textarea-class="text-right"
                  />
                  <wd-input
                    v-model="form.taxPoint" prop="taxPoint"
                    label="企业开票税点" placeholder="请输入" label-width="100px"
                    align-right use-suffix-slot type="number"
                  >
                    <template #suffix>
                      <text class="text-24rpx">
                        %
                      </text>
                    </template>
                  </wd-input>

                  <wd-datetime-picker
                    v-model="form.signDate" prop="signDate"
                    label="签约日期"
                    type="date" align-right
                    :default-value="dayjs().valueOf()"
                    :min-date="minDateLicense" :max-date="maxDateLicense"
                  />
                </wd-cell-group>

                <view class="p-12px text-14px font-medium">
                  法人信息
                </view>
                <wd-cell-group border>
                  <view class="bg-white py-12px">
                    <view class="flex">
                      <view v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" class="w-50%">
                        <GivenUpload
                          v-model:file-data="item.fileData"
                          :file-name="item.fileName"
                          :placeholder="item.placeholder"
                          custom-class="w-full"
                          @choose="(file) => ocrIdcardByFile(file, item)"
                        />
                      </view>
                    </view>
                  </view>

                  <wd-input
                    v-model="form.legalName" prop="legalName"
                    label="法人姓名" placeholder="可图片自动识别" label-width="100px"
                    align-right
                  />
                  <wd-input
                    v-model="form.legalCertNo" prop="legalCertNo"
                    label="法人证件号" placeholder="可图片自动识别" label-width="100px"
                    align-right
                  />
                  <wd-datetime-picker
                    v-model="legalDateRegion" prop="legalCertEndDate"
                    label="法人证件有效期" label-width="120px"
                    type="date" :min-date="minDate" :max-date="maxDate" align-right
                    :default-value="defaultDateRegionValue"
                    @confirm="onConfirmLegalDateRegion"
                  />
                  <wd-textarea
                    v-model="form.legalCertAddr" prop="legalCertAddr"
                    label="法人证件地址" placeholder="可图片自动识别" label-width="100px"
                    auto-height
                    custom-textarea-class="text-right"
                  />
                  <wd-input
                    v-model="form.legalTel" prop="legalTel" type="number"
                    label="法人手机号" placeholder="请输入法人手机号" label-width="100px"
                    align-right
                  />
                </wd-cell-group>

                <view class="flex items-center justify-between p-12px text-14px">
                  <text class="font-medium">
                    联系人信息
                  </text>
                  <view @click="handleSyncLegalInfo">
                    <text class="text-#4d80f0">
                      同步法人信息
                    </text>
                  </view>
                </view>
                <wd-cell-group border>
                  <!-- <view class="bg-white py-12px">
                  <view class="flex">
                    <view v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" class="w-50%">
                      <GivenUpload
                        v-model:file-data="item.fileData"
                        :file-name="item.fileName"
                        :placeholder="item.placeholder"
                        custom-class="w-full"
                        @choose="(file) => ocrIdcardByFile(file, item)"
                      />
                    </view>
                  </view>
                </view> -->

                  <wd-input
                    v-model="form.contactsName" prop="contactsName"
                    label="联系人姓名" placeholder="可图片自动识别" label-width="100px"
                    align-right
                  />
                  <wd-input
                    v-model="form.contactsCertNo" prop="contactsCertNo"
                    label="联系人证件号" placeholder="可图片自动识别" label-width="100px"
                    align-right
                  />
                  <wd-datetime-picker
                    v-model="contactsDateRegion" prop="contactsCertEndDate"
                    label="联系人证件有效期" label-width="130px"
                    type="date" :min-date="minDate" :max-date="maxDate" align-right
                    :default-value="defaultDateRegionValue"
                    @confirm="onConfirmContactsDateRegion"
                  />
                  <wd-textarea
                    v-model="form.contactsCertAddr" prop="contactsCertAddr"
                    label="联系人证件地址" placeholder="可图片自动识别" label-width="120px"
                    auto-height
                    custom-textarea-class="text-right"
                  />
                </wd-cell-group>
              </wd-form>
            </wd-tab>

            <!-- ! 结算信息 -->
            <wd-tab title="结算信息" :lazy="false">
              <wd-form ref="settleFormRef" :model="settleForm" :rules="settleFormRules" custom-class="error-message__align-right">
                <view class="rounded-lg bg-primary py-20px">
                  <view class="flex flex-wrap justify-center">
                    <template v-for="(item, key) in [fileMap[16]]" :key="key">
                      <view class="basis-1/2">
                        <GivenUpload
                          v-model:file-data="item.fileData"
                          :file-name="item.fileName"
                          :placeholder="item.placeholder"
                          custom-class="w-full"
                        />
                      </view>
                    </template>
                  </view>
                </view>
                <wd-cell-group border>
                  <wd-input
                    :model-value="settleForm.bankAccountName" prop="bankAccountName"
                    label="结算账户户名" label-width="100px"
                    placeholder="可自动识别"
                    align-right
                    readonly
                  />
                  <wd-input
                    v-model="settleForm.bankAccountNo" prop="bankAccountNo"
                    label="对公卡卡号" label-width="88px"
                    placeholder="请填写对公卡卡号"
                    clearable align-right
                  />
                  <wd-cell
                    title="开户行所在地" title-width="100px"
                    prop="bankCity" is-link
                    @click="onSelectBankArea"
                  >
                    <wd-textarea
                      v-model="bankArea"
                      placeholder="请选择"
                      auto-height no-border readonly
                      custom-textarea-class="text-right"
                    />
                  </wd-cell>
                  <wd-cell
                    title="开户行总行" title-width="100px"
                    prop="typeCode" is-link
                    @click="onSelectBankType"
                  >
                    <wd-textarea
                      v-model="settleForm.typeName"
                      placeholder="请选择"
                      auto-height no-border readonly
                      custom-textarea-class="text-right"
                    />
                  </wd-cell>
                  <wd-cell
                    title="开户行支行" title-width="100px"
                    prop="bankSubName" is-link
                    @click="onSelectBankSub"
                  >
                    <wd-textarea
                      v-model="settleForm.bankSubName"
                      placeholder="请选择"
                      auto-height no-border readonly
                      custom-textarea-class="text-right"
                    />
                  </wd-cell>
                  <wd-input
                    v-model="settleForm.mobile" prop="mobile"
                    label="预留手机号" label-width="100px"
                    placeholder="请输入预留手机号"
                    align-right
                  />
                </wd-cell-group>
              </wd-form>
            </wd-tab>
          </block>
        </wd-tabs>
      </scroll-view>
    </view>

    <view class="shrink-0 p-40rpx">
      <wd-button type="primary" size="large" block @click="save">
        提交
      </wd-button>
    </view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common';
import { emitter } from '@/utils/emitter';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { Toast, deepClone } from '@/utils';
import { TeamApi } from '@/api-org/team';

const simFeePolicyPeriodGroupDef: any = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
  },
];

const policyConfigByTerminalSourceDef: any = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: [],
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: [],
  },
];

interface IPolicyConfigByChannelGroupItem {
  channelCode: string;
  channelName: string;
  channelData: any[];
}

const toast = useToast();

const show = ref(false);

// 当前信息类型tab
const currentInfoTypeTab = ref(0);

const scrollTop = ref(0);

const channelList = ref<any>([]);

const isResubmit = ref(false);

// #region 结算政策相关

const rateFormRef = ref<FormInstance | null>(null);
const rateForm = reactive<any>({
  ratePolicyId: '',
  ratePolicyName: '',
});
const rateFormRules: FormRules = {
  ratePolicyId: [{ required: true, message: '请选择' }],
};
// 费率政策列表
const rateMapDTOList = ref<any>([]);
// 当前选中通道的费率政策
const currentChannelTab = ref(0);

// #endregion

//  #region 返现政策

// 选中的规则
const checkedServiceRules = ref<any[]>([]);

const activeChannelTabKey = ref('0');
const activeTerminalSourceTabKey = ref('1');

const ruleFormRef = ref<FormInstance | null>(null);
const ruleForm = reactive<any>({
  serviceFeePolicyDTOList: [],
  simFeePolicyDTOList: [],
});

// #endregion

// #region 企业信息
const formRef = ref<FormInstance | null>(null);
const form = reactive<any>({
  // 机构类型 0:个人 1:企业
  orgType: 1,
  taxPoint: '',
  // 证件类型
  contactsCertType: '01',
  legalCertType: '01',
  signDate: '',
});

const rules: FormRules = {
  agentSname: [{ required: true, message: '请填写团队名或企业简称' }],
  agentName: [{ required: true, message: '请填写企业名称' }],
  licenseNo: [{ required: true, message: '请填写营业执照号码' }],
  licenseEndDate: [{ required: true, message: '请选择营业执照有效期' }],
  licenseAddr: [{ required: true, message: '请填写营业执照注册地址' }],

  districtCode: [{ required: true, message: '请选择经营所在省市区' }],
  officeAddr: [{ required: true, message: '请填写经营详细地址' }],

  legalCertNo: [{ required: true, message: '请填写证件号码' }],
  legalName: [{ required: true, message: '请填写法人姓名' }],
  legalCertAddr: [{ required: true, message: '请填写法人证件地址' }],
  legalCertEndDate: [{ required: true, message: '请选择证件有效期' }],
  legalTel: [{ required: true, message: '请填写法人手机号' }],

  contactsName: [{ required: true, message: '请填写联系人姓名' }],
  contactsCertNo: [{ required: true, message: '请填写联系人证件号码' }],
  contactsCertEndDate: [{ required: true, message: '请选择证件有效期' }],
  contactsCertAddr: [{ required: true, message: '请填写联系人证件地址' }],
  contactsTel: [{ required: true, message: '请填写手机号' }],

  taxPoint: [{ required: true, message: '请填写税点' }],
  signDate: [{ required: true, message: '请选择签约日期' }],
};

// #endregion

// #region 文件列表
type FileType = 1 | 2 | 7 | 16;
const fileMap = ref <Record<FileType, GivenUploadProps>> ({
  1: {
    fileName: '人像面',
    placeholder: require('@/static/images/card_face.png'),
    fileData: '',
    fileType: 1,
  },
  2: {
    fileName: '国徽面',
    placeholder: require('@/static/images/card_back.png'),
    fileData: '',
    fileType: 2,
  },
  7: {
    fileName: '营业执照',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 7,
  },
  16: {
    fileName: '开户许可证',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 16,
  },
});

// #endregion

const licenseDateRegion = ref<any>([]);
const defaultDateRegionValue = ref([dayjs().valueOf(), dayjs().valueOf()]);

const legalDateRegion = ref<any>([]);
const contactsDateRegion = ref<any>([]);

// 身份证有效期选择范围
const minDate = dayjs('1900-01-01').valueOf();
const maxDate = dayjs('2099-12-31').valueOf();

// 营业执照有效期选择范围
const minDateLicense = dayjs().subtract(50, 'year').valueOf();
const maxDateLicense = dayjs('2099-12-31').valueOf();

const bankArea = ref('');
const businessArea = ref('');

// #region 结算信息

const oriPolicyDTOListMap: any = {
  serviceFeePolicyDTOList: [],
  simFeeNewPolicyDTO: {},
};

const settleFormRef = ref<FormInstance | null>(null);
const settleFormRules: FormRules = {
  bankAccountNo: [{ required: true, message: '请填写对公卡卡号' }],
  bankCity: [{ required: true, message: '请选择开户行所在地' }],
  typeCode: [{ required: true, message: '请选择开户行总行' }],
  bankSubName: [{ required: true, message: '请选择开户行支行' }],
  mobile: [{ required: true, message: '请输入预留手机号' }],
};
const settleForm = reactive<any>({
  accountType: 'G',
});

// #endregion

onLoad(async (query: any) => {
  toast.loading('加载中...');

  await getChannelList();
  await queryAddDisplay();
  // 驳回
  if (query.id) {
    isResubmit.value = true;
    handleRejectData(query.id);
  }
});

onMounted(() => {
  setTimeout(() => {
    show.value = true;
    toast.close();
  }, 800);
  uni.setNavigationBarTitle({
    title: isResubmit.value ? '驳回编辑-企业' : '新增团队-企业',
  });
});

async function getChannelList() {
  const data = await CommonApi.getChannelList({});
  channelList.value = data || [];
}

/**
 * 处理驳回数据
 */
async function handleRejectData(id: string) {
  const data = await TeamApi.queryRejectEditDetail({ id });
  Object.assign(form, data);

  if (form.signDate) {
    form.signDate = dayjs(form.signDate).valueOf();
  }

  // 处理结算政策
  rateForm.ratePolicyName = data.policyDesc;
  rateForm.ratePolicyId = data.policyId;
  if (data.rateMap) {
    const rateMapDTOListMap: any = [];
    Object.keys(data.rateMap).forEach((key) => {
      const channel = channelList.value.find((i: any) => i.channelCode === key);
      rateMapDTOListMap.push({
        channelName: channel?.channelName,
        channelCode: channel?.channelCode,
        rateDTOList: data.rateMap[key],
      });
    });
    rateMapDTOList.value = rateMapDTOListMap;
  }

  const serviceFeePolicyDTOMap: any = {};
  const simFeePolicyDTOMap: any = {};
  // 自身全部服务费
  oriPolicyDTOListMap.serviceFeePolicyDTOList.forEach((s: any) => {
    serviceFeePolicyDTOMap[s.configId] = s;
  });

  // 自身全部流量费
  Object.keys(oriPolicyDTOListMap.simFeeNewPolicyDTO || {}).forEach((key) => {
    simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
    oriPolicyDTOListMap.simFeeNewPolicyDTO[key].forEach((s: any) => {
      simFeePolicyDTOMap[key][s.configId] = s;
    });
  });

  ruleForm.serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];
  ruleForm.serviceFeePolicyDTOList.forEach((i: any) => {
    const configItem = serviceFeePolicyDTOMap[i.configId] || {};
    i.parentCashbackAmt = configItem.parentCashbackAmt;
    i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);

    if (i.openStatus) {
      checkedServiceRules.value.push(i.configId);
    }
    else if (!i.show) {
      checkedServiceRules.value.push(i.configId);
      i.cashbackAmt = 0;
    }
    else {
      i.cashbackAmt = '';
    }
  });

  const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key];
        period.checkedList = [];
        period.data.forEach((item: any) => {
          const configItem = (simFeePolicyDTOMap[key] && simFeePolicyDTOMap[key][item.configId]) || {};
          item.parentCashbackAmt = configItem.parentCashbackAmt;
          item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);

          if (item.openStatus) {
            period.checkedList.push(item.configId);
          }
          else if (!item.show) {
            period.checkedList.push(item.configId);
            item.cashbackAmt = 0;
          }
          else {
            item.cashbackAmt = '';
          }
        });
      }
    });
  });

  // 找出所有通道
  let allChannelCodes: any = [];
  try {
    const channelCodes = new Set(
      [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
        .flat(Infinity)
        .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
        .filter(code => code?.trim())
        .map(code => code.trim()),
    );
    allChannelCodes = [...channelCodes];
  }
  catch (error) {
    console.log(error);
  }

  const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
  allChannelCodes.forEach((item: any) => {
    const channelItem = channelList.value.find((channel: any) => channel.channelCode === item);
    policyConfigByChannelGroup.push({
      channelCode: item,
      channelName: channelItem?.channelName,
      channelData: deepClone(policyConfigByTerminalSourceDef),
    });
  });

  policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
    channel.channelData.forEach((tab) => {
      tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

      const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
        const periodData = period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);
        return {
          ...period,
          data: periodData,
          show: periodData.some((item: any) => item.show),
        };
      });
      tab.simFeeData = simFeeData;
    });
  });

  ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

  // 处理企业信息
  form.contactsTel = data.contactsTelCipher;
  form.legalTel = data.legalTelCipher;

  if (form.licenseStartDate && form.licenseEndDate) {
    licenseDateRegion.value = [dayjs(form.licenseStartDate).valueOf(), dayjs(form.licenseEndDate).valueOf()];
  }
  if (form.legalCertStartDate && form.legalCertEndDate) {
    legalDateRegion.value = [dayjs(form.legalCertStartDate).valueOf(), dayjs(form.legalCertEndDate).valueOf()];
  }
  if (form.contactsCertStartDate && form.contactsCertEndDate) {
    contactsDateRegion.value = [dayjs(form.contactsCertStartDate).valueOf(), dayjs(form.contactsCertEndDate).valueOf()];
  }

  businessArea.value = [form.provinceName, form.cityName, form.districtName].join('');

  form.contactsCertNo = data.contactsCertNoCipher;
  form.legalCertNo = data.legalCertNoCipher;

  // 处理图片
  if (data.fileDTOList) {
    const fileList = Object.values(fileMap.value);
    fileList.forEach((item) => {
      data.fileDTOList.forEach((item2: any) => {
        if (item2?.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    });
  }

  // 处理结算信息
  Object.assign(settleForm, data.bankCard);
  bankArea.value = [settleForm.bankProvinceName, settleForm.bankCityName].join('');
  settleForm.bankAccountNo = settleForm.bankAccountNoCipher;
  settleForm.typeName = settleForm.bankName;
  settleForm.mobile = settleForm.mobileCipher;
}

async function save() {
  // 校验表单
  const { valid, errors } = await rateFormRef.value!.validate();
  if (!valid) {
    toast.warning('结算政策信息有误');
    return Promise.reject(errors);
  }

  const { valid: valid1, errors: errors1 } = await ruleFormRef.value!.validate();
  if (!valid1) {
    toast.warning('返现政策信息有误');
    return Promise.reject(errors1);
  }

  const { valid: valid2, errors: errors2 } = await formRef.value!.validate();
  if (!valid2) {
    toast.warning('企业信息有误');
    return Promise.reject(errors2);
  }

  const { valid: valid3, errors: errors3 } = await settleFormRef.value!.validate();
  if (!valid3) {
    toast.warning('结算信息有误');
    return Promise.reject(errors3);
  }

  // 校验文件
  await checkFile();

  let serviceFeePolicyDTOList: any = [];
  const simPolicySubmitData: any = {};

  ruleForm.policyConfigByChannelGroup.forEach((channel: any) => {
    channel.channelData.forEach((item: any) => {
      // 格式化服务费
      const checkedServiceFeeData = item.serviceFeeData.filter((i: any) => checkedServiceRules.value.includes(i.configId));
      serviceFeePolicyDTOList = [...serviceFeePolicyDTOList, ...checkedServiceFeeData];

      // 格式化流量费
      item.simFeeData.forEach((period: any) => {
        const checkedSimFeeData = period.data.filter((i: any) => period.checkedList.includes(i.configId));
        simPolicySubmitData[period.field] = [...(simPolicySubmitData[period.field] || []), ...checkedSimFeeData];
      });
    });
  });

  const params: any = {
    ...form,
    ...rateForm,
    serviceFeePolicyDTOList,
    simFeeNewPolicyDTO: simPolicySubmitData,
    orgBankCardRequest: settleForm,
  };

  params.signDate = dayjs(params.signDate).format('YYYY-MM-DD');

  params.fileDTOList = Object.values(fileMap.value)
    .map((item: any) => {
      if (item.fileData) {
        return {
          fileName: item.fileName,
          fileData: item.fileData,
          fileType: item.fileType,
          suffixType: item.suffixType || 'png',
        };
      }
      return null;
    })
    .filter(f => !!f);

  if (isResubmit.value) {
    await TeamApi.rejectEditAgent(params);
  }
  else {
    await TeamApi.addAgent(params);
  }

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

function onChangeInfoTypeTab() {
  scrollTop.value = -1;
  nextTick(() => {
    scrollTop.value = 0;
  });
}

function onSelectRatePolicy() {
  uni.navigateTo({
    url: '/pages-org/picker-view/rate-policy/index',
  });
  emitter.on('picker-rate-policy', (data: any) => {
    rateForm.ratePolicyId = data.id;
    rateForm.ratePolicyName = data.policyDesc;
    rateMapDTOList.value = data.rateMapDTOList;
  });
}

/**
 * 查询自身返现政策
 */
async function queryAddDisplay() {
  const data = await RatePolicyApi.queryAddDisplay();
  let { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};
  serviceFeePolicyDTOList = serviceFeePolicyDTOList || [];
  simFeeNewPolicyDTO = simFeeNewPolicyDTO || {};

  serviceFeePolicyDTOList.forEach((i: any) => {
    i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
    if (!i.show) {
      i.cashbackAmt = 0;
      checkedServiceRules.value.push(i.configId);
    }
  });
  ruleForm.serviceFeePolicyDTOList = serviceFeePolicyDTOList;

  const simFeePolicyMap = simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key];
        period.checkedList = [];
        period.data.forEach((item: any) => {
          item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
          if (!item.show) {
            item.cashbackAmt = 0;
            period.checkedList.push(item.configId);
          }
        });
      }
    });
  });

  // 找出所有通道
  let allChannelCodes: any = [];
  try {
    const channelCodes = new Set(
      [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
        .flat(Infinity)
        .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
        .filter(code => code?.trim())
        .map(code => code.trim()),
    );
    allChannelCodes = [...channelCodes];
  }
  catch (error) {
    console.log(error);
  }

  const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
  allChannelCodes.forEach((item: any) => {
    const channelItem = channelList.value.find((channel: any) => channel.channelCode === item);
    policyConfigByChannelGroup.push({
      channelCode: item,
      channelName: channelItem?.channelName,
      channelData: deepClone(policyConfigByTerminalSourceDef),
    });
  });

  policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
    channel.channelData.forEach((tab) => {
      tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

      const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
        const periodData = period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);
        return {
          ...period,
          data: periodData,
          show: periodData.some((item: any) => item.show),
        };
      });
      tab.simFeeData = simFeeData;
    });
  });

  ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

  oriPolicyDTOListMap.serviceFeePolicyDTOList = deepClone(serviceFeePolicyDTOList);
  oriPolicyDTOListMap.simFeeNewPolicyDTO = deepClone(simFeeNewPolicyDTO);
}

function simFeeCashbackAmtRules(key: any, maxCashbackAmt: any, period: any): FormItemRule[] {
  if (period.checkedList.includes(key)) {
    return [
      {
        required: true,
        message: '请输入',
        validator: async (value) => {
          if (Number(value) > Number(maxCashbackAmt)) {
            return Promise.reject(`注：下级必须低于或等于${maxCashbackAmt}元`);
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ];
  }
  return [];
}

function serviceCashbackAmtRules(key: any, maxCashbackAmt: any): FormItemRule[] {
  if (checkedServiceRules.value.includes(key)) {
    return [
      {
        required: true,
        message: '请输入',
        validator: async (value) => {
          if (Number(value) > Number(maxCashbackAmt)) {
            return Promise.reject(`注：下级必须低于或等于${maxCashbackAmt}元`);
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ];
  }
  return [];
}

function onSelectCashbackPolicy() {
  uni.navigateTo({
    url: '/pages-org/picker-view/cashback-policy/index',
  });

  emitter.on('picker-cashback-policy', (data: any) => {
    ruleForm.cashbackPolicyName = data.templateName;

    // 选中的
    const { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};
    // 全部的
    const { serviceFeePolicyDTOList: oriServiceFeePolicyDTOList, simFeeNewPolicyDTO: oriSimFeeNewPolicyDTO } = deepClone(oriPolicyDTOListMap);

    // 设置服务费
    checkedServiceRules.value = [];
    if (serviceFeePolicyDTOList.length) {
      oriServiceFeePolicyDTOList.forEach((i: any, index: number) => {
        i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
        if (!i.show) {
          i.cashbackAmt = 0;
          checkedServiceRules.value.push(i.configId);
        }
        serviceFeePolicyDTOList.forEach((j: any) => {
          if (j.configId === i.configId) {
            j.show = i.show;
            if (!j.show) {
              j.cashbackAmt = 0;
            }
            oriServiceFeePolicyDTOList[index] = j;
            if (!checkedServiceRules.value.includes(j.configId)) {
              checkedServiceRules.value.push(j.configId);
            }
          }
        });
      });
      ruleForm.serviceFeePolicyDTOList = oriServiceFeePolicyDTOList;
    }

    // 设置流量费
    const simFeePolicyMap = oriSimFeeNewPolicyDTO || {};
    const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
    simFeePolicyPeriodGroupMap.forEach((period: any) => {
      Object.keys(simFeePolicyMap).forEach((key) => {
        if (period.field === key) {
          period.data = simFeePolicyMap[key];
          period.checkedList = [];
          period.data.forEach((pItem: any, pIndex: number) => {
            pItem.show = Number(pItem.parentCashbackAmt) > 0 || !(Number(pItem.simFeeAmt) > 0);
            if (!pItem.show) {
              pItem.cashbackAmt = 0;
              period.checkedList.push(pItem.configId);
            }
            simFeeNewPolicyDTO[key]?.forEach((sItem: any) => {
              if (pItem.configId === sItem.configId) {
                sItem.show = pItem.show;
                if (!sItem.show) {
                  sItem.cashbackAmt = 0;
                }
                period.data[pIndex] = sItem;
                if (!period.checkedList.includes(sItem.configId)) {
                  period.checkedList.push(sItem.configId);
                }
              }
            });
          });
        }
      });
    });

    // 找出所有通道
    let allChannelCodes: any = [];
    try {
      const channelCodes = new Set(
        [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
          .flat(Infinity)
          .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
          .filter(code => code?.trim())
          .map(code => code.trim()),
      );
      allChannelCodes = [...channelCodes];
    }
    catch (error) {
      console.log(error);
    }

    const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
    allChannelCodes.forEach((item: any) => {
      const channelItem = channelList.value.find((channel: any) => channel.channelCode === item);
      policyConfigByChannelGroup.push({
        channelCode: item,
        channelName: channelItem?.channelName,
        channelData: deepClone(policyConfigByTerminalSourceDef),
      });
    });

    policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
      channel.channelData.forEach((tab) => {
        tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

        const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
          const periodData = period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);
          return {
            ...period,
            data: periodData,
            show: periodData.some((item: any) => item.show),
          };
        });
        tab.simFeeData = simFeeData;
      });
    });

    ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

    activeChannelTabKey.value = '0';
    activeTerminalSourceTabKey.value = '1';
  });
}

function onChangeOrgName() {
  if (settleForm.accountType === 'G') {
    settleForm.bankAccountName = form.agentName;
  }
}

function handleSyncLegalInfo() {
  form.contactsName = form.legalName;
  form.contactsCertNo = form.legalCertNo;
  form.contactsCertAddr = form.legalCertAddr;

  contactsDateRegion.value = deepClone(legalDateRegion.value);
  if (contactsDateRegion.value.length) {
    onConfirmContactsDateRegion();
  }
}

/** 选择营业执照&ocr */
async function onChooseBusinessLicense(value: string) {
  const res = await CommonApi.ocrBusinessLicense({ imgFile: value });
  if (res?.success) {
    Object.keys(res).forEach((key) => {
      res[key] = res[key] === 'FailInRecognition' ? '' : res[key];
    });

    const { regNum, name, address, establishDate, validPeriod } = res;
    form.agentName = name;
    form.agentSname = name;
    form.licenseNo = regNum;
    form.licenseAddr = address;

    if (establishDate && validPeriod) {
      let endDate = validPeriod;
      if (validPeriod === '长期') {
        endDate = '2099-12-31';
      }
      licenseDateRegion.value = [dayjs(establishDate).valueOf(), dayjs(endDate).valueOf()];
      onConfirmLicenseDateRegion();
    }

    settleForm.bankAccountName = name;
  }
}

/** 选择证件&ocr */
async function ocrIdcardByFile(file: string, item: any) {
  const { fileType } = item;
  const side = fileType === 1 ? 'face' : 'back';
  const data = await CommonApi.ocrIdCard({ imgFile: file, side });

  if (!data?.success)
    return;

  switch (side) {
    case 'face':
      form.legalName = data.name;
      form.legalCertNo = data.num;
      form.legalCertAddr = data.address;
      break;
    case 'back':
      // eslint-disable-next-line no-case-declarations
      let { startDate, endDate } = data;
      if (startDate && endDate) {
        if (endDate === '长期') {
          endDate = '2099-12-31';
        }
        legalDateRegion.value = [dayjs(startDate).valueOf(), dayjs(endDate).valueOf()];
        onConfirmLegalDateRegion();
      }
      break;

    default:
      break;
  }
}

/** 选择开户行总行 */
function onSelectBankType() {
  uni.navigateTo({ url: `/pages/picker-view/bank/bank-type` });
  emitter.on('picker-bank-type', (data: any) => {
    if (settleForm.typeCode !== data.typeCode) {
      settleForm.bankSubName = '';
      settleForm.bankChannelNo = '';
    }
    settleForm.typeName = data.typeName;
    settleForm.typeCode = data.typeCode;
  });
}

/** 选择开户行支行 */
function onSelectBankSub() {
  const { typeCode, bankProvince, bankCity } = settleForm;
  if (!bankCity)
    return Toast('请选择开户行所在地');
  if (!typeCode)
    return Toast('请选择开户行总行');

  const query = {
    typeCode,
    provinceCode: bankProvince,
    cityCode: bankCity,
  };
  uni.navigateTo({
    url: `/pages/picker-view/bank/bank-sub?where=${encodeURIComponent(JSON.stringify(query))}`,
  });

  emitter.on('picker-bank-sub', (data: any) => {
    settleForm.bankSubName = data.bankName;
    settleForm.bankChannelNo = data.clearChannelNo;
  });
}

/** 选择法人经营省市区 */
function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index?leaf=3' });
  emitter.on('picker-area', (data: any) => {
    const [province, city, country] = data;
    businessArea.value = data.map((item: any) => item.areaName).join('');
    form.provinceCode = province.areaCode;
    form.cityCode = city.areaCode;
    form.districtCode = country.areaCode;

    form.districtName = country.areaName;
    form.cityName = city.areaName;
    form.provinceName = province.areaName;
  });
}

/** 选择开户行省市 */
function onSelectBankArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index' });
  emitter.on('picker-area', (data: any) => {
    const [province, city] = data;
    const areaName = data.map((item: any) => item.areaName).join('');
    bankArea.value = areaName;
    settleForm.bankProvince = province.areaCode;
    settleForm.bankCity = city.areaCode;
  });
}

function onConfirmLicenseDateRegion() {
  const [startDate, endDate] = licenseDateRegion.value;
  form.licenseStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.licenseEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

function onConfirmLegalDateRegion() {
  const [startDate, endDate] = legalDateRegion.value;
  form.legalCertStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.legalCertEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

function onConfirmContactsDateRegion() {
  const [startDate, endDate] = contactsDateRegion.value;
  form.contactsCertStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.contactsCertEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

/*
 * 校验文件
 */
async function checkFile() {
  const fileList = Object.values(fileMap.value);
  const isCheckPass = fileList.every((f) => {
    if (f.required === false)
      return true;
    if (!f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}
</script>

<style lang="scss" scoped>
:deep(.custom-checkbox-group){
 background-color: transparent;

 .wd-checkbox{
   margin-bottom: 0;
  }
}

:deep(.custom-rule-group){
  .wd-cell__wrapper{
     justify-content: flex-start;
    }

  .rule-cell{
   .wd-cell__left{
    flex: none;
    flex-shrink: 0;
    max-width: 35%;
   }

   .wd-cell__right{
    flex: none;
   }

   .wd-input__inner{
    height: 30px;
   }
  }

  .rule-desc{
    .wd-cell__label{
      margin-top: 0;
    }

    .wd-cell__right{
      flex: none;
    }
  }
}
</style>
