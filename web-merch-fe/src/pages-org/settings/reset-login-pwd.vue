<template>
  <view>
    <view class="h-10px bg-primary" />

    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-input
          v-model="form.account" prop="account"
          label="手机号" placeholder="请输入手机号"
          align-right type="number"
        />
        <wd-input
          v-model="form.smsCode" prop="smsCode"
          label="验证码" placeholder="请输入验证码"
          type="number" use-suffix-slot align-right
        >
          <template #suffix>
            <view class="w-94px border-#f3f5f7 border-l-solid text-center">
              <text class="text-14px text-#4d80f0" @click="getSmsCode">
                {{ timer ? `${countdown}s后重发` : '获取验证码' }}
              </text>
            </view>
          </template>
        </wd-input>
        <wd-input
          v-model="form.password" prop="password"
          label="新密码" placeholder="请输入新密码"
          align-right show-password
        />
        <wd-input
          v-model="form.confirmPassword" prop="confirmPassword"
          label="确认密码" placeholder="请再次输入新密码"
          align-right show-password
        />
        <wd-cell />
      </wd-cell-group>
    </wd-form>

    <view class="p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        提交
      </wd-button>
    </view>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage } from 'wot-design-uni';
import { UserApi } from '@/api-org/user';
import { Toast } from '@/utils';
import { CommonApi } from '@/api/common';

const message = useMessage();

const loading = ref(false);

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive({
  account: '', // 手机号，限制11位 必填
  smsCode: '',
  password: '', // 登录密码
  confirmPassword: '',
});

const phonePattern = /^1[3-9]\d{9}$/;
const rules: FormRules = {
  account: [{ required: true, pattern: phonePattern, message: '请输入正确的手机号' }],
  smsCode: [{ required: true, message: '请输入验证码' }],
  password: [{ required: true, pattern: /[a-z0-9]{8,20}/i, message: '密码必须为8-20位字母+数字组合' }],
  confirmPassword: [
    { required: true, message: '请再次输入密码', validator: (value) => {
      if (value === form.password) {
        return Promise.resolve();
      }
      else {
        return Promise.reject('两次密码不一致');
      }
    } },
  ],
};

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  loading.value = true;

  // 提交
  UserApi.resetPassword(form)
    .then(() => {
      message.alert({
        msg: '操作成功, 请前往登录',
        title: '提示',
      }).then(() => {
        uni.reLaunch({ url: '/pages/common/login/index' });
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const timer = ref();
const countdown = ref(60);

function getSmsCode() {
  if (!phonePattern.test(form.account))
    return Toast('请输入正确的手机号');

  if (timer.value)
    return;

  CommonApi.sendSms({
    mobile: form.account,
    smsBusinessType: 4,
    smsSendSource: 1,
  })
    .then(() => {
      Toast('验证码已发送');
      startTimer();
    });
}

function startTimer() {
  if (timer.value)
    return;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value!);
      timer.value = null;
      countdown.value = 60;
    }
  }, 1000);
}
</script>
