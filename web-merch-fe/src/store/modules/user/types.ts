export interface UserState {
  info: {
    account?: string;
    email?: string;
    nickName?: string;
    orgCode?: string;
    orgType?: number;
    avatarUrl?: string;
    token?: string;
    appBrandType?: number;
    userId?: number;
    legalName?: string;
    companyName?: string;
  };
  // 用户类型: 机构 | 商户
  userType: 'org' | 'merch';
};

export type providerType =
  | 'weixin'
  | 'qq'
  | 'sinaweibo'
  | 'xiaomi'
  | 'apple'
  | 'univerify'
  | undefined;
