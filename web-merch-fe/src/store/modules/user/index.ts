import { defineStore } from 'pinia';
import type { UserState } from './types';
import { UserApi } from '@/api/user';
import { UserApi as OrgUserApi } from '@/api-org/user';
import { clearToken } from '@/utils';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    // 当前登录用户的信息
    info: {},
    // 当前登录用户类型
    userType: 'merch',
  }),
  getters: {},
  actions: {
    // 设置用户信息
    setInfo(partial: UserState['info']) {
      this.info = Object.assign(this.info, partial);
    },
    // 重置用户信息
    resetInfo() {
      this.info = {};
    },
    // 设置用户类型
    setUserType(userType: UserState['userType']) {
      this.userType = userType;
    },
    // 重置用户类型
    resetUserType() {
      this.userType = 'org';
    },
    // Logout
    async logout(driving = true) {
      if (driving) {
        if (this.userType === 'org') {
          await OrgUserApi.loginout();
        }
        else if (this.userType === 'merch') {
          await UserApi.loginout();
        }
      }

      this.resetInfo();
      clearToken();
      uni.reLaunch({ url: '/pages/common/login/index' });
    },
  },
  // 持久化配置
  persist: {
    enabled: true,
    H5Storage: window?.localStorage,
  },
});

export default useUserStore;
