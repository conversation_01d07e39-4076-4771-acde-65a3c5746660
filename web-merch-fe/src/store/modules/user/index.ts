import { defineStore } from 'pinia';
import type { UserState } from './types';
import { UserApi } from '@/api/user';
import { UserApi as OrgUserApi } from '@/api-org/user';
import { clearToken } from '@/utils';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    // 当前登录用户的信息
    info: {},
    // 当前登录用户类型
    userType: 'merch',
  }),
  getters: {},
  actions: {
    // 设置用户信息
    setInfo(partial: UserState['info']) {
      this.info = Object.assign(this.info, partial);
    },
    // 重置用户信息
    resetInfo() {
      this.info = {};
    },
    // 设置用户类型
    setUserType(userType: UserState['userType']) {
      this.userType = userType;
      // 根据用户类型动态设置tabBar
      this.updateTabBar();
    },
    // 重置用户类型
    resetUserType() {
      this.userType = 'org';
    },
    // Logout
    async logout(driving = true) {
      if (driving) {
        if (this.userType === 'org') {
          await OrgUserApi.loginout();
        }
        else if (this.userType === 'merch') {
          await UserApi.loginout();
        }
      }

      this.resetInfo();
      clearToken();
      uni.reLaunch({ url: '/pages/common/login/index' });
    },
    // 根据用户类型更新tabBar
    updateTabBar() {
      try {
        if (this.userType === 'org') {
          // 机构登录：隐藏提现tab（索引为1）
          uni.hideTabBarRedDot({ index: 1 });
          uni.setTabBarItem({
            index: 1,
            text: '',
            iconPath: 'static/tabbar/transparent.png', // 需要一个透明图标
            selectedIconPath: 'static/tabbar/transparent.png',
          });
          // 禁用提现tab的点击
          uni.setTabBarItem({
            index: 1,
            text: '',
          });
        } else if (this.userType === 'merch') {
          // 商户登录：显示提现tab
          uni.setTabBarItem({
            index: 1,
            text: '提现',
            iconPath: 'static/tabbar/home.png',
            selectedIconPath: 'static/tabbar/home-hover.png',
          });
        }
      } catch (error) {
        console.warn('设置tabBar失败:', error);
      }
    },
  },
  // 持久化配置
  persist: {
    enabled: true,
    H5Storage: window?.localStorage,
  },
});

export default useUserStore;
