/**
 * 数据传递相关类型定义
 */

/**
 * 传递数据项接口
 */
export interface TransferItem {
  /** 存储的数据 */
  data: any;
  /** 创建时间戳 */
  timestamp: number;
  /** 过期时间戳（可选） */
  expire?: number;
  /** 是否自动删除 */
  autoRemove: boolean;
}

/**
 * Transfer Store 状态接口
 */
export interface TransferState {
  /** 传递数据存储 */
  transferData: Record<string, TransferItem>;
}

/**
 * 传递选项接口
 */
export interface TransferOptions {
  /** 过期时间（毫秒） */
  expire?: number;
  /** 是否自动删除 */
  autoRemove?: boolean;
}

/**
 * 清理选项接口
 */
export interface CleanupOptions {
  // 预留接口，暂时为空
}

/**
 * 数据统计信息接口
 */
export interface DataStats {
  /** 总数据数量 */
  totalItems: number;
  /** 有效数据数量 */
  validItems: number;
  /** 过期数据数量 */
  expiredItems: number;
  /** 总数据大小（字节） */
  totalSize: number;
}
