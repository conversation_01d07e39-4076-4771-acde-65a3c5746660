<template>
  <view>
    <page-paging ref="pagingRef" v-model="dataSource" :default-page-size="20" @query="queryDataSource">
      <!-- 头部固定 -->
      <template #top>
        <view class="h-10px bg-primary" />
        <wd-search placeholder="请输入支行名称" placeholder-left hide-cancel @search="onSearch" />
      </template>

      <!-- 列表 -->
      <wd-cell-group border>
        <wd-cell v-for="(item, key) in dataSource" :key="key" clickable title-width="100%" @click="onSelect(item)">
          <template #title>
            <view class="font-medium">
              {{ item.bankName }}
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </page-paging>
  </view>
</template>

<script setup lang="ts">
import { CommonApi } from '@/api/common/index';
import { emitter } from '@/utils/emitter';

const dataSource = ref<any[]>([]);

const pagingRef = ref();

const where = reactive({
  typeCode: '', // 银行行别代码 必填
  provinceCode: '', // 省编码 必填
  cityCode: '', // 市编码 必填
  bankName: '', // 银行名称(支行) 支持模糊查询 选填
});

onLoad((option: any) => {
  const query = option.where ? JSON.parse(decodeURIComponent(option.where)) : {};
  Object.assign(where, query);
});

onUnmounted(() => {
  emitter.off('picker-bank-sub');
});

function onSelect(item: any) {
  emitter.emit('picker-bank-sub', item);
  uni.navigateBack();
}

function onSearch({ value } = { value: '' }) {
  where.bankName = value;
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  CommonApi.queryBankSub({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>
