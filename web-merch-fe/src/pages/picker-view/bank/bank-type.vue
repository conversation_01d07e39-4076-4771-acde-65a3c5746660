<template>
  <view>
    <page-paging ref="pagingRef" v-model="dataSource" :default-page-size="30" @query="queryDataSource">
      <!-- 头部固定 -->
      <template #top>
        <view class="h-10px bg-primary" />
        <wd-search placeholder="请输入总行名称" placeholder-left hide-cancel @search="onSearch" />
      </template>

      <!-- 列表 -->
      <wd-cell-group border>
        <wd-cell v-for="(item, key) in dataSource" :key="key" clickable title-width="100%" @click="onSelect(item)">
          <template #title>
            <view class="font-medium">
              {{ item.typeName }}
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </page-paging>
  </view>
</template>

<script setup lang="ts">
import { CommonApi } from '@/api/common/index';
import { emitter } from '@/utils/emitter';

const dataSource = ref<any[]>([]);

const pagingRef = ref();

const where = reactive({
  typeName: '', // 银行名称 支持模糊查询 选填
});

onUnmounted(() => {
  emitter.off('picker-bank-type');
});

function onSelect(item: any) {
  emitter.emit('picker-bank-type', item);
  uni.navigateBack();
}

function onSearch({ value } = { value: '' }) {
  where.typeName = value;
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  CommonApi.queryBankType({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>
