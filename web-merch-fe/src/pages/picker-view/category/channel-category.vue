<template>
  <view class="h-full flex flex-col overflow-hidden">
    <view class="flex-shrink-0">
      <view class="gap-primary" />
      <wd-search placeholder="请输入MCC编码或行业名称搜索" placeholder-left hide-cancel @search="onSearch" />
    </view>

    <view class="grow overflow-y-scroll">
      <wd-row custom-class="h-full">
        <wd-col :span="5" custom-class="h-full">
          <scroll-view
            scroll-y
            scroll-with-animation
            :show-scrollbar="false"
            :throttle="false"
            class="h-full"
          >
            <wd-sidebar v-model="mccBigClassValue" custom-class="!w-full bg-white">
              <view
                v-for="(item, key) in mccBigClass"
                :key="key" @click="queryMcc(item)"
              >
                <wd-sidebar-item
                  :value="item.mccBigClass"
                  :label="item.mccBigClass"
                />
              </view>
            </wd-sidebar>
          </scroll-view>
        </wd-col>

        <wd-col :span="7" custom-class="h-full">
          <scroll-view
            scroll-y
            scroll-with-animation
            :show-scrollbar="false"
            :scroll-top="scrollTop"
            :throttle="false"
            class="h-full"
          >
            <wd-sidebar v-model="mccSmallClassValue" custom-class="!w-full">
              <view
                v-for="(item, key) in mccSmallClass"
                :key="key" @click="queryMcc(item)"
              >
                <wd-sidebar-item
                  :value="item.mccSmallClass"
                  :label="item.mccSmallClass"
                />
              </view>
            </wd-sidebar>
          </scroll-view>
        </wd-col>

        <wd-col :span="12" custom-class="h-full">
          <scroll-view
            scroll-y
            scroll-with-animation
            :show-scrollbar="false"
            :scroll-top="scrollTop2"
            :throttle="false"
            class="h-full"
          >
            <wd-sidebar v-model="mccValue" custom-class="!w-full">
              <view
                v-for="(item, key) in mccMcc"
                :key="key" @click="onFinishSelect(item)"
              >
                <wd-sidebar-item
                  :value="item.mcc"
                  :label="item.mccName"
                />
              </view>
            </wd-sidebar>
          </scroll-view>
        </wd-col>
      </wd-row>
    </view>
  </view>
</template>

<script setup lang="ts">
import { CommonApi } from '@/api/common';
import { CHANNEL_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';

const mccBigClassValue = ref('');
const mccSmallClassValue = ref('');
const mccValue = ref('');

const mccBigClass = ref<any[]>([]);
const mccSmallClass = ref<any[]>([]);
const mccMcc = ref<any[]>([]);

const scrollTop = ref<number>(0);
const scrollTop2 = ref<number>(0);

onMounted(() => {
  queryMcc();
});

onUnmounted(() => {
  emitter.off('picker-category');
});

function onSearch({ value } = { value: '' }) {
  console.log(value);
}

/**
 * 查询MCC
 */
function queryMcc(parent: any = { level: 0 }) {
  const { level } = parent;

  CommonApi.channelMcc({
    channelCode: CHANNEL_CODE,
    codeType: 'UNION',
    mccBigClass: parent.mccBigClass || '',
    mccSmallClass: parent.mccSmallClass || '',
    mccName: parent.mccName || '',
  }).then((res: any) => {
    res = res || [];

    if (level === 0) {
      res.forEach((r: Record<string, any>) => {
        r.level = 1;
      });
      mccBigClass.value = res;
      const initItem = mccBigClass.value[0];
      if (initItem) {
        mccBigClassValue.value = initItem.mccBigClass;
        queryMcc(initItem);
      }
    }
    else {
      switch (level) {
        case 1:
          res.forEach((r: Record<string, any>) => {
            r.level = 2;
            r.mccBigClass = parent.mccBigClass;
          });
          mccSmallClass.value = res;
          // eslint-disable-next-line no-case-declarations
          const initItem = mccSmallClass.value[0];
          if (initItem) {
            mccSmallClassValue.value = initItem.mccSmallClass;
            queryMcc(initItem);
          }
          scrollTop.value = 0;
          scrollTop2.value = 0;
          break;
        case 2:
          mccMcc.value = res;
          mccValue.value = mccMcc.value[0]?.mcc;
          scrollTop2.value = 0;
          break;
      }
    }
  });
}

function onFinishSelect(item: any) {
  emitter.emit('picker-category', item);
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-sidebar-item{
    padding: 10px 12px !important;
    min-height: auto;
    line-height: normal;

   @apply !bg-white border-1 border-b-solid  border-r-solid border-#f7f8fa !rounded-0;
  }

  .wd-sidebar__padding{
    background-color: #fff !important;
    border-right: 1px solid #f7f8fa;
  }

  .wd-sidebar-item--active::before{
    display: none;
  }
}
</style>
