<template>
  <view class="h-full flex flex-col overflow-hidden">
    <view class="flex-shrink-0">
      <view class="gap-primary" />
      <wd-search placeholder="请输入MCC编码或行业名称搜索" placeholder-left hide-cancel @search="onSearch" />
    </view>

    <view class="grow overflow-y-scroll">
      <wd-row custom-class="h-full">
        <wd-col :span="7" custom-class="h-full">
          <scroll-view
            scroll-y
            scroll-with-animation
            :show-scrollbar="false"
            :throttle="false"
            class="h-full"
          >
            <wd-sidebar v-model="mccBigClassValue" custom-class="!w-full bg-white">
              <view
                v-for="(item, key) in mccBigClass"
                :key="key" @click="onChangeBigClass(item)"
              >
                <wd-sidebar-item
                  :value="item.mcc"
                  :label="item.mccName"
                />
              </view>
            </wd-sidebar>
          </scroll-view>
        </wd-col>

        <wd-col :span="17" custom-class="h-full">
          <scroll-view
            scroll-y
            scroll-with-animation
            :show-scrollbar="false"
            :scroll-top="scrollTop"
            :throttle="false"
            class="h-full"
          >
            <wd-sidebar v-model="mccSmallClassValue" custom-class="!w-full">
              <view
                v-for="(item, key) in mccSmallClass"
                :key="key" @click="onFinishSelect(item)"
              >
                <wd-sidebar-item
                  :value="item.mcc"
                  :label="item.mccName"
                />
              </view>
            </wd-sidebar>
          </scroll-view>
        </wd-col>
      </wd-row>
    </view>
  </view>
</template>

<script setup lang="ts">
import { CommonApi } from '@/api/common';
import { emitter } from '@/utils/emitter';

const mccBigClassValue = ref('');
const mccSmallClassValue = ref('');

const mccBigClass = ref<any[]>([]);
const mccSmallClass = ref<any[]>([]);

const scrollTop = ref<number>(0);

const whereSubClass = reactive({
  parentMcc: '', // 大类MCC编码 选填
  mccName: '', // 选填 如果为类别名称，则模糊匹配查询； 如果传递的是纯数字，则会根据mcc码进行精确查询
});

onMounted(() => {
  queryMcc();
});

onUnmounted(() => {
  emitter.off('picker-category');
});

function onSearch({ value } = { value: '' }) {
  whereSubClass.parentMcc = '';
  whereSubClass.mccName = value;
  querySmallClass();
}

/**
 * 查询MCC
 */
function queryMcc() {
  CommonApi.bigclass().then((res: any) => {
    res = res || [];

    mccBigClass.value = res;
    const initItem = mccBigClass.value[0];
    if (initItem) {
      mccBigClassValue.value = initItem.mcc;
      whereSubClass.parentMcc = initItem.mcc;
      querySmallClass();
    }
  });
}

async function querySmallClass() {
  scrollTop.value = 0;
  const data = await CommonApi.subclass(whereSubClass);
  mccSmallClass.value = (data || []) as any;
}

function onChangeBigClass(item: any) {
  mccBigClassValue.value = item.mcc;
  whereSubClass.parentMcc = item.mcc;
  querySmallClass();
}

function onFinishSelect(item: any) {
  emitter.emit('picker-category', item);
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-sidebar-item{
    padding: 10px 12px !important;
    min-height: auto;
    line-height: normal;

    @apply !bg-white border-1 border-b-solid  border-r-solid border-#f7f8fa !rounded-0;
  }

  .wd-sidebar__padding{
    background-color: #fff !important;
    border-right: 1px solid #f7f8fa;
  }

  .wd-sidebar-item--active::before{
    display: none;
  }
}
</style>
