<template>
  <view class="h-full flex flex-col overflow-hidden bg-primary">
    <view class="flex-shrink-0">
      <view class="h-10px bg-primary" />
      <wd-search placeholder="请输入MCC编码或行业名称搜索" placeholder-left hide-cancel @change="onSearch" />
    </view>
    <view class="flex-1 overflow-y-scroll">
      <wd-cell-group border>
        <wd-cell v-for="(item, key) in data" :key="key" clickable title-width="100%" @click="onSelect(item)">
          <template #title>
            <view class="font-medium">
              {{ item.mccName }}
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>
  </view>
</template>

<script setup lang="ts">
import { CommonApi } from '@/api/common/index';
import { deepClone } from '@/utils';
import { emitter } from '@/utils/emitter';

interface MccItem {
  mcc: string; // 行业编码
  mccName: string; // 行业名称
  mccDescription: string; // 行业描述
}

const where = reactive({
  channelCode: '', // 通道编码 必填
});

const dataSource = ref<MccItem[]>([]);
const data = ref<MccItem[]>([]);

onLoad((query) => {
  where.channelCode = query?.channelCode || '';
});

onMounted(() => {
  getDataSource();
});

onUnmounted(() => {
  emitter.off('picker-category');
});

function onSearch({ value } = { value: '' }) {
  data.value = deepClone(filterData(value));
}

function onSelect(item: any) {
  emitter.emit('picker-category', item);
  uni.navigateBack();
}

/**
 * 过滤数据
 */
function filterData(value: string) {
  if (!value)
    return dataSource.value;

  return data.value.filter((item: MccItem) => {
    return item.mccName.includes(value) || item.mcc.includes(value);
  });
}

/**
 * 获取数据
 */
async function getDataSource() {
  const data = await CommonApi.storeMcc({ ...where });
  dataSource.value = (data || []) as MccItem[];

  onSearch();
}
</script>
