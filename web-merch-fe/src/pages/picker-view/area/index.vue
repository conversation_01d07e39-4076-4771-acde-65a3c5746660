<template>
  <view class="h-full flex flex-col overflow-hidden">
    <view v-if="showNavbar" class="shrink-0">
      <wd-navbar title="地区选择" safe-area-inset-top left-arrow @click-left="handleClickLeft" />
    </view>

    <view class="gap-primary shrink-0" />
    <view class="grow">
      <wd-col-picker
        ref="areaPicker"
        v-model="value"
        :columns="area"
        :column-change="columnChange"
        size="large"
        label-key="areaName"
        value-key="areaCode" custom-class="h-full relative"
        use-default-slot
        @confirm="onFinishSelectArea"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import type { ColPickerColumnChange } from 'wot-design-uni/components/wd-col-picker/types';
import { CommonApi } from '@/api/common';
import type { AreaResult } from '@/api/common/types';
import { emitter } from '@/utils/emitter';

const areaPicker = ref();

const leaf = ref(2);

const value = ref([]);
const area = ref<AreaResult[][]>([[]]);

const showNavbar = ref(true);

onLoad((query) => {
  leaf.value = query?.leaf ? Number(query?.leaf) : leaf.value;

  if (query?.embe === '1') {
    showNavbar.value = false;
  }
});

onMounted(() => {
  areaPicker.value.open();
  queryProvince();
});

const columnChange: ColPickerColumnChange = async ({ selectedItem, resolve, index, finish }) => {
  const isLastCol = index === leaf.value - 1;

  if (isLastCol) {
    finish('' as any);
    areaPicker.value.open();
  }
  else {
    const { areaCode } = selectedItem;
    let list: AreaResult[] = [];
    if (index === 0) {
      list = await queryCity(areaCode);
    }
    else if (index === 1) {
      list = await queryCountry(areaCode);
    }
    resolve(list);
  }
};

onUnmounted(() => {
  emitter.off('picker-area');
});

/**
 * 查询省
 */
function queryProvince() {
  CommonApi.queryProvince().then((res) => {
    area.value[0] = res || [];
  });
}

/**
 * 查询市
 */
async function queryCity(parentCode: string) {
  const data = await CommonApi.queryCity({ parentCode });
  return data || [];
}

/**
 * 查询区
 */
async function queryCountry(parentCode: string) {
  const data = await CommonApi.queryCountry({ parentCode });
  return data || [];
}

function onFinishSelectArea({ selectedItems }: { selectedItems: AreaResult[] }) {
  const areaItems = [...selectedItems];

  emitter.emit('picker-area', areaItems);
  uni.navigateBack();
}

function handleClickLeft() {
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-overlay{
    display: none;
    transition:none !important;
  }

  .wd-action-sheet__popup{
    border-radius: 0;
    transition:none !important;
  }

  .wd-popup{
    position: absolute !important;
    top: 0;
    overflow: hidden;
  }

  .wd-action-sheet{
    height: 100%;
  }

  .wd-col-picker__list-container{
    height: 100%;
  }

  .wd-col-picker__list{
    height: 100% !important;
}

  .wd-action-sheet__header{
    display: none;
  }
}
</style>
