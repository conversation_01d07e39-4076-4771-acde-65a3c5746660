<template>
  <view>
    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-input
          v-model="form.terminalSn"
          prop="terminalSn"
          label="序列号"
          label-width="60px"
          placeholder="请输入或扫描机器背后的条形码"
          clearable use-suffix-slot align-right
        >
          <template #suffix>
            <i
              class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
              @click="scanCode"
            />
          </template>
        </wd-input>
        <wd-cell />
      </wd-cell-group>
      <view class="mt40px px-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          下一步
        </wd-button>
      </view>
    </wd-form>

    <!-- H5扫码组件 -->
    <!-- #ifdef H5 -->
    <cshaptx4869-scancode
      v-if="showH5ScanCode"
      @success="onScanSuccess"
      @fail="onScanFail"
      @close="onScanClose"
    />
    <!-- #endif -->

    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { CHANNEL_CODE } from '../../../config/setting';
import { TerminalApi } from '@/api/terminal';

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive({
  terminalSn: '', // 终端SN必填
});
// 规则
const rules: FormRules = {
  terminalSn: [{ required: true, message: '请输入序列号' }],
};

const showH5ScanCode = ref(false);

onBackPress((options) => {
  if (options.from === 'backbutton') {
    onBeforeBackPress();
    return true;
  }
});

const isControlAppletBack = ref(true);
onUnload(() => {
  // #ifdef MP-WEIXIN
  if (isControlAppletBack.value)
    onBeforeBackPress();
  // #endif
});

// 返回处理
function onBeforeBackPress() {
  uni.redirectTo({ url: '/pages/report/merch-report/index' });
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  const data = await TerminalApi.fetchInfoByTerminalSn(form);
  if (data?.channelCode === CHANNEL_CODE) {
    isControlAppletBack.value = false;
    uni.redirectTo({ url: `/pages/report/merch-dredge/index?handleType=4&terminalSn=${form.terminalSn}` });
  }
  else {
    toast.error('终端SN无效');
  }
}

function scanCode() {
  // #ifdef H5
  showH5ScanCode.value = true;
  // #endif
  // #ifndef H5
  uni.scanCode({
    success: (res) => {
      form.terminalSn = res.result || '';
    },
  });
  // #endif
}

// #region h5扫码相关
function onScanSuccess(res: string) {
  showH5ScanCode.value = false;
  form.terminalSn = res || '';
}
function onScanFail(err: { errName: string;errMsg: string }) {
  uni.showModal({
    title: '提示',
    content: '浏览器不支持, 请您手动输入或切换浏览器重试哦~',
    showCancel: false,
    complete: () => {
      showH5ScanCode.value = false;
    },
  });
  console.error(err.errName, err.errMsg);
}
function onScanClose() {
  showH5ScanCode.value = false;
}
// #endregion
</script>
