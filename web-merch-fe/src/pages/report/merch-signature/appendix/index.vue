<template>
  <view class="h-full overflow-y-scroll">
    <web-view :src="pdfSource" />
  </view>
</template>

<script setup lang="ts">
import { decodeUrlParams } from '@/utils';

const pdfSource = ref<string>('');

onLoad((params: any) => {
  params = decodeUrlParams(params);

  if (params.url)
    pdfSource.value = params.url;

  if (params.title) {
    uni.setNavigationBarTitle({
      title: params.title,
    });
  }
});
</script>
