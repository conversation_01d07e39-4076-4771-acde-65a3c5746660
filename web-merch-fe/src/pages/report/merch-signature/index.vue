<template>
  <view class="signature-wrap">
    <view class="signature-contain">
      <view class="signature-main">
        <!-- 标题提示 -->
        <view class="signature-title">
          <text v-for="(t, key) in [...'请在框内签名']" :key="key">
            {{ t }}
          </text>
        </view>

        <!-- 签名组件 -->
        <view class="w-full grow border border-#969799 rounded border-dashed bg-white">
          <l-signature ref="signatureRef" disable-scroll prefer-to-data-u-r-l landscape styles="height:100%" :pen-size="5" />
        </view>

        <!-- 操作按钮 -->
        <view class="signature-btns">
          <view class="btn btn-clear" @click="onClick('clear')">
            <text>清</text><text>空</text>
          </view>
          <view class="btn btn-clear" @click="onClick('undo')">
            <text>撤</text><text>销</text>
          </view>
          <view class="btn btn-ok" @click="onClick('save')">
            <text>完</text><text>成</text>
          </view>
        </view>
      </view>
    </view>
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import { useToast } from 'wot-design-uni';
import { CommonApi } from '@/api/common';

const toast = useToast();

const signatureRef = ref();

// 事件类型: 清空 | 撤销 | 保存
type SignatureEvents = 'clear' | 'undo' | 'save';

onBackPress((options) => {
  if (options.from === 'backbutton') {
    onBeforeBackPress();
    return true;
  }
});

onUnload(() => {
  // #ifdef MP-WEIXIN
  onBeforeBackPress();
  // #endif
});

// 返回处理
function onBeforeBackPress() {
  uni.redirectTo({ url: '/pages/report/merch-report/index' });
}

function onClick(type: SignatureEvents) {
  if (!signatureRef.value)
    return;

  switch (type) {
    case 'save':
      signatureRef.value.canvasToTempFilePath({
        success: async (res: any) => {
          if (res.isEmpty) {
            toast.warning('您还没有签名哦~');
            return;
          }
          // base64
          await CommonApi.uploadImages({
            imageList: [
              {
                fileType: 14,
                fileData: res.tempFilePath.split(',')[1],
                suffixType: 'png',
              },
            ],
          },
          );
          uni.redirectTo({ url: `/pages/report/merch-dredge/index?handleType=2` });
        },
      });
      break;
    default:
      signatureRef.value[type]();
  }
}
</script>

<!-- eslint-disable style/no-tabs -->
<style lang="scss" scoped>
	.signature-wrap {
    overflow: hidden;
    padding: 30rpx 0;
		width: 100%;
		height: 100%;
    background-color: #f8f9fa;
	}

	.signature-contain {
		width: 100%;
    height: 100%;

		.signature-main {
			display: flex;
			align-items: stretch;
      height: 100%;
			flex-direction: row-reverse;
		}

		.signature-title {
			display: flex;
			justify-content: left;
			padding: 20rpx;
			height: 100rpx;
			font-size: 28rpx;
			flex-direction: column;

			text {
        color: #666;
				transform: rotate(90deg);
			}
		}

		.signature-btns {
			display: flex;
			justify-content: space-around;
			align-items: center;
			padding: 20rpx;
			flex-direction: column;

			.btn {
				display: flex;
				padding: 50rpx 20rpx;
				margin: 0;
				height: 200rpx;
				font-size: 14px;
				text-align: center;
				text-decoration: none;
				border: 1px solid #eee;
				border-radius: 10rpx;
				place-content: center center;
				flex-shrink: 0;
				flex-direction: column;

				text {
					transform: rotate(90deg);
				}

				&.btn-clear {
					flex-basis: 100rpx;
          color: #888;
				}

				&.btn-ok {
					color: #fff;
          background-color: #4d80f0;
				}
			}
		}
	}
</style>
