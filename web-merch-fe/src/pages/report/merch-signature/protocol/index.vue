<template>
  <view class="h-full w-full flex flex-col overflow-hidden bg-primary">
    <view class="grow overflow-y-scroll">
      <wd-cell-group border title="协议信息" custom-class="mt-10px">
        <wd-cell title="查看协议" icon="folder" is-link label="请阅读协议内容后再去签署哦~" @click="onViewFile('0')" />
      </wd-cell-group>
      <wd-cell-group border title="附件信息" custom-class="mt-10px">
        <wd-cell title="附件1" icon="link" is-link @click="onViewFile('1')" />
        <wd-cell title="附件2" icon="link" is-link @click="onViewFile('2')" />
      </wd-cell-group>
    </view>

    <view class="shrink-0 pt-20rpx">
      <view class="mb-20rpx px-40rpx">
        <wd-button block @click="onClickSign">
          签署协议
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { Dialog, buildUrlWithParams } from '@/utils';

function onClickSign() {
  uni.navigateTo({ url: '/pages/report/merch-signature/index' });
}

function onViewFile(type: string) {
  const params = {
    url: '',
    title: '',
  };

  switch (type) {
    case '0':
      params.url = 'https://wsboss.chinaebi.com/api/sysFileInfo/previewByObjectName?fileBucket=chinaebiProtocolTemplate&fileObjectName=xy4.pdf';
      params.title = '协议';
      break;
    case '1':
      params.url = 'https://wsboss.chinaebi.com/api/sysFileInfo/previewByObjectName?fileBucket=chinaebiProtocolTemplate&fileObjectName=xy2.pdf';
      params.title = '附件1';
      break;
    case '2':
      params.url = 'https://wsboss.chinaebi.com/api/sysFileInfo/previewByObjectName?fileBucket=chinaebiProtocolTemplate&fileObjectName=xy3.pdf';
      params.title = '附件2';
      break;
  }

  // #ifndef H5
  uni.downloadFile({
    url: params.url,
    success(res) {
      const filePath = res.tempFilePath;
      uni.openDocument({
        filePath,
        showMenu: true,
        fail() {
          Dialog('文件打开失败');
        },
      });
    },
    fail() {
      Dialog('文件下载失败');
    },
  });
  // #endif

  // #ifdef H5
  const url = buildUrlWithParams('/pages/report/merch-signature/appendix/index', params);
  uni.navigateTo({
    url,
  });
  // #endif
}

onBackPress((options) => {
  if (options.from === 'backbutton') {
    onBeforeBackPress();
    return true;
  }
});

onUnload(() => {
  // #ifdef MP-WEIXIN
  onBeforeBackPress();
  // #endif
});

// 返回处理
function onBeforeBackPress() {
  uni.redirectTo({ url: '/pages/report/merch-report/index' });
}
</script>
