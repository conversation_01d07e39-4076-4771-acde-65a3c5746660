<template>
  <wd-popup :model-value="visible" custom-class=" rounded-xl" :close-on-click-modal="false" @update:model-value="updateVisible">
    <view class="w-75vw pb-15px">
      <view class="flex flex-col items-center">
        <image
          :src="props.data?.authImgUrl"
          mode="widthFix"
          class="w-full"
        />
        <view v-if="props.data?.merchId" class="mt-10px flex items-center px-30rpx">
          <text>子商户号: </text>
          <text>{{ props.data?.merchId }}</text>
          <i class="i-mdi-content-copy ml-2px size-28rpx text-#b51e1e" @click="copyContent(props.data?.merchId)" />
        </view>
        <text class="mt-10px text-26rpx text-#888">
          使用微信扫一扫, 完成认证
        </text>
        <view class="mt-20px w-full px-60rpx">
          <wd-button type="info" block @click="updateVisible(false)">
            关闭
          </wd-button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';

interface DataType {
  authImgUrl: string;
  merchId: string;
}

const props = defineProps({
  visible: Boolean,
  data: Object as PropType<DataType>,
});

const emit = defineEmits([
  'update:visible',
]);

function updateVisible(value: boolean) {
  emit('update:visible', value);
}

function copyContent(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>
