<template>
  <view>
    <view class="gap-primary" />
    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-picker
          v-model="form.channelCode"
          prop="channelCode"
          label="支付通道"
          label-width="100px"
          placeholder="支付通道"
          :columns="channelColumns"
          align-right
          readonly
        />
        <wd-picker
          v-model="form.chnMerchNo"
          prop="chnMerchNo"
          label="通道商户"
          label-width="100px"
          placeholder="通道商户"
          :columns="channelMerchColumns"
          align-right
          readonly
        />
        <wd-input
          v-model="form.terminalSn"
          prop="terminalSn"
          label="序列号"
          label-width="60px"
          placeholder="请输入或扫描机器背后的条形码"
          clearable use-suffix-slot align-right
        >
          <template #suffix>
            <i
              class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
              @click="scanCode"
            />
          </template>
        </wd-input>
        <wd-cell />
      </wd-cell-group>

      <view class="mt40px px-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          绑定
        </wd-button>
      </view>
    </wd-form>

    <!-- H5扫码组件 -->
    <!-- #ifdef H5 -->
    <cshaptx4869-scancode
      v-if="showH5ScanCode"
      @success="onScanSuccess"
      @fail="onScanFail"
      @close="onScanClose"
    />
    <!-- #endif -->

    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { TerminalApi } from '@/api/terminal';
import { MerchReportApi } from '@/api/report';
import { CommonApi } from '@/api/common';

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive<any>({
  channelCode: '', // 通道编码必填
  terminalSn: '', // 终端SN必填
  chnMerchNo: '', // 通道商户号必填
});
// 规则
const rules: FormRules = {
  channelCode: [{ required: true, message: '请选择支付通道' }],
  chnMerchNo: [{ required: true, message: '请选择通道商户' }],
  terminalSn: [{ required: true, message: '请输入序列号' }],
};

const channelMerchColumns = ref<any[]>([]);

const channelColumns = ref<any>([]);

const showH5ScanCode = ref(false);

onLoad((query: any) => {
  form.channelCode = query.channelCode || '';
  form.chnMerchNo = query.chnMerchNo || '';

  getChannelList();
  queryChannelMerch();
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  await TerminalApi.blankSnBindSave(form);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

async function getChannelList() {
  let data = await CommonApi.getChannelList();
  data = data || [];

  const formatData = data.map((item: any) => ({
    label: item.channelName,
    value: item.channelCode,
  }));
  channelColumns.value = formatData;
}

async function queryChannelMerch() {
  const data = await MerchReportApi.findPage({ channelCode: form.channelCode, reportRespCode: 2, pageNo: 1, pageSize: 1000 });
  const filterData = data?.rows.map((item: any) => {
    return {
      label: item.chnMerchName,
      value: item.chnMerchNo,
    };
  });
  channelMerchColumns.value = filterData;
}

function scanCode() {
  // #ifdef H5
  showH5ScanCode.value = true;
  // #endif
  // #ifndef H5
  uni.scanCode({
    success: (res) => {
      form.terminalSn = res.result || '';
    },
  });
  // #endif
}

// #region h5扫码相关
function onScanSuccess(res: string) {
  showH5ScanCode.value = false;
  form.terminalSn = res || '';
}
function onScanFail(err: { errName: string;errMsg: string }) {
  uni.showModal({
    title: '提示',
    content: '浏览器不支持, 请您手动输入或切换浏览器重试哦~',
    showCancel: false,
    complete: () => {
      showH5ScanCode.value = false;
    },
  });
  console.error(err.errName, err.errMsg);
}
function onScanClose() {
  showH5ScanCode.value = false;
}
// #endregion
</script>
