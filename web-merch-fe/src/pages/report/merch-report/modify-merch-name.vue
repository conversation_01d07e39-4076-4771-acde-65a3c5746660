<template>
  <view>
    <view class="gap-primary" />
    <wd-form
      ref="formRef" :model="form" :rules="rules"
      custom-class="error-message__align-right"
    >
      <wd-cell-group border>
        <wd-input
          v-model="form.shortName" prop="shortName"
          label="商户简称" placeholder="请输入2-10位字符,不要出现省市区" label-width="100px"
          clearable align-right
        />
        <wd-cell />
      </wd-cell-group>

      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { CHANNEL_CODE } from '@/config/setting';
import { MerchReportApi } from '@/api/report';

const toast = useToast();

// 表单
const form = reactive({
  channelCode: CHANNEL_CODE, // 通道编号
  chnMerchNo: '', // 通道商户编号
  shortName: '', // 商户简称
});
// 校验规则
const validatorShortName = (val: string) => {
  if (String(val).length >= 2 && String(val).length <= 10) {
    return Promise.resolve();
  }
  else {
    return Promise.reject();
  }
};
const rules: FormRules = {
  shortName: [{ required: true, validator: validatorShortName, message: '请输入2-10位字符' }],
};
const formRef = ref<FormInstance | null>(null);

onLoad((query) => {
  form.chnMerchNo = query?.chnMerchNo || '';
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  await MerchReportApi.uptChnMerchAbbr(form);
  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}
</script>
