<template>
  <view class="h-full overflow-y-scroll">
    <view class="p-40rpx">
      <view class="card-shadow rounded-3 px-30rpx">
        // #ifdef H5
        <img v-if="path" :src="path" class="w-full object-contain">
        // #endif
        // #ifndef H5
        <image v-if="path" :src="path" class="w-full" mode="widthFix" />
        // #endif
        <l-painter
          v-if="showPainter && !path"
          css="padding-bottom: 80rpx; background-color: #fff"
          path-type="url"
          is-canvas-to-temp-file-path
          @success="onPainerSuccess"
        >
          <!-- 顶部 -->
          <l-painter-view css="padding: 40rpx; border-bottom: 1px dashed #ddd;">
            <l-painter-image
              src="@/static/images/logo.jpg"
              css="width: 88rpx; height: 88rpx; border-radius: 16rpx;"
            />
            <l-painter-view
              css="display: inline-block; padding-left: 60rpx;"
            >
              <l-painter-text
                text="开通D0"
                css="display: block; padding-bottom: 10rpx; color: #000; font-size: 36rpx; fontWeight: bold"
              />
              <l-painter-text
                text="微信扫一扫, 人脸认证开通D0"
                css="color: #666; font-size: 24rpx"
              />
            </l-painter-view>
          </l-painter-view>

          <!-- 二维码区域 -->
          <l-painter-view css="margin-top: 40rpx;">
            <l-painter-qrcode
              css="width: 300rpx; height: 300rpx; margin:0 auto;"
              :text="qrcodeLink"
            />
          </l-painter-view>
        </l-painter>
      </view>
    </view>

    <view v-if="path" class="px-60rpx">
      <wd-button plain block @click="handleCopyLink">
        复制二维码链接
      </wd-button>

      <view class="mt-30rpx">
        // #ifndef H5
        <wd-button block @click="handleSaveQRcode">
          保存二维码
        </wd-button>
        // #endif

        // #ifdef H5
        <view class="text-center text-28rpx text-#666">
          长按二维码保存图片到相册
        </view>
        // #endif
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useClipboard } from '@/hooks';
import { decodeUrlParams } from '@/utils';

// canvas生成的图片地址 临时路径
const path = ref('');

// 拓展码链接
const qrcodeLink = ref('');

const showPainter = computed(() => {
  return !!qrcodeLink.value;
});

onLoad((query: any) => {
  query = decodeUrlParams(query);
  qrcodeLink.value = query?.authUrl || '';
});

function onPainerSuccess(res: any) {
  path.value = res;
}

function handleCopyLink() {
  useClipboard().setClipboardData({ data: qrcodeLink.value });
}

function handleSaveQRcode() {
  uni.saveImageToPhotosAlbum({
    filePath: path.value,
    success() {
      uni.showToast({
        title: '二维码已保存到相册',
        icon: 'success',
      });
    },
  });
}
</script>

<style lang="scss" scoped>
.card-shadow{
  box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
}
</style>
