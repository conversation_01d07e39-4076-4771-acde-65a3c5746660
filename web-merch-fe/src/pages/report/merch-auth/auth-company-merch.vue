<template>
  <view class="h-full overflow-hidden bg-primary">
    <scroll-view
      scroll-y :show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
      class="h-full"
    >
      <view class="p-12px text-14px font-medium">
        企业信息
      </view>
      <view class="bg-white py-12px">
        <view class="mb-12px px-12px text-14px">
          营业执照照片
        </view>
        <view v-for="(item, key) in [fileMap[7]]" :key="key" class="w-50%">
          <GivenUpload
            v-model:file-data="item.fileData"
            :file-name="item.fileName"
            :placeholder="item.placeholder"
            custom-class="w-full"
            @choose="onChooseBusinessLicense"
          />
        </view>
      </view>

      <wd-form ref="formRef" :model="form" :rules="rules">
        <wd-cell-group border>
          <wd-picker v-model="form.merchantGrade" prop="merchantGrade" label="商户类型" label-width="100px" :show-confirm="false" :columns="merchantGradeColumns" align-right />
          <wd-input
            v-model="form.companyName" prop="companyName"
            label="企业名称" placeholder="可图片自动识别" label-width="100px"
            clearable align-right
          />
          <wd-textarea
            v-model="form.licenseNo" prop="licenseNo"
            label="统一社会信用代码" placeholder="可图片自动识别"
            clearable auto-height
            custom-textarea-class="text-right"
          />
          <wd-datetime-picker
            v-model="licenseDateRegion" prop="licenseEndDate"
            label="营业执照有效期"
            type="date" align-right
            :min-date="minDateLicense" :max-date="maxDateLicense"
            :default-value="defaultDateRegionValue"
            @confirm="onConfirmLicenseDateRegion"
          />
          <wd-textarea
            v-model="form.licenseAddr" prop="licenseAddr"
            label="企业注册地址" placeholder="请输入企业注册地址" label-width="100px"
            clearable auto-height
            custom-textarea-class="text-right"
          />
          <wd-textarea
            v-model="form.licenseOperateRange" prop="licenseOperateRange"
            label="经营范围" placeholder="可图片自动识别" label-width="100px"
            clearable
            custom-textarea-class="text-right"
          />
          <wd-cell
            title="经营所在地区" title-width="100px"
            prop="country" is-link
            @click="onSelectArea"
          >
            <wd-textarea
              v-model="bankArea"
              placeholder="请选择省/市/区"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
          <wd-textarea
            v-model="form.positionAddr" prop="positionAddr"
            label="经营详细地址" placeholder="请输入经营详细地址" label-width="100px"
            auto-height
            custom-textarea-class="text-right"
          />
          <wd-cell
            title="经营类目" title-width="100px" prop="mcc" is-link
            @click="onSelectUnionMcc"
          >
            <wd-textarea
              v-model="form.mccName"
              placeholder="请选择MCC"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
        </wd-cell-group>

        <view class="p-12px text-14px font-medium">
          法人信息 (营业执照上的经营者即法人)
        </view>
        <wd-cell-group border>
          <view class="bg-white py-12px">
            <view class="mb-12px px-12px text-14px">
              法人身份证照片
            </view>
            <view class="flex">
              <view v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" class="w-50%">
                <GivenUpload
                  v-model:file-data="item.fileData"
                  :file-name="item.fileName"
                  :placeholder="item.placeholder"
                  custom-class="w-full"
                  @choose="(file) => ocrIdcardByFile(file, item)"
                />
              </view>
            </view>
          </view>

          <wd-input
            v-model="form.legalName" prop="legalName"
            label="法人姓名" placeholder="可图片自动识别" label-width="100px"
            align-right
          />
          <wd-input
            v-model="form.legalCertNo" prop="legalCertNo"
            label="法人身份证号" placeholder="可图片自动识别" label-width="100px"
            align-right
          />
          <wd-datetime-picker
            v-model="legalDateRegion" prop="legalCertEndDate"
            label="证件有效期" label-width="100px"
            type="date" :min-date="minDate" :max-date="maxDate" align-right
            :default-value="defaultDateRegionValue"
            @confirm="onConfirmLegalDateRegion"
          />
          <wd-textarea
            v-model="form.legalAddr" prop="legalAddr"
            label="法人居住地址" placeholder="请输入法人居住地址" label-width="100px"
            auto-height
            custom-textarea-class="text-right"
          />
          <wd-input
            v-model="form.legalCertTel" prop="legalCertTel"
            label="法人联系电话" placeholder="请输入联系手机号" label-width="100px"
            align-right
          />
        </wd-cell-group>

        <view class="p-40rpx">
          <wd-button type="primary" size="large" block @click="save">
            提交
          </wd-button>
        </view>
      </wd-form>
    </scroll-view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common';
import { CHANNEL_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';
import { MerchReportApi } from '@/api/report';

const toast = useToast();
const message = useMessage();

// 表单
const form = reactive({
  companyName: '', // * 企业名称 必填
  merchantGrade: '', // 商户类型 A-企业 B-个体工商户 C-小微商户 必填
  licenseNo: '', // 统一社会信用代码 必填
  licenseName: '', // 营业执照全称 必填
  licenseAddr: '', // 注册地址 必填
  licenseStartDate: '', // 营业执照有效期（开始：yyyy-MM-dd） 必填
  licenseEndDate: '', // 营业执照有效期（结束：yyyy-MM-dd） 必填
  licenseOperateRange: '', // 营业执照经营范围 必填

  // 证件信息
  legalCertNo: '', // 证件号码 必填，用来与之前的比对 是否一致
  legalName: '', // 姓名 必填
  legalAddr: '', // 地址 必填
  legalSex: '', // 性别 必填
  legalCertStartDate: '', // 证件有效期（开始：yyyy-MM-dd）必填
  legalCertEndDate: '', // 证件有效期（结束：yyyy-MM-dd） 必填
  legalCertTel: '', // 联系电话 必填

  mcc: '', // 行业类别编码 （MCC行业类型API查询）
  mccName: '',

  // 经营地址
  province: '', // 省编码
  city: '', // 市编码
  country: '', // 区县 编码
  positionAddr: '', //* 街道地址/定位地址（省+市+街道定位）
  longitude: '', // * 经度（地理位置x）
  latitude: '', // * 纬度（地理位置y）

  imageJsonList: [],
});
// 规则
const rules: FormRules = {
  companyName: [{ required: true, message: '请填写企业名称' }],
  merchantGrade: [{ required: true, message: '请选择商户类型' }],
  licenseNo: [{ required: true, message: '请填写营业执照号码' }],
  licenseAddr: [{ required: true, message: '请填写企业注册地址' }],
  licenseEndDate: [{ required: true, message: '请选择营业执照有效期' }],
  licenseOperateRange: [{ required: true, message: '请填写经营范围' }],

  country: [{ required: true, message: '请选择经营所在省市区' }],
  positionAddr: [{ required: true, message: '请填写经营详细地址' }],
  mcc: [{ required: true, message: '请选择经营类目' }],

  legalCertNo: [{ required: true, message: '请填写证件号码' }],
  legalName: [{ required: true, message: '请填写姓名' }],
  legalAddr: [{ required: true, message: '请填写居住地址' }],
  legalCertEndDate: [{ required: true, message: '请选择证件有效期' }],
  legalCertTel: [{ required: true, message: '请填写联系电话' }],
};
const formRef = ref<FormInstance | null>(null);

// 文件
type FileType = 1 | 2 | 7 ;
const fileMap = ref <Record<FileType, GivenUploadProps>> ({
  1: {
    fileName: '人像面',
    placeholder: require('@/static/images/card_face.png'),
    fileData: '',
    fileType: 1,
  },
  2: {
    fileName: '国徽面',
    placeholder: require('@/static/images/card_back.png'),
    fileData: '',
    fileType: 2,
  },
  7: {
    fileName: '营业执照',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 7,
  },
});

const licenseDateRegion = ref<any>([]);
const defaultDateRegionValue = ref([dayjs().valueOf(), dayjs().valueOf()]);

const legalDateRegion = ref<any>([]);

const minDate = dayjs('1900-01-01').valueOf();
const maxDate = dayjs('2099-12-31').valueOf();

// 营业执照有效期选择范围
const minDateLicense = dayjs().subtract(50, 'year').valueOf();
const maxDateLicense = dayjs('2099-12-31').valueOf();

const bankArea = ref('');

const scrollTop = ref(0);

const merchantGradeColumns = [
  {
    label: '企业',
    value: 'A',
  },
  {
    label: '个体工商户',
    value: 'B',
  },
];

onBackPress((options) => {
  if (options.from === 'backbutton') {
    onBeforeBackPress();
    return true;
  }
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageList = await uploadFile();
  form.imageJsonList = imageList as any;

  await MerchReportApi.enterpriseAuth(form);
  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.reLaunch({ url: '/pages/report/merch-dredge/index?handleType=1' });
    },
  });
}

/** 选择营业执照&ocr */
async function onChooseBusinessLicense(value: string) {
  const res = await CommonApi.ocrBusinessLicense({ imgFile: value });
  if (res?.success) {
    Object.keys(res).forEach((key) => {
      res[key] = res[key] === 'FailInRecognition' ? '' : res[key];
    });

    const { regNum, name, address, establishDate, validPeriod, business } = res;
    form.companyName = name;
    form.licenseNo = regNum;
    form.licenseAddr = address;
    form.licenseOperateRange = business;

    if (establishDate && validPeriod) {
      let endDate = validPeriod;
      if (validPeriod === '长期') {
        endDate = '2099-12-31';
      }
      licenseDateRegion.value = [dayjs(establishDate).valueOf(), dayjs(endDate).valueOf()];
      onConfirmLicenseDateRegion();
    }
  }
}

function onSelectUnionMcc() {
  uni.navigateTo({ url: '/pages/picker-view/category/mcc-category' });
  emitter.on('picker-category', (item) => {
    form.mcc = item.mcc;
    form.mccName = item.mccName;
  });
}

function onBeforeBackPress() {
  message
    .confirm({
      msg: '返回当前页面内容将丢失哦, 是否确定返回?',
      title: '温馨提示',
    })
    .then(() => {
      uni.navigateBack();
    });
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index?leaf=3' });
  emitter.on('picker-area', (data: any) => {
    const [province, city, country] = data;
    bankArea.value = data.map((item: any) => item.areaName).join('');
    form.province = province.areaCode;
    form.city = city.areaCode;
    form.country = country.areaCode;
  });
}

function onConfirmLicenseDateRegion() {
  const [startDate, endDate] = licenseDateRegion.value;
  form.licenseStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.licenseEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

function onConfirmLegalDateRegion() {
  const [startDate, endDate] = legalDateRegion.value;
  form.legalCertStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.legalCertEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileList = Object.values(fileMap.value);
  const imageList = fileList.map((item) => {
    return {
      fileData: item.fileData,
      fileType: item.fileType,
      suffixType: 'png',
    };
  });
  const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList })
    .catch(() => Promise.reject(new Error('upload fail!')));

  return data?.imageJsonList || [];
}

/*
 * 校验文件
 */
async function checkFile() {
  const fileList = Object.values(fileMap.value);
  const isCheckPass = fileList.every((f) => {
    if (f.required === false)
      return true;
    if (!f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

async function ocrIdcardByFile(file: string, item: any) {
  const { fileType } = item;
  const side = fileType === 1 ? 'face' : 'back';
  const data = await CommonApi.ocrIdCard({ imgFile: file, side });

  if (!data?.success)
    return;

  switch (side) {
    case 'face':
      form.legalName = data.name;
      form.legalCertNo = data.num;
      form.legalAddr = data.address;
      form.legalSex = data.sex;
      break;
    case 'back':
      // eslint-disable-next-line no-case-declarations
      let { startDate, endDate } = data;
      if (startDate && endDate) {
        if (endDate === '长期') {
          endDate = '2099-12-31';
        }
        legalDateRegion.value = [dayjs(startDate).valueOf(), dayjs(endDate).valueOf()];
        onConfirmLegalDateRegion();
      }
      break;

    default:
      break;
  }
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-cell-group__title{
   @apply bg-primary;
  }

  .wd-input__error-message,
  .wd-cell__error-message,
  .wd-textarea__error-message,
  .wd-picker__error-message
  {
    text-align: right !important;
  }
}
</style>
