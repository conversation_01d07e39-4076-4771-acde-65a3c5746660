<template>
  <view class="h-full flex flex-col bg-primary">
    <view v-if="showNavbar" class="shrink-0">
      <wd-navbar title="详细地址" safe-area-inset-top left-arrow @click-left="handleClickLeft" />
    </view>
    <view class="shrink-0">
      <wd-textarea v-model="searchKeyword" placeholder="详细地址 (例如**街**号**)" @input="onSearch" />
      <!-- <wd-textarea v-if="current" v-model="replenishValue" label="地址补充:" label-width="80px" placeholder="补充地址信息" auto-height /> -->
      <!-- <view class="flex justify-end bg-white p-20rpx">
        <template v-if="!!searchKeyword">
          <view>
            <wd-button type="info" size="small" plain block @click="onClear">
              清空
            </wd-button>
          </view>
          <view v-if="current" class="ml-20rpx">
            <wd-button size="small" block @click="onConfirm">
              确定
            </wd-button>
          </view>
        </template>
        <view v-else>
          <wd-button type="error" size="small" plain block @click="onCancel">
            取消
          </wd-button>
        </view>
      </view> -->
    </view>
    <view v-if="prompts?.length" class="p-20rpx">
      <text class="text-26rpx text-#999">
        请选择地址
      </text>
    </view>
    <view class="grow overflow-y-scroll">
      <view
        v-for="(item, key) in prompts" :key="key"
        class="flex flex-col border border-#edf0f3 border-b-solid bg-white p-20rpx"
        @click="onSelect(item)"
      >
        <text class="text-32rpx font-600" v-html="formatTextStyle(item.locationName)" />
        <text class="mt-10rpx text-#999">
          {{ item.address }}
        </text>
      </view>
    </view>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { debounce } from 'wot-design-uni/components/common/util';
import { emitter } from '@/utils/emitter';
import { CommonApi } from '@/api/common';

const prompts = ref<any>([]);

const replenishValue = ref();

const current = ref<any>(null);

const showNavbar = ref(true);

const areaCodeMap = reactive({
  province: '',
  city: '',
  country: '',
});

// 地址搜索关键字
const searchKeyword = ref<string>('');

onLoad((options) => {
  Object.assign(areaCodeMap, options);

  if (options?.embe) {
    showNavbar.value = options?.embe !== '1';
  }
});

onUnmounted(() => {
  emitter.off('picker-address');
});

function handleClickLeft() {
  uni.navigateBack();
}

const onSearch = debounce(async ({ value }) => {
  if (!value && value !== 0) {
    prompts.value = [];
    return;
  }

  const data = await CommonApi.queryLocationList({
    keyWord: value,
    provinceCode: areaCodeMap.province,
    cityCode: areaCodeMap.city,
    countyCode: areaCodeMap.country,
    pageNo: 1,
    pageSize: 20,
  });

  const filterList = data?.locationDetailList?.filter((i: any) => i.longitude && i.latitude) || [];
  prompts.value = filterList;
}, 500);

function onSelect(item: any) {
  searchKeyword.value = `${item.address}${item.locationName}`;
  current.value = item;
  replenishValue.value = '';
  onConfirm();
}

function onConfirm() {
  const resBody = {
    address: `${current.value.address}`,
    longitude: current.value.longitude,
    latitude: current.value.latitude,
    adname: current.value.countyName,
  };
  emitter.emit('picker-address', resBody);
  uni.navigateBack();
}

function onCancel() {
  uni.navigateBack();
}

function onClear() {
  searchKeyword.value = '';
  current.value = null;
  prompts.value = [];
  replenishValue.value = '';
}

function formatTextStyle(text: string) {
  const reg = new RegExp(searchKeyword.value, 'g');
  return text.replace(reg, `<text class="text-red">${searchKeyword.value}</text>`);
}
</script>
