<template>
  <view class="h-full overflow-y-scroll">
    <view class="gap-primary" />

    <view class="p-15px">
      <view class="mt-15px font-medium">
        请选择商户类型:
      </view>

      <wd-radio-group v-model="merchType" custom-class="custom-radio-group-class">
        <wd-radio v-for="(item, key) in merchTypeOptions" :key="key" :value="item.value">
          <view class="flex items-center">
            <i class="size-56rpx shrink-0" :class="item.iconClass" />
            <text class="ml-20px text-28rpx font-medium">
              {{ item.label }}
            </text>
          </view>
        </wd-radio>
      </wd-radio-group>

      <view class="mt-50px">
        <wd-button type="primary" size="large" block @click="handleNext">
          下一步
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const merchTypeOptions = [
  {
    label: '小微商户(无营业执照)',
    value: 1,
    iconClass: ['i-mdi-storefront', 'text-#fa4350'],
  },
  {
    label: '个体或企业商户',
    value: 2,
    iconClass: ['i-mdi-office-building-outline', 'text-#f0cd1d'],
  },
];

const merchType = ref<number>(1);

function handleNext() {
  switch (merchType.value) {
    case 1:
      uni.navigateTo({
        url: '/pages/report/merch-auth/auth-micro-merch/index',
      });
      break;
    case 2:
      uni.navigateTo({
        url: '/pages/report/merch-auth/auth-company-merch',
      });
      break;
  }
}
</script>

<style lang="scss" scoped>
:deep(.custom-radio-group-class) {
  .wd-radio{
    padding: 22px;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .is-checked{
    background-color: #f8f9fa;
    border-color:transparent
  }

  .wd-radio__label{
    width: 100%;
  }

  .wd-radio__shape{
    background-color: transparent;
  }
}
</style>
