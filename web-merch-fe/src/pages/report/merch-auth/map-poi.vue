<template>
  <div class="h-full flex flex-col overflow-hidden bg-primary">
    <view v-if="showNavbar" class="shrink-0">
      <wd-navbar title="详细地址" safe-area-inset-top left-arrow placeholder fixed @click-left="handleClickLeft" />
    </view>
    <!-- 搜索栏 -->
    <view class="mt-10px shrink-0">
      <wd-search
        placeholder="小区/写字楼/学校等"
        placeholder-left
        hide-cancel
        read-only
        @click="toAddressPrompt"
      >
        <template #prefix>
          <span class="search-label">{{ cityName }}</span>
        </template>
      </wd-search>
    </view>

    <view class="flex grow flex-col">
      <!-- 地图 -->
      <div id="map-container" />
      <!-- POI 列表 -->
      <div id="poi-container">
        <div v-show="mapCenterInfo.address" class="flex items-center p-20rpx">
          <span class="mr-2px text-16px font-500">{{ mapCenterInfo.address }}</span>
          <wd-icon name="location" size="36rpx" />
        </div>
        <wd-cell-group border>
          <wd-cell
            v-for="(poi, idx) in pois"
            :key="idx"
            :title="poi.name"
            :label="poi.address"
            clickable
            size="large"
            title-width="100%"
            @click="onSelectPoi(poi)"
          />
        </wd-cell-group>
      </div>
    </view>
  </div>
</template>

<script setup lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { debounce } from 'wot-design-uni/components/common/util';
import { MAP_KEY, MAP_SECURITY_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';
import { Dialog, buildUrlWithParams } from '@/utils';
/**
 * 安全密钥配置
 */
window._AMapSecurityConfig = {
  securityJsCode: MAP_SECURITY_CODE,
};

// !城市名 必须设置
const cityName = ref('南京市');
// 高德城市编码
let cityCode = '';

// 地图实例
let map: any = null;
let geolocation: any = null;

// 地图中心点信息
const mapCenterInfo = reactive({
  address: '',
  position: null,
});

// 地图中心点POI列表
const pois = ref([]);

const showNavbar = ref(true);

onLoad((options) => {
  if (!options?.c) {
    Dialog('城市不能为空').then(() => {
      uni.navigateBack();
    });
    return;
  }

  if (options?.embe) {
    showNavbar.value = options.embe !== '1';
  }

  cityName.value = options.c;
  initAmap();
});

onUnmounted(() => {
  map?.destroy();
});

interface PoiType {
  name: string;
  adname: string;
  address: string;
  location: {
    lng: number;
    lat: number;
  };
  citycode: string;
};
/**
 * 选中地图中心点POI地址
 */
const onSelectPoi = (poi: PoiType) => {
  if (poi?.citycode && cityCode) {
    if (poi.citycode !== cityCode) {
      Dialog(`只能选择${cityName.value}内的地址`);
      return;
    }
  }

  const resBody = {
    address: `${poi.address}`,
    longitude: poi.location?.lng,
    latitude: poi.location?.lat,
    adname: poi.adname,
  };
  emitter.emit('picker-address', resBody);
  uni.navigateBack();
  emitter.off('picker-address');
};

/**
 * 地图初始化
 */
function initAmap() {
  AMapLoader.load({
    key: MAP_KEY,
    version: '2.0',
    plugins: ['AMap.PlaceSearch', 'AMap.Scale', 'AMap.Geocoder', 'AMap.Geolocation'],
    AMapUI: {
      version: '1.1',
    },
  })
    .then(async (AMap) => {
      // 创建定位实例 获取当前位置
      geolocation = new AMap.Geolocation({
        enableHighAccuracy: true,
        needAddress: true,
        timeout: 10000,
      });

      const geolocationInfo: any = await getLocationByGaode();

      // 地图中心点
      let cpoint = null;

      // 暂认为同城
      if (geolocationInfo && geolocationInfo.formattedAddress.includes(cityName.value)) {
        cpoint = [geolocationInfo.position.lng, geolocationInfo.position.lat];
      }
      else {
        /**
         * 正地理编码&获取地图中心点
         */

        const geocoder = new AMap.Geocoder({ batch: false });
        await new Promise((resolve, reject) => {
        // 地理编码
          geocoder.getLocation(cityName.value, (status: any, result: any) => {
            if (status === 'complete' && result.info === 'OK') {
            // result中对应详细地理坐标信息
              const geoInfo = result.geocodes[0] || {};

              cityCode = geoInfo.addressComponent.citycode;

              const location = geoInfo.location || [];
              cpoint = location;

              resolve('MAPCENTER SET SUCCESS!');
            }
            else {
            // 地图中心点设置失败 直接抛异常
              reject('MAPCENTER SET ERROR !');
              Dialog('地图加载异常, 请稍后重试!').then(() => {
                uni.navigateBack();
              });
            }
          });
        });
      }

      /** 设置地图 */
      map = new AMap.Map('map-container', {
        zoom: 16,
      });

      // 设置地图中心点
      map.setCenter(cpoint, true);

      /** 地图添加比例尺 */
      map.addControl(new AMap.Scale());

      /**
       * 构造地点查询类
       */
      const placeSearch = new AMap.PlaceSearch({
        pageSize: 10, // 单页显示结果条数
        pageIndex: 1, // 页码
        autoFitView: false,
        extensions: 'all',
      });
      searchNearBy(cpoint);
      // 中心点周边POI
      function searchNearBy(centerPoint: any) {
        placeSearch.searchNearBy('', centerPoint, 1000, (status: any, result: any) => {
          if (status === 'complete' && result.info === 'OK') {
            const filterTips = result?.poiList?.pois.filter(t => typeof t.address === 'string' && !!t.address);
            pois.value = filterTips || [];
            console.log(pois.value);
          }
          else {
            pois.value = [];
          }
        });
      }

      /**
       * 拖拽选址实现
       */
      AMapUI.loadUI(['misc/PositionPicker'], (PositionPicker) => {
        const positionPicker = new PositionPicker({
          mode: 'dragMap', // 设定为拖拽地图模式，可选'dragMarker'
          map, // 依赖地图对象
        });

        positionPicker.on(
          'success',
          debounce((positionResult) => {
            Object.assign(mapCenterInfo, positionResult, {});
            searchNearBy(mapCenterInfo.position);
          }, 450),
        );

        positionPicker.start();
      });
    })
    .catch((e) => {
      console.log(e);
    });
}

async function getLocationByGaode() {
  return new Promise((resolve) => {
    geolocation.getCurrentPosition((status, result) => {
      console.log(status, result);
      if (status === 'complete') {
        resolve(result);
      }
      else {
        resolve(null);
      }
    });
  });
}

function toAddressPrompt() {
  const url = buildUrlWithParams('/pages/report/merch-auth/address-prompt', {
    c: cityName.value,
    embe: showNavbar.value ? '0' : '1',
  });

  uni.redirectTo({ url });
}

function handleClickLeft() {
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
.search-label {
    display: inline-block;
    overflow: hidden;
    padding-left: 10px;
    width: 5em;
    font-size: 26rpx;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
  }

// 地图
#map-container {
  width: 100%;
  flex: 1 1 0%;
}

// POI 展示
#poi-container {
  position: relative;
  overflow-y: scroll;
  width: 100%;
  height: 660rpx;
}
</style>
