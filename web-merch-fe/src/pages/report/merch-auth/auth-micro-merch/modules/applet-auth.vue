<template>
  <view class="h-full">
    <web-view :src="url" />
  </view>
</template>

<script setup lang="ts">
import { SELF_WEBVIEW_URL } from '@/config/setting';
import { buildUrlWithParams, getToken } from '@/utils';

const url = ref<string>('');

onLoad((options) => {
  url.value = buildUrlWithParams(`${SELF_WEBVIEW_URL}/#/pages/report/merch-auth/auth-micro-merch/index`, {
    ...options,
    client_type: 'applet',
    token: getToken(),
  });
  console.log('url=', url.value);
});
</script>
