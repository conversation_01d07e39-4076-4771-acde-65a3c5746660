<template>
  <view>
    <view class="h-10px bg-primary" />

    <wd-form ref="formRef" :model="form" error-type="toast">
      <wd-cell-group border>
        <wd-input
          v-model="form.password" prop="password"
          label="登录密码" placeholder="请输入登录密码"
          :rules="[{ required: true, message: '请输入登录密码' }]"
          align-right show-password
        />
        <wd-input
          v-model="form.newAccount" prop="newAccount"
          label="新手机号" placeholder="请输入新手机号"
          :rules="[{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }]"
          align-right type="number"
        />
        <wd-input
          v-model="form.smsCode" prop="smsCode"
          label="验证码" placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
          type="number" use-suffix-slot align-right
        >
          <template #suffix>
            <view class="w-94px border-#f3f5f7 border-l-solid text-center">
              <text class="text-14px text-#4d80f0" @click="getSmsCode">
                {{ timer ? `${countdown}s后重发` : '获取验证码' }}
              </text>
            </view>
          </template>
        </wd-input>
        <wd-cell />
      </wd-cell-group>
    </wd-form>

    <view class="p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        提交
      </wd-button>
    </view>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance } from 'wot-design-uni/components/wd-form/types';
import { useMessage } from 'wot-design-uni';
import { UserApi } from '@/api/user';
import { Toast, clearToken } from '@/utils';
import { CommonApi } from '@/api/common';

const loading = ref(false);

const message = useMessage();

// 表单
const form = reactive({
  password: '', // 登录密码
  newAccount: '', // 新手机号，限制11位 必填
  smsCode: '',
});
const formRef = ref<FormInstance | null>(null);

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  loading.value = true;

  // 提交
  UserApi.changeTelephone(form)
    .then(() => {
      message.alert({
        msg: '手机号已更改, 请重新登录',
        title: '提示',
      }).then(() => {
        clearToken();
        uni.reLaunch({ url: '/pages/common/login/index' });
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const timer = ref();
const countdown = ref(60);

function getSmsCode() {
  if (!/^1[3-9]\d{9}$/.test(form.newAccount))
    return Toast('请输入正确的手机号');

  if (timer.value)
    return;

  CommonApi.sendSms({
    mobile: form.newAccount,
    smsBusinessType: 3,
    smsSendSource: 1,
  })
    .then(() => {
      Toast('验证码已发送');
      startTimer();
    });
}

function startTimer() {
  if (timer.value)
    return;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value!);
      timer.value = null;
      countdown.value = 60;
    }
  }, 1000);
}
</script>
