<template>
  <view>
    <view class="gap-primary" />
    <view>
      <wd-cell-group border>
        <wd-cell title="修改登录密码" is-link to="/pages/settings/update-login-pwd" />
        <wd-cell title="更换手机号" is-link to="/pages/settings/update-phone" />
        <wd-cell title="关于" is-link to="/pages/settings/about" />
        <wd-cell title="注销" is-link @click="handleLogout" />
        <wd-cell />
      </wd-cell-group>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { isNumber } from 'wot-design-uni/components/common/util';
import { UserApi } from '@/api/user';
import { Dialog, Loading } from '@/utils';

interface ICancelInfo {
  cancelStatus?: number;
  cancelCheckStatus?: number;
  auditRemark?: string;
}

async function handleLogout() {
  Loading.show('');
  const res = await UserApi.merchantCancelStatus().catch(() => {
    Loading.hide();
  });
  Loading.hide();

  const cancleInfo: ICancelInfo = res || {};

  if (cancleInfo.cancelStatus === 0 && !isNumber(cancleInfo.cancelCheckStatus)) {
    uni.navigateTo({
      url: '/pages/settings/logout',
    });
    return;
  }

  if (cancleInfo.cancelStatus === 1) {
    Dialog('注销申请已提交，审核中');
    return;
  }

  if (cancleInfo.cancelStatus === 0 && cancleInfo.cancelCheckStatus === 2) {
    uni.navigateTo({
      url: `/pages/settings/logout?checkFail=1`,
    });
  }
}
</script>
