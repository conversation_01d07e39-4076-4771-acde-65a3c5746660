<template>
  <view>
    <view class="flex flex-col items-center p-20px">
      <text class="text-#333">
        注销通过申请后, 您将无法登录该账号, 相关数据也将被删除无法找回
      </text>
      <view class="mt-15px">
        <wd-text text="当前账号:" />
        <wd-text :text="loginUser.account" mode="phone" :format="true" class="ml-10px" />
      </view>
    </view>

    <wd-form ref="formRef" :model="form" error-type="toast">
      <wd-cell-group border>
        <wd-input
          v-model="form.smsCode" prop="smsCode"
          label="验证码" placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
          type="number" use-suffix-slot align-right :no-border="false"
        >
          <template #suffix>
            <view class="w-94px border-#f3f5f7 border-l-solid text-center">
              <text class="text-14px text-#4d80f0" @click="getSmsCode">
                {{ timer ? `${countdown}s后重发` : '获取验证码' }}
              </text>
            </view>
          </template>
        </wd-input>
        <wd-cell />
      </wd-cell-group>
    </wd-form>

    <view class="p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        立即注销
      </wd-button>
      <view v-if="cancelInfo?.auditRemark" class="mt-10px">
        <wd-text type="error" :text="cancelInfo.auditRemark" />
      </view>
    </view>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance } from 'wot-design-uni/components/wd-form/types';
import { useMessage } from 'wot-design-uni';
import { UserApi } from '@/api/user';
import { Toast, clearToken } from '@/utils';
import { useUserStore } from '@/store';
import { CommonApi } from '@/api/common';

const loading = ref(false);

const message = useMessage();

const loginUser = computed(() => useUserStore().info);

// 表单
const form = reactive({
  smsCode: '',
  smsCodeMobile: loginUser.value.account,
});
const formRef = ref<FormInstance | null>(null);

const cancelInfo = ref<any>(null);

onLoad((options) => {
  if (options?.checkFail) {
    queryCancelInfo();
  }
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  loading.value = true;

  // 提交
  UserApi.merchantCancelApply(form)
    .then(() => {
      message.alert({
        msg: '注销申请已提交, 请等待审核',
        title: '提示',
      }).then(() => {
        clearToken();
        uni.reLaunch({ url: '/pages/common/login/index' });
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const timer = ref();
const countdown = ref(60);

function getSmsCode() {
  if (timer.value)
    return;

  CommonApi.sendSms({
    mobile: form.smsCodeMobile,
    smsBusinessType: 6,
    smsSendSource: 1,
  })
    .then(() => {
      Toast('验证码已发送');
      startTimer();
    });
}

function startTimer() {
  if (timer.value)
    return;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value!);
      timer.value = null;
      countdown.value = 60;
    }
  }, 1000);
}

async function queryCancelInfo() {
  const data = await UserApi.merchantCancelStatus();
  cancelInfo.value = data;
}
</script>
