<template>
  <view>
    <view class="h-10px bg-primary" />

    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-input
          v-model="form.password" prop="password"
          label="当前密码" placeholder="请输入当前登录密码"
          align-right show-password
        />
        <wd-input
          v-model="form.newPassword" prop="newPassword"
          label="新密码" placeholder="请输入新密码"
          align-right show-password
        />
        <wd-input
          v-model="form.confirmPassword" prop="confirmPassword"
          label="确认密码" placeholder="请再次输入新密码"
          align-right show-password
        />
        <wd-cell />
      </wd-cell-group>
    </wd-form>

    <view class="p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        提交
      </wd-button>
    </view>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage } from 'wot-design-uni';
import { UserApi } from '@/api/user';
import { clearToken } from '@/utils';

const loading = ref(false);

const message = useMessage();

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive({
  password: '', // 旧密码
  newPassword: '', // 新密码
  confirmPassword: '',
});

// 规则
const rules: FormRules = {
  password: [{ required: true, message: '请输入当前登录密码' }],
  newPassword: [{ required: true, pattern: /[a-z0-9]{8,20}/i, message: '新密码必须为8-20位字母+数字组合' }],
  confirmPassword: [
    { required: true, message: '请再次输入密码', validator: (value) => {
      if (value === form.newPassword) {
        return Promise.resolve();
      }
      else {
        return Promise.reject('两次密码不一致');
      }
    } },
  ],
};

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  loading.value = true;

  // 提交
  UserApi.updatePassword(form)
    .then(() => {
      message.alert({
        msg: '登录密码已更改, 请重新登录',
        title: '提示',
      }).then(() => {
        clearToken();
        uni.reLaunch({ url: '/pages/common/login/index' });
      });
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
