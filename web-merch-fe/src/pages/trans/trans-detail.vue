<template>
  <view class="p-30rpx">
    <!-- 骨架屏 -->
    <wd-skeleton theme="paragraph" :loading="loading" />

    <!-- 交易详情 -->
    <view v-show="!loading" class="rounded-lg bg-primary py-30rpx">
      <view class="flex flex-col items-center py-30rpx text-28rpx">
        <text class="text-40rpx text-#b51e1e font-500">
          {{ detail.transAmount }}
        </text>
        <text class="mt-10rpx font-500">
          {{ transStatusMap[detail.transStatus] || '--' }}
        </text>
        <text v-if="detail.transStatus === 3" class="mt-4px px-20rpx text-#b51e1e">
          {{ detail.transResDesc }}
        </text>
      </view>

      <view class="text-28rpx">
        <wd-row :gutter="10">
          <wd-col :span="8" custom-class="col-flex text-#999">
            <text>交易时间</text>
            <text>银行名称</text>
            <text>付款卡号</text>
            <text>交易手续费(元)</text>
            <text>手续费类型</text>
            <text>交易费率(%)</text>
            <text>结算金额(元)</text>
            <text>交易方式</text>
            <text>交易类型</text>
            <text>交易卡类型</text>
            <text>机具SN</text>
            <text>商户编码</text>
            <text>商户名称</text>
            <text>交易参考号</text>
          </wd-col>
          <wd-col :span="16" custom-class="col-flex items-end">
            <text>{{ detail.transTime || '--' }}</text>
            <text>{{ detail.bankName || '--' }}</text>
            <text>{{ detail.payCardNoMask || '--' }}</text>
            <text>{{ detail.merchFee || '--' }}</text>
            <text>{{ detail.feeTypeDesc || '--' }}</text>
            <text>{{ detail.merchRate || '--' }}</text>
            <text>{{ detail.merchSettleAmount || '--' }}</text>
            <text>{{ payMethodMap[detail.payMethod] || '--' }}</text>
            <text>{{ detail.transCodeName || '--' }}</text>
            <text>{{ payCardTypeMap[detail.payCardType] || '--' }}</text>
            <view>
              {{ detail.termSn || '--' }}
              <i v-if="detail.termSn" class="copy-icon" @click="copyContent(detail.termSn)" />
            </view>
            <view>
              {{ detail.chnMerchNo || '--' }}
              <i v-if="detail.chnMerchNo" class="copy-icon" @click="copyContent(detail.chnMerchNo)" />
            </view>
            <text>{{ detail.chnMerchName || '--' }}</text>
            <view>
              {{ detail.orderNo || '--' }}
              <i v-if="detail.orderNo" class="copy-icon" @click="copyContent(detail.orderNo)" />
            </view>
          </wd-col>
        </wd-row>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { TransApi } from '@/api/trans';
import { useClipboard } from '@/hooks';

const loading = ref(true);

const detail = ref<any>({});

const transStatusMap: Record<number, string> = {
  1: '已创建',
  2: '交易成功',
  3: '交易失败',
  4: '进行中',
  5: '已受理',
  6: '支付结果待查',
};

const payMethodMap: Record<number, string> = {
  1: '云闪付支付',
  2: '微信支付',
  3: '支付宝支付',
  4: 'EPOS支付',
  5: 'POS刷卡',
};

const payCardTypeMap: Record<number, string> = {
  1: '借记卡',
  2: '贷记卡',
  3: '准贷记卡',
  4: '预付费卡',
};

onLoad((query) => {
  const { orderNo, orderType } = query || {};
  getDetail({ orderNo, orderType });
});

function copyContent(data: string) {
  useClipboard().setClipboardData({ data });
}

async function getDetail(where: any) {
  const data = await TransApi.transDetailInfo(where);
  detail.value = data || {};

  loading.value = false;
}
</script>

<style lang="scss" scoped>
 :deep(.col-flex){
  @apply flex flex-col;

  text,view{
    @apply mb-8px break-all;
  }
}

.copy-icon{
  @apply i-mdi-content-copy  size-30rpx text-#b51e1e ml-2px mb-6px shrink-0;
}
</style>
