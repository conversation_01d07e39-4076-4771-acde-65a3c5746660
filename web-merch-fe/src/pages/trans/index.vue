<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <template #top>
        <!-- 搜索栏 -->
        <wd-navbar left-arrow :bordered="false" custom-class="custom-navbar-class" safe-area-inset-top @click-left="handlePageBack">
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入交易订单号" custom-class="w-full"
                @search="onSearchByOrderNo"
              />
            </view>
          </template>
        </wd-navbar>

        <view class="w-50%">
          <wd-drop-menu>
            <wd-drop-menu-item
              v-model="where.transStatus" :options="transStatusOptions"
              :[transStatusTitleProp]="'交易状态'"
              @change="onChangeWhereStatus"
            />
            <wd-drop-menu-item
              v-model="where.transType" :options="transTypeOptions"
              :[transTypeTitleProp]="'交易类型'"
              @change="onChangeWhereType"
            />
          </wd-drop-menu>
        </view>

        <view class="bg-primary p-20rpx">
          <view class="flex flex-col items-start rounded-md bg-#4D80F0 p-20rpx text-28rpx text-white">
            <custom-datetime-picker
              v-model="whereDate"
              type="date"
              :default-value="whereDate"
              custom-value-class="!text-white"
              @confirm="handleConfirmDate"
            />

            <view class="mt-12px w-full flex">
              <view class="flex basis-1/2 flex-col items-start">
                <text>交易总额(元)</text>
                <view class="mt-6px">
                  <text class="font-bold">
                    {{ transInfo.totalTransAmount }}
                  </text>
                  <text class="ml-6px">
                    共{{ transInfo.countTrans }}笔
                  </text>
                </view>
              </view>

              <view class="flex basis-1/2 flex-col items-end">
                <text>应结算金额(元)</text>
                <text class="mt-6px font-bold">
                  {{ transInfo.totalSettleAmount }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <view class="overflow-hidden bg-primary">
        <view v-for="(item, key) in datasource" :key="key" class="bg-white px-20rpx" @click="toDetail(item)">
          <view class="flex items-center border border-#f3f5f7 border-b-solid py-20rpx">
            <view class="shrink-0">
              <i class="i-mdi-storefront size-70rpx shrink-0 text-#846a27" />
            </view>
            <view class="flex grow flex-col">
              <view class="flex grow">
                <view class="flex basis-16/24 flex-col break-all px-20rpx text-29rpx">
                  <text class="mb-4px font-500">
                    {{ item.chnMerchName }}
                  </text>
                  <text class="mb-4px">
                    {{ item.chnMerchNo }}
                  </text>
                  <text class="mb-4px">
                    {{ item.terminalSn }}
                  </text>
                  <text class="mb-4px">
                    {{ item.payCardNoMask }}
                  </text>
                  <text class="mb-4px text-#999">
                    {{ item.transTime }}
                  </text>
                </view>

                <view class="flex shrink-0 basis-8/24 items-center justify-end">
                  <view class="flex flex-col items-end break-all">
                    <text class="mb-10rpx text-32rpx font-500">
                      {{ item.transAmount }}
                    </text>
                    <template v-for="(option, optionKey) in transStatusOptions " :key="optionKey">
                      <text v-if="item.transStatus === option.value" :class="option.textClass" class="text-28rpx">
                        {{ option.label }}
                      </text>
                    </template>
                  </view>
                  <i class="i-mdi-chevron-right size-60rpx text-#666" />
                </view>
              </view>

              <view class="px-20rpx">
                <wd-tag type="primary" plain custom-class="custom-tag-class">
                  {{ item.transCodeName }}
                </wd-tag>
                <wd-tag type="primary" plain custom-class="custom-tag-class">
                  {{ payCardTypeMap[item.payCardType] || '--' }}
                </wd-tag>
                <wd-tag type="primary" plain custom-class="custom-tag-class">
                  {{ feeTypeMap[item.feeType] || '--' }}
                </wd-tag>
              </view>
            </view>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { TransApi } from '@/api/trans';
import { CHANNEL_CODE } from '@/config/setting';
import { buildUrlWithParams, deepClone } from '@/utils';

const pagingRef = ref();

const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive<any>({
  transChn: CHANNEL_CODE, // 支付机构(传渠道编码）
  orderNo: '', // 平台订单号
  transType: -1, // 交易类型(传交易类型大类 ，1银联云闪付 2微信支付 3支付宝支付 4EPOS支付 ）
  transStatus: -1, // 交易状态(1已创建、2交易成功、3交易失败、4交易进行中、5请求已受理、6支付结果待查）
  searchBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 查询交易开始时间 yyyy-MM-dd 2023-04-11
  searchEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 查询交易结束时间 yyyy-MM-dd 2023-04-11
});

const transStatusTitleProp = computed(() => {
  const prop = where.transStatus === -1 ? 'title' : '';
  return prop;
});
const transTypeTitleProp = computed(() => {
  const prop = where.transType === -1 ? 'title' : '';
  return prop;
});

const transTypeOptions = [
  { label: '全部', value: -1 },
  { label: '银联云闪付', value: 1 },
  { label: '微信支付', value: 2 },
  { label: '支付宝支付', value: 3 },
  { label: 'EPOS支付', value: 4 },
];

const transStatusOptions = [
  { label: '全部', value: -1 },
  { label: '已创建', value: 1, textClass: 'text-blue' },
  { label: '交易成功', value: 2, textClass: 'text-green' },
  { label: '交易失败', value: 3, textClass: 'text-red' },
  { label: '进行中', value: 4, textClass: 'text-orange' },
  { label: '已受理', value: 5, textClass: 'text-blue' },
  { label: '支付结果待查', value: 6, textClass: 'text-blue' },
];

const payCardTypeMap: EnumMap = {
  1: '借记卡',
  2: '贷记卡',
  3: '准贷记卡',
  4: '预付费卡',
};

const feeTypeMap: EnumMap = {
  B: '标准费率',
  YN: '云闪付费率',
};

const datasource = ref<any[]>([]);

const transInfo = ref<any>({});

function toDetail(item: any) {
  const url = buildUrlWithParams('/pages/trans/trans-detail', {
    orderNo: item.orderNo,
    orderType: item.orderType,
  });
  uni.navigateTo({ url });
}

async function queryTransAmount(params = {}) {
  const data = await TransApi.selectTotalAmount({
    ...params,
    transStatus: 2,
  });
  transInfo.value = data || {};
}

function handleConfirmDate({ value }: { value: string[] }) {
  [where.searchBeginTime, where.searchEndTime] = value;
  pagingRef.value.reload();
}

function onChangeWhereStatus() {
  pagingRef.value.reload();
}

function onChangeWhereType() {
  pagingRef.value.reload();
}

function onSearchByOrderNo({ value }: { value: string }) {
  where.orderNo = value;
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.transStatus = where.transStatus === -1 ? null : where.transStatus;
  formatWhere.transType = where.transType === -1 ? null : where.transType;

  TransApi.queryList({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });

  pageNo === 1 && queryTransAmount(formatWhere);
}

function handlePageBack() {
  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

:deep(.custom-tag-class){
  margin-right: 10px;
  font-size: 24rpx !important;
}
</style>
