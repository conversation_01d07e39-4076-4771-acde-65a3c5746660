<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="p-10px">
      <view v-for="(item, key) in transRules" :key="key" class="mb-20rpx rounded-lg bg-white p-20rpx">
        <text class="font-medium">
          {{ item.type }}
        </text>
        <view class="mt-20rpx">
          <wd-row custom-class="row-table-head">
            <wd-col :span="6">
              类型
            </wd-col>
            <wd-col :span="6">
              单笔
            </wd-col>
            <wd-col :span="6">
              日限额
            </wd-col>
            <wd-col :span="6">
              月限额
            </wd-col>
          </wd-row>
          <wd-row v-for="(ruleItem, keyR) in item.data" :key="keyR" custom-class="row-table-body">
            <wd-col :span="6">
              {{ ruleItem.type }}
            </wd-col>
            <wd-col :span="6">
              {{ formatAmount(ruleInfo[ruleItem.single]) }}
            </wd-col>
            <wd-col :span="6">
              <text class="text-#4d80f0">
                {{ formatAmount(ruleInfo[ruleItem.dayCumulate]) }}
              </text>/{{ formatAmount(ruleInfo[ruleItem.dayLimit]) }}
            </wd-col>
            <wd-col :span="6">
              <text class="text-#4d80f0">
                {{ formatAmount(ruleInfo[ruleItem.monthCumulate]) }}
              </text>/{{ formatAmount(ruleInfo[ruleItem.monthLimit]) }}
            </wd-col>
          </wd-row>
        </view>
      </view>

      <view class="round-card">
        <text class="font-medium">
          交易时间
        </text>
        <text>{{ ruleInfo.allowPayPeriod }}</text>
      </view>

      <view class="round-card">
        <text class="font-medium">
          提现时间
        </text>
        <text>{{ ruleInfo.allowWithdrawPeriod }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import NP from 'number-precision';
import { MerchApi } from '@/api/merch';

const ruleInfo = ref<any>({});

// 银行卡交易
const bankTrans: any[] = [
  {
    type: '芯片卡(贷)',
    single: 'posIcCreditOnceLimit',
    dayLimit: 'posIcCreditDayLimit',
    monthLimit: 'posIcCreditMonthLimit',
    dayCumulate: 'posIcCreditDayCumulate',
    monthCumulate: 'posIcCreditMonthCumulate',
  },
  {
    type: '磁条卡(贷)',
    single: 'posCiCreditOnceLimit',
    dayLimit: 'posCiCreditDayLimit',
    monthLimit: 'posCiCreditDayLimit',
    dayCumulate: 'posCiCreditDayCumulate',
    monthCumulate: 'posCiCreditMonthCumulate',
  },
  {
    type: '手机pay(贷)',
    single: 'posNfcCreditOnceLimit',
    dayLimit: 'posNfcCreditDayLimit',
    monthLimit: 'posNfcCreditMonthLimit',
    dayCumulate: 'posNfcCreditDayCumulate',
    monthCumulate: 'posNfcCreditMonthCumulate',
  },
  {
    type: '芯片卡(借)',
    single: 'posIcDebitOnceLimit',
    dayLimit: 'posIcDebitDayLimit',
    monthLimit: 'posIcDebitMonthLimit',
    dayCumulate: 'posIcDebitDayCumulate',
    monthCumulate: 'posIcDebitMonthCumulate',
  },
  {
    type: '磁条卡(借)',
    single: 'posCiDebitOnceLimit',
    dayLimit: 'posCiDebitDayLimit',
    monthLimit: 'posCiDebitMonthLimit',
    dayCumulate: 'posCiDebitDayCumulate',
    monthCumulate: 'posCiDebitMonthCumulate',
  },
  {
    type: '手机pay(借)',
    single: 'posNfcDebitOnceLimit',
    dayLimit: 'posNfcDebitDayLimit',
    monthLimit: 'posNfcDebitMonthLimit',
    dayCumulate: 'posNfcDebitDayCumulate',
    monthCumulate: 'posNfcDebitMonthCumulate',
  },
];

// 扫码交易
const scanTrans: any[] = [
  {
    type: '云闪付',
    single: 'unionpayOnceLimit',
    dayLimit: 'unionpayDayLimit',
    monthLimit: 'unionpayMonthLimit',
    dayCumulate: 'unionpayDayCumulate',
    monthCumulate: 'unionpayMonthCumulate',
  },
  {
    type: '支付宝',
    single: 'alipayOnceLimit',
    dayLimit: 'alipayDayLimit',
    monthLimit: 'alipayMonthLimit',
    dayCumulate: 'alipayDayCumulate',
    monthCumulate: 'alipayMonthCumulate',
  },
  {
    type: '微信',
    single: 'wechatOnceLimit',
    dayLimit: 'wechatDayLimit',
    monthLimit: 'wechatMonthLimit',
    dayCumulate: 'wechatDayCumulate',
    monthCumulate: 'wechatMonthCumulate',
  },
];

const transRules: any[] = [
  {
    type: '银行卡交易',
    data: bankTrans,
  },
  {
    type: '扫码交易',
    data: scanTrans,
  },
];

onMounted(() => {
  getTransLimit();
});

async function getTransLimit() {
  const data = await MerchApi.cumulateLimitDetail();
  ruleInfo.value = Object.assign({}, data, data?.cumulate);
}

/** 转换金额 */
function formatAmount(amount: number): string {
  if (!amount)
    return '0.00';

  amount = Number(amount);

  // 如果金额小于 1000，直接返回
  if (amount < 1000) {
    return NP.round(amount, 2) as unknown as string;
  }
  // 如果金额在 1000 到 10000 之间，转换为千单位并保留两位小数
  else if (amount < 10000) {
    return `${NP.round(NP.divide(amount, 1000), 2)}千`;
  }
  // 如果金额大于等于 10000，转换为万单位并保留两位小数
  else {
    return `${NP.round(NP.divide(amount, 10000), 2)}万`;
  }
}
</script>

<style lang="scss" scoped>
:deep(.row-table-head){
  @apply break-all bg-primary p-10rpx text-center font-medium;
  @apply border border-solid border-#f3f5f7;
}

:deep(.row-table-body){
  @apply break-all p-10rpx text-center text-28rpx;
  @apply border border-solid border-#f3f5f7 border-t-transparent;
}

.round-card{
  @apply mb-20rpx flex flex-col rounded-lg bg-white p-20rpx leading-relaxed;
}
</style>
