<!-- 小程序过渡页 -->
<template>
  <view class="h-full flex items-center justify-center">
    <view class="loader" />
  </view>
</template>

<script setup lang="ts">
import { getToken } from '@/utils/auth';

onShow(() => {
  preprocessing();
});

function preprocessing() {
  if (getToken()) {
    uni.switchTab({ url: '/pages/tab/home/<USER>' });
  }
  else {
    // #ifdef WEB
    uni.reLaunch({ url: '/pages/common/login/index' });
    // #endif

    // #ifdef MP-WEIXIN
    uni.reLaunch({ url: '/pages/common/tourist/index' });
    // #endif
  }
}
</script>

<style lang="scss" scoped>
.loader {
  --w:10ch;

  overflow: hidden;
  width:var(--w);
  font-size: 30px;
  font-family: monospace;
  white-space: nowrap;
  text-shadow:
    calc(-1*var(--w)) 0,
    calc(-2*var(--w)) 0,
    calc(-3*var(--w)) 0,
    calc(-4*var(--w)) 0,
    calc(-5*var(--w)) 0,
    calc(-6*var(--w)) 0,
    calc(-7*var(--w)) 0,
    calc(-8*var(--w)) 0,
    calc(-9*var(--w)) 0;
  font-weight: bold;
  letter-spacing: var(--w);
  animation: l16 2s infinite;
}

.loader::before {
  content:"Loading...";
}

@keyframes l16 {
  20% {text-shadow:
    calc(-1*var(--w)) 0,
    calc(-2*var(--w)) 0 red,
    calc(-3*var(--w)) 0,
    calc(-4*var(--w)) 0 #ffa516,
    calc(-5*var(--w)) 0 #63fff4,
    calc(-6*var(--w)) 0,
    calc(-7*var(--w)) 0,
    calc(-8*var(--w)) 0 green,
    calc(-9*var(--w)) 0;}

  40% {text-shadow:
    calc(-1*var(--w)) 0,
    calc(-2*var(--w)) 0 red,
    calc(-3*var(--w)) 0 #e945e9,
    calc(-4*var(--w)) 0,
    calc(-5*var(--w)) 0 green,
    calc(-6*var(--w)) 0 orange,
    calc(-7*var(--w)) 0,
    calc(-8*var(--w)) 0 green,
    calc(-9*var(--w)) 0;}

  60% {text-shadow:
    calc(-1*var(--w)) 0 lightblue,
    calc(-2*var(--w)) 0,
    calc(-3*var(--w)) 0 #e945e9,
    calc(-4*var(--w)) 0,
    calc(-5*var(--w)) 0 green,
    calc(-6*var(--w)) 0,
    calc(-7*var(--w)) 0 yellow,
    calc(-8*var(--w)) 0 #ffa516,
    calc(-9*var(--w)) 0 red;}

  80% {text-shadow:
    calc(-1*var(--w)) 0 lightblue,
    calc(-2*var(--w)) 0 yellow,
    calc(-3*var(--w)) 0 #63fff4,
    calc(-4*var(--w)) 0 #ffa516,
    calc(-5*var(--w)) 0 red,
    calc(-6*var(--w)) 0,
    calc(-7*var(--w)) 0 grey,
    calc(-8*var(--w)) 0 #63fff4,
    calc(-9*var(--w)) 0 ;}
}
</style>
