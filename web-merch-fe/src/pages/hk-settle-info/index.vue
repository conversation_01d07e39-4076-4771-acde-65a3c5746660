<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <template #top>
        <view class="p-20rpx pb-0">
          <wd-notice-bar text="银联商户报备失败，可选择名下已报备过的结算卡重新报备" prefix="help-circle" :scrollable="false" />
        </view>
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <wd-radio-group v-model="current" custom-class="custom-radio-group-class" shape="dot">
          <wd-radio v-for="(item, key) in datasource" :key="key" :value="JSON.stringify(item)">
            <view class="flex flex-col text-left">
              <view>{{ item.bankAccountName }}</view>
              <view class="my-10rpx flex items-center">
                <text>
                  {{ item.bankName }}
                </text>
                <text class="ml-2px">
                  ({{ item.bankAccount }})
                </text>
              </view>
              <view>{{ item.mchntCnAbbr }}</view>
            </view>
          </wd-radio>
        </wd-radio-group>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="p-40rpx">
          <wd-button block size="large" type="primary" :disabled="!current" @click="handleSubmitSettleInfo">
            提交
          </wd-button>
        </view>
      </template>
    </page-paging>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni';
import { MerchReportApi } from '@/api/report';

const toast = useToast();
const message = useMessage();

const pagingRef = ref();

const datasource = ref<any[]>([]);

const where = reactive({
  chnMerchNo: '',
  channelCode: '',
});

const current = ref<any>(null);

onLoad((query) => {
  where.chnMerchNo = query?.chnMerchNo || '';
  where.channelCode = query?.channelCode || '';
});

onShow(() => {
  pagingRef.value?.reload();
});

function queryDataSource() {
  MerchReportApi.queryHkpayFetchChlSettleCards({ ...where })
    .then((data) => {
      pagingRef.value.complete(data?.list || []);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

async function handleSubmitSettleInfo() {
  const item = JSON.parse(current.value);

  const res = await MerchReportApi.hkpayChlSettleCardsCompare({ chnMerchNo: where.chnMerchNo, channelCode: where.channelCode, bankAccountNo: item.bankAccount });
  // 是否需要补充 0-不需要 1-需要
  const { needSave, resMsg } = res;

  if (![0, 1].includes(needSave)) {
    message.alert({
      msg: resMsg || '业务异常',
      title: '温馨提示',
    });
    return;
  }

  if (needSave) {
    uni.navigateTo({
      url: `/pages/hk-settle-info/replenish-settle-info?chnMerchNo=${where.chnMerchNo}&channelCode=${where.channelCode}&bankAccountNo=${item.bankAccount}`,
    });
  }
  else {
    const params = {
      chnMerchNo: where.chnMerchNo,
      channelCode: where.channelCode,
      accNo: item.bankAccount,
      needSave,
    };
    await MerchReportApi.hkpayCommitChlSettleCard(params);
    toast.success({
      msg: '操作成功',
      closed: () => {
        uni.navigateBack();
      },
    });
  }
}
</script>

<style lang="scss" scoped>
:deep(.custom-radio-group-class) {
  .wd-radio{
    padding: 22px;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .is-checked{
    background-color: #f8f9fa;
    border-color:transparent
  }

  .wd-radio__label{
    width: 100%;
  }

  .wd-radio__shape{
    background-color: transparent;
  }
}
</style>
