<template>
  <view class="h-full overflow-y-scroll">
    <view class="gap-primary" />
    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-picker
          v-model="form.chnMerchNo"
          prop="chnMerchNo"
          label="通道商户选择"
          label-width="100px"
          placeholder="请选择通道商户"
          :columns="channelMerchColumns"
          align-right
        />
        <wd-input
          v-model="form.cardNo"
          prop="cardNo"
          label="银行卡号"
          label-width="100px"
          placeholder="请输入银行卡号"
          clearable align-right
        />
        <wd-input
          v-model="form.cvv2"
          prop="cvv2"
          label="cvv2"
          label-width="100px"
          placeholder="请输入cvv2"
          clearable align-right
        />
        <wd-datetime-picker
          v-model="validDateValue" prop="validDate"
          label="有效期" label-width="100px"
          type="date" :max-date="maxDate" align-right
          :default-value="defaultValidDateValue"
          @confirm="onConfirmValidDateValue"
        />
        <wd-input
          v-model="form.mobile" prop="mobile"
          label="预留手机号" label-width="100px"
          placeholder="请输入预留手机号"
          align-right clearable
        />
        <wd-cell />
      </wd-cell-group>

      <view class="mt40px px-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>
    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import { CHANNEL_CODE } from '@/config/setting';
import { IncreaseQuotaApi } from '@/api/increase-quota';
import { MerchReportApi } from '@/api/report';

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive({
  // channelCode: CHANNEL_CODE,
  chnMerchNo: '', // 通道商户号必填
  cardNo: '', // 卡号 (必填)
  cvv2: '', // cvv2 (必填)
  validDate: '', // 有效期 (必填)
  mobile: '', // 手机号 (必填)
});
// 规则
const rules: FormRules = {
  chnMerchNo: [{ required: true, message: '请选择通道商户' }],
  cardNo: [{ required: true, message: '请输入卡号' }],
  cvv2: [{ required: true, message: '请输入cvv2' }],
  validDate: [{ required: true, message: '请选择' }],
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
};

const channelMerchColumns = ref<any[]>([]);

const validDateValue = ref('');
const maxDate = dayjs('2099-12-31').valueOf();
const defaultValidDateValue = dayjs().valueOf();
const onConfirmValidDateValue = () => {
  form.validDate = dayjs(validDateValue.value).format('YYYY-MM-DD');
};

onLoad(() => {
  queryChannelMerch();
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  await IncreaseQuotaApi.bindCardUpLimit(form);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

async function queryChannelMerch() {
  const data = await MerchReportApi.findPage({ channelCode: CHANNEL_CODE, reportRespCode: 2, pageNo: 1, pageSize: 1000 });
  const filterData = data?.rows.map((item: any) => {
    return {
      label: item.chnMerchName,
      value: item.chnMerchNo,
    };
  });
  channelMerchColumns.value = filterData;
}
</script>
