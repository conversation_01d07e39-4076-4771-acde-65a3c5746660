<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text>
              {{ item.walletTypeName }}
            </text>
          </view>

          <view class="py-30rpx">
            <view class="flex flex-col items-center px-30rpx">
              <text>总结算金额</text>
              <text class="mt-10rpx text-32rpx">
                {{ item.profitBalance }}
              </text>
            </view>

            <view class="mt-30rpx w-full flex items-center">
              <view class="w-33.3% flex flex-col items-center">
                <text>可提现金额</text>
                <text class="mt-10rpx text-32rpx">
                  0.00
                </text>
              </view>
              <view class="flex grow flex-col items-center border-x-1px border-#edf0f3 border-x-solid">
                <text>T1账户余额</text>
                <text class="mt-10rpx text-32rpx">
                  0.00
                </text>
              </view>
              <view class="w-33.3% flex flex-col items-center">
                <view class="flex items-center">
                  <text>冻结金额</text>
                  <view class="flex items-center justify-center" @click="showPromptInfo()">
                    <i class="i-mdi-chat-question-outline ml-6rpx" />
                  </view>
                </view>
                <text class="mt-10rpx text-32rpx">
                  0.00
                </text>
              </view>
            </view>
          </view>

          <view class="flex border-1px border-#edf0f3 border-t-solid text-center">
            <view class="grow py-20rpx" @click="handleFindRecord(item)">
              提现记录
            </view>
            <view class="w-1px bg-#edf0f3" />
            <view class="grow py-20rpx text-#4d80f0" @click="handleInitiate(item)">
              发起提现
            </view>
          </view>
        </view>
      </view>
    </page-paging>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni';
import { TransApi } from '@/api-org/trans';

const message = useMessage();

const pagingRef = ref();

const datasource = ref<any[]>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function queryDataSource() {
  TransApi.profitRewardBalance()
    .then((data) => {
      // 组装数据
      const assembleData = [
        {
          walletType: '600',
          walletTypeName: '交易分润钱包',
          profitBalance: data.profitBalance || '0.00',
        },
        {
          walletType: '300',
          walletTypeName: '活动分润钱包',
          profitBalance: data.rewardBalance || '0.00',
        },
      ];
      pagingRef.value.complete(assembleData);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

function handleInitiate(item: any) {
  uni.navigateTo({
    url: `/pages-org/drawcash/initiate/index?walletType=${item.walletType}`,
  });
}

function handleFindRecord(item: any) {
  uni.navigateTo({
    url: `/pages-org/drawcash/draw-record/index?walletType=${item.walletType}`,
  });
}

function showPromptInfo() {
  message.alert({
    msg: '冻结金额说明',
    title: '冻结金额说明',
  });
}
</script>
