<template>
  <view class="h-full">
    <view class="gap-primary" />
    <view class="p-20rpx">
      <wd-cell
        title="到账银行卡" vertical custom-class="!bg-#f5f5dc rounded-lg"
      >
        <view v-if="isCheckedSettleCard" class="flex items-center">
          <i class="i-mdi-bank-circle-outline size-60rpx shrink-0 text-red-6" />
          <view class="ml-20rpx flex grow flex-col items-start">
            <text>{{ settleCardInfo.bankName }}</text>
            <text class="my-10rpx">
              {{ settleCardInfo.accountType === 'G' ? '对公' : '对私' }}
            </text>
            <text class="font-semibold">
              {{ settleCardInfo.idCardNoMask }}
            </text>
          </view>
        </view>
      </wd-cell>

      <view class="mt-20rpx overflow-hidden rounded-lg bg-#f5f5f5 px-30rpx">
        <view class="flex border border-b-#edf0f3 border-b-solid py-22rpx">
          <text class="shrink-0 text-#666">
            账户余额
          </text>
          <text class="ml-30rpx grow break-all text-right text-32rpx">
            {{ drawInfo.profitBalance }}
          </text>
        </view>

        <view class="mt-20rpx flex justify-between">
          <text>
            提现金额
          </text>
          <text class="text-#4d80f0" @click="handleAllIn">
            全部提现
          </text>
        </view>

        <view class="my-20rpx flex">
          <text class="mt-20rpx">
            ¥
          </text>
          <view class="ml-10rpx grow">
            <wd-input
              v-model="withdrawAmount"
              custom-class="custom-input-class"
              no-border
              placeholder="0.00"
              type="digit"
            />
          </view>
        </view>
      </view>

      <view class="py-80rpx">
        <wd-button block size="large" :disabled="isBanSave" @click="save">
          提现
        </wd-button>
      </view>
    </view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni';
import { DrawcashApi } from '@/api-org/drawcash';
import { TransApi } from '@/api-org/trans';
import { Dialog, deepClone } from '@/utils';
import { emitter } from '@/utils/emitter';
import { UserApi } from '@/api-org/user';
import { SettleCardApi } from '@/api-org/settle-card';

const withdrawAmount = ref('');

const toast = useToast();
const message = useMessage();

const settleCardInfo = ref<any>({});
const isCheckedSettleCard = computed(() => !!settleCardInfo.value.id);

const drawInfo = ref<any>({
  walletType: '',
  profitBalance: '0.00',
  minLimitAmount: '',
  maxLimitAmount: '',
});

const taxInfo = ref<any>({
  rate: '', // 提现税率（%）
  singleFee: '', // 提现单笔费用
  taxFee: '', // 提现税额
});

const showTaxInfo = ref(false);

const isBanSave = computed(() => {
  return !isCheckedSettleCard.value || !withdrawAmount.value || !drawInfo.value.remitChannelNo;
});

onLoad((query: any) => {
  // 设置钱包类型
  drawInfo.value.walletType = query.walletType;
  // 获取钱包余额
  getProfitBalance();
  // 获取结算卡
  querySettleCardList();
});

/**
 * 查询结算卡列表
 */
async function querySettleCardList() {
  const data = await SettleCardApi.findPage();
  if (data.length) {
    settleCardInfo.value = data[0];
    getWithdrawRemitChannel();
  }
}

async function draw() {
  const params = {
    walletType: drawInfo.value.walletType,
    withdrawAmount: withdrawAmount.value,
    remitChannelNo: drawInfo.value.remitChannelNo,
    settleCardId: settleCardInfo.value.id,
    bankAccountType: settleCardInfo.value.accountType,
    isSelectBill: 0,
  };

  DrawcashApi.walletWithdraw(params).then(() => {
    toast.success({
      msg: '操作成功',
      closed: () => {
        uni.navigateBack();
      },
    });
  }).finally(() => {
    showTaxInfo.value = false;
  });
}

async function save() {
  if (drawInfo.value.minLimitAmount) {
    if (Number(withdrawAmount.value) < Number(drawInfo.value.minLimitAmount)) {
      return Dialog(`提现金额不能小于${drawInfo.value.minLimitAmount}`);
    }
  }

  if (drawInfo.value.maxLimitAmount) {
    if (Number(withdrawAmount.value) > Number(drawInfo.value.maxLimitAmount)) {
      return Dialog(`提现金额不能大于${drawInfo.value.maxLimitAmount}`);
    }
  }

  if (Number(withdrawAmount.value) > Number(drawInfo.value.profitBalance)) {
    return Dialog('提现金额不能大于账户余额');
  }

  if (settleCardInfo.value.accountType === 'S') {
    await queryOrgUserInfo();
  }

  getWithdrawTaxInfo();
}

async function getWithdrawTaxInfo() {
  const data = await DrawcashApi.getWithdrawTaxInfo({
    remitChannelNo: drawInfo.value.remitChannelNo,
    walletType: drawInfo.value.walletType,
    withdrawAmount: withdrawAmount.value,
    bankAccountType: settleCardInfo.value.accountType,
  });

  taxInfo.value = Object.assign({}, data);

  showTaxInfo.value = true;
}

async function queryOrgUserInfo() {
  const data = await UserApi.getOrgUserInfo();
  const { dlgSignStatus, dlgAuthStatus, hkwlSignStatus, settleChannelCode: settleChannel } = data || {};

  if (settleChannel === '1002') {
    if ([0, 3].includes(dlgSignStatus) || [0, 3].includes(dlgAuthStatus)) {
      uni.navigateTo({ url: '/pages-org/drawcash/dlg-sign/index' });
      return Promise.reject('DLG_SIGN');
    }
    if (dlgAuthStatus === 1) {
      message.alert('钉灵工认证中，请稍后再试');
      return Promise.reject('DLG_SIGN');
    }
    if (dlgSignStatus === 1) {
      message.alert('钉灵工签约中，请稍后再试');
      return Promise.reject('DLG_SIGN');
    }
  }
  else if (settleChannel === '1004') {
    if (hkwlSignStatus === 0) {
      uni.navigateTo({ url: '/pages-org/drawcash/dlg-sign/index' });
      return Promise.reject('DLG_SIGN');
    }
  }

  return Promise.resolve();
}

async function getProfitBalance() {
  const res = await TransApi.profitRewardBalance();

  switch (drawInfo.value.walletType) {
    case '600':
      drawInfo.value.profitBalance = res.profitBalance;
      break;
    case '300':
      drawInfo.value.profitBalance = res.rewardBalance;
      break;
  }

  drawInfo.value.profitBalance = drawInfo.value.profitBalance || '0.00';
}

async function getWithdrawRemitChannel() {
  if (!isCheckedSettleCard.value)
    return;

  const data = await DrawcashApi.getWithdrawRemitChannel({
    walletType: drawInfo.value.walletType,
    bankAccountType: settleCardInfo.value.accountType,
  });

  drawInfo.value = Object.assign(drawInfo.value, data);
}

function onSelectBankCard() {
  uni.navigateTo({ url: `/pages-org/settle-card/index?isSelect=1` });
  emitter.on('picker-bank-card', (item: any) => {
    item = deepClone(item);
    settleCardInfo.value = item;

    getWithdrawRemitChannel();
  });
}

function handleAllIn() {
  withdrawAmount.value = drawInfo.value.profitBalance;
}
</script>

<style lang="scss" scoped>
:deep(.custom-input-class) {
  background-color: transparent !important;

/* stylelint-disable-next-line selector-class-pattern */
.wd-input__inner{
  font-size: 50rpx;
  font-weight: 600;
}
}
</style>
