<template>
  <view class="h-full overflow-y-scroll">
    <view class="rounded-lg bg-primary py-20px">
      <view class="flex flex-wrap justify-center">
        <template v-for="(item, key) in fileList" :key="key">
          <view v-if="item.show" class="basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              :disabled="isUpdate"
              custom-class="w-full"
              @choose="(value) => onChooseFile(value, item)"
            />
          </view>
        </template>
      </view>
    </view>

    <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
      <wd-cell-group border>
        <wd-cell title="结算卡类型" title-width="100px" center>
          <wd-radio-group v-model="form.accountType" :disabled="isUpdate" shape="dot" inline custom-class="flex items-center">
            <wd-radio value="S">
              对私
            </wd-radio>
            <wd-radio v-if="merchGrade === 'A'" value="G">
              对公
            </wd-radio>
          </wd-radio-group>
        </wd-cell>
        <wd-input
          v-model="form.bankAccountName"
          label="银行卡户名" label-width="100px" placeholder="银行卡户名"
          readonly align-right
        />
        <wd-input
          v-model="form.bankAccountNo" prop="bankAccountNo"
          label="银行卡卡号" placeholder="可图片自动识别" label-width="88px"
          align-right clearable
          :readonly="isUpdate"
        />
        <wd-cell
          title="开户行所在地" title-width="100px"
          prop="city" is-link
          @click="onSelectArea"
        >
          <wd-textarea
            v-model="bankArea"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行总行" title-width="100px"
          prop="typeCode" is-link
          @click="onSelectBankType"
        >
          <wd-textarea
            v-model="form.bankName"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行支行" title-width="100px"
          prop="bankBranch" is-link
          @click="onSelectBankSub"
        >
          <wd-textarea
            v-model="form.bankBranch"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-input
          v-model="form.mobile" prop="mobile"
          label="预留手机号" label-width="100px" placeholder="请输入预留手机号"
          align-right
        />
        <wd-cell />
      </wd-cell-group>
      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { emitter } from '@/utils/emitter';
import type { BankAccountInfo } from '@/api/bank-card/types';
import { BankCardApi } from '@/api/bank-card';
import { Toast, deepClone } from '@/utils';
import { CommonApi } from '@/api/common';
import { MerchApi } from '@/api/merch';
import { CHANNEL_CODE } from '@/config/setting';

const bankArea = ref('');
const toast = useToast();

const isUpdate = ref(false);

// 表单
const form: BankAccountInfo = reactive<any>({
  accountType: 'S',
  bankAccountNo: '',
});
// 规则
const rules: FormRules = {
  city: [{ required: true, message: '请选择' }],
  typeCode: [{ required: true, message: '请选择' }],
  bankBranch: [{ required: true, message: '请选择' }],
  bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
};
const formRef = ref<FormInstance | null>(null);

const merchGrade = ref('');

const accountType = computed(() => form.accountType);
// 文件列表
const fileList = ref <GivenUploadProps[] > (
  [
    {
      fileName: '银行卡正面',
      placeholder: require('@/static/images/bank_card.png'),
      fileData: '',
      fileType: 3,
      show: computed(() => accountType.value === 'S'),
    },
    {
      fileName: '开户许可证',
      placeholder: require('@/static/images/bank_card.png'),
      fileData: '',
      fileType: 16,
      show: computed(() => accountType.value === 'G'),
    },
  ],
);

onLoad((query) => {
  isUpdate.value = query?.isUpdate === '1';
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: isUpdate.value ? '修改收款卡' : '新增收款卡',
  });
});

onMounted(() => {
  queryMerchInfo();

  const instance: any = getCurrentInstance()?.proxy || {};
  const eventChannel = instance?.getOpenerEventChannel();

  eventChannel.on('route-params', ({ data }: any) => {
    data = deepClone(data);
    Object.assign(form, data, {});
    bankArea.value = [data.provinceName, data.cityName].join('');

    fileList.value.forEach((item) => {
      data.imageJsonList.forEach((item2: any) => {
        if (item2?.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    },
    );
  });
});

async function queryMerchInfo() {
  const data = await MerchApi.queryMerchInfo();
  merchGrade.value = data?.merchant?.grade;
  form.accountType = 'S';
  form.bankAccountName = data?.merchantDetail?.legalName;
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageJsonList = await uploadFile();
  form.imageJsonList = imageJsonList;

  // 默认借记卡
  form.cardType = form.cardType || 1;

  let result;
  if (isUpdate.value) {
    result = BankCardApi.editBankCard;
  }
  else {
    result = BankCardApi.addBankCard;
  }
  await result(form);
  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

/*
 * 校验文件
 */
async function checkFile() {
  const isCheckPass = fileList.value.every((f) => {
    if (!f.fileData && f.show !== false) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileListHasVal = fileList.value.filter(i => !!i.fileData);
  const noChangeFiles: any = [];
  const changeedFiles: any = [];
  fileListHasVal.forEach((i: any) => {
    if (/^(https?:)/.test(i.fileData)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList: any = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

async function onChooseFile(value: string, item: GivenUploadProps) {
  if (item.fileType === 3) {
    const res = await CommonApi.ocrBankCard({ imgFile: value });
    form.bankAccountNo = res?.cardNum;

    const cardTypeMap: Record<string, number> = {
      DC: 1,
      CC: 2,
      SCC: 3,
      PC: 4,
    };
    form.cardType = cardTypeMap[res?.cardType] as any;
  }
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index' });
  emitter.on('picker-area', (data: any) => {
    const [province, city] = data;
    bankArea.value = data.map((item: any) => item.areaName).join('');
    form.province = province.areaCode;
    form.city = city.areaCode;

    form.bankBranch = '';
    form.bankChannelNo = '';
  });
}

function onSelectBankType() {
  uni.navigateTo({ url: `/pages/picker-view/bank/bank-type` });
  emitter.on('picker-bank-type', (data: any) => {
    form.bankName = data.typeName;
    form.typeCode = data.typeCode;

    form.bankBranch = '';
    form.bankChannelNo = '';
  });
}

function onSelectBankSub() {
  const { typeCode, city, province } = form;
  if (!city)
    return Toast('请选择开户行所在地');
  if (!typeCode)
    return Toast('请选择开户行总行');

  const query = {
    typeCode,
    provinceCode: province,
    cityCode: city,
  };
  uni.navigateTo({
    url: `/pages/picker-view/bank/bank-sub?where=${encodeURIComponent(JSON.stringify(query))}`,
  });

  emitter.on('picker-bank-sub', (data: any) => {
    form.bankBranch = data.bankName;
    form.bankChannelNo = data.clearChannelNo;
  });
}
</script>
