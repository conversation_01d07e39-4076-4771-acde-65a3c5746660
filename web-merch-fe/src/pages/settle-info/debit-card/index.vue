<template>
  <view class="h-full overflow-y-scroll">
    <!-- 分割块 -->
    <view class="gap-primary" />

    <!-- 主体 -->
    <view class="p-20rpx">
      <view
        v-for="(item, key) in datasource" :key="key"
        class="mb-20rpx rounded-lg p-30rpx text-white even:bg-#fa4350 odd:bg-#f0883a"
      >
        <view class="flex items-center">
          <view class="shrink-0">
            <view class="size-80rpx rounded-full bg-white p-10rpx">
              <i class="i-mdi-bank-circle-outline size-full shrink-0 text-#846a27" />
            </view>
          </view>
          <view class="ml-30rpx flex grow flex-col items-start text-26rpx leading-relaxed">
            <text>{{ item.bankAccountName }}</text>
            <text>{{ item.bankName }}</text>
            <text>{{ item.accountType === 'S' ? '对私' : item.accountType === 'G' ? '对公' : '' }}</text>
            <text class="font-medium">
              {{ item.bankAccountNoMask }}
            </text>
          </view>
        </view>

        <view class="text-right">
          <!-- <wd-button type="info" size="small" custom-class="mr-20rpx" @click="updateBankCard(item)">
            编辑收款卡
          </wd-button> -->
          <wd-button v-if="isSelect" type="info" size="small" @click="onSelect(item)">
            选择
          </wd-button>
          <wd-button v-else type="info" size="small" @click="onDelete(item)">
            删除
          </wd-button>
        </view>
      </view>

      <!-- <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="updateBankCard(null)">
          新增收款卡
        </wd-button>
      </view> -->
    </view>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni';
import { BankCardApi } from '@/api/bank-card';
import { emitter } from '@/utils/emitter';

const message = useMessage();

const isSelect = ref(false);

const datasource = ref<any>([]);

const where = reactive({
  accountType: '',
});

onLoad((query) => {
  isSelect.value = query?.isSelect === '1';
  where.accountType = query?.accountType || '';
});

onShow(() => {
  queryDataSource();
});

onUnmounted(() => {
  emitter.off('picker-bank-card');
});

async function queryDataSource() {
  const res = await BankCardApi.queryBankCard(where);
  datasource.value = res || [];
}

function onSelect(item: object) {
  emitter.emit('picker-bank-card', item);
  uni.navigateBack();
}

function updateBankCard(item?: any) {
  uni.navigateTo({
    url: `/pages/settle-info/debit-card/update-debit-card?isUpdate=${item ? 1 : 0}`,
    success(res) {
      if (!item)
        return;

      res.eventChannel.emit('route-params', { data: item });
    },
  });
}

function onDelete(item: any) {
  message
    .confirm({
      msg: '确定要删除该收款卡？',
      title: '提示',
    })
    .then(async () => {
      await BankCardApi.deleteBankCard({
        id: item.id,
      });
      queryDataSource();
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
