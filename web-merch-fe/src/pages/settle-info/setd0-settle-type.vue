<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <view class="pb-20rpx">
          <wd-notice-bar text="查看结算规则" prefix="help-circle" :scrollable="false" @click="handleViewRule" />
        </view>
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="p-16rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="商户名称" :value="item.chnMerchName" />
              <wd-cell title="商户编号" :value="item.chnMerchNo" />
              <wd-cell title="D0结算类型" :value="d0SettleTypeMap[item.d0SettleType] || '--'" />
            </wd-cell-group>
          </view>

          <view class="flex justify-center border border-#f4f4f4 border-t-solid p-10rpx">
            <wd-select-picker v-model="settleForm.d0SettleType" type="radio" use-default-slot title="请选择结算类型" :columns="columns" @confirm="onConfirm">
              <wd-button :round="false" type="info" size="small" plain hairline @click="handleChangeType(item)">
                变更D0结算类型
              </wd-button>
            </wd-select-picker>
          </view>
        </view>
      </view>
    </page-paging>
    <!-- 结算规则弹框 -->
    <wd-message-box selector="wd-message-box-settle-rule" custom-class="wd-message-box-settle-rule">
      <view class="rules-content">
        <wd-text text="批次1：23:00-05:59 的交易D1或T1 12:00左右出款" />
        <wd-text text="批次2：06:00-11:59 的交易D0 12:30左右出款" />
        <wd-text text="批次3：12:00-16:59 的交易D0 17:30左右出款" />
        <wd-text text="批次4：17:00-20:59 的交易DO 21:30左右出款" />
        <wd-text text="批次5：21:00-22:59 的交易DO 23:30左右出款" />
        <wd-text text="注：合并结算开通或关闭均D1早上3点生效" type="error" />
        <wd-text text="注：单笔交易金额小于等于100元的交易转D1结算" type="error" />
      </view>
    </wd-message-box>
    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni';
import { MerchReportApi } from '@/api/report';
import { CHANNEL_CODE } from '@/config/setting';

const toast = useToast();
const message = useMessage('wd-message-box-settle-rule');

const pagingRef = ref();

const d0SettleTypeMap: any = {
  0: '秒到',
  1: '合并到账',
};

const columns = [
  {
    label: '秒到',
    value: 0,
  },
  {
    label: '合并到账',
    value: 1,
  },

];

const settleForm = reactive<any>({
  channelCode: CHANNEL_CODE,
  chnMerchNo: '',
  d0SettleType: '',
});

const datasource = ref<any[]>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function queryDataSource() {
  MerchReportApi.findChnMerchList({
    channelCode: CHANNEL_CODE,
  })
    .then((data) => {
      pagingRef.value.complete(data.chnMerchList);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

async function onConfirm() {
  await MerchReportApi.d0SettleTypeModify(settleForm);
  pagingRef.value?.reload();
  toast.success({
    msg: '操作成功',
  });
}

function handleChangeType(item: any) {
  settleForm.chnMerchNo = item.chnMerchNo;
  settleForm.d0SettleType = item.d0SettleType;
}

function handleViewRule() {
  message
    .alert({
      title: '结算规则',
    })
    .then(() => {
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}

:deep(.rules-content) {
  .wd-text{
    display: block;
    margin-bottom: 4px;
    text-align: left !important;
  }
}

:deep(.wd-message-box-settle-rule) {
  width: 90vw !important;
}
</style>
