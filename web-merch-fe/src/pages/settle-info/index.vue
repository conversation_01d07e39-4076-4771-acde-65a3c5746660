<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <template #top>
        <view class="h-10px bg-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text class="text-28rpx font-medium">
              {{ channelMap[item.channelCode] || '--' }}-{{ item.chnMerchNo }}-{{ item.chnMerchName }}
            </text>
          </view>

          <view class="p-16rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="结算人姓名" :value="item.bankAccountName" />
              <wd-cell title="结算卡卡号" :value="item.bankAccountNoMask" />
              <wd-cell title="结算支行名称" :value="item.bankBranch" />
              <wd-cell title="结算银行名称" :value="item.bankName" />
              <wd-cell title="变更处理状态" :value="changeChannelStatusMap[item.changeChannelStatus] || '--'" />
              <wd-cell v-if="item.changeChannelStatus === 2 && item.changeRemark" title="驳回原因" :value="item.changeRemark" />
            </wd-cell-group>
          </view>

          <view v-if="item.supportChangeSettleCard === 1 && item.changeChannelStatus !== 3" class="flex justify-center border border-#f4f4f4 border-t-solid p-10rpx">
            <wd-button v-if="item.changeChannelStatus === 6" :round="false" type="info" size="small" plain hairline @click="toReplenishSettleCard(item)">
              补充结算信息
            </wd-button>
            <wd-button v-else type="info" :round="false" size="small" plain hairline @click="toChangeSettleCard(item)">
              变更结算信息
            </wd-button>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { CommonApi } from '@/api/common';
import { MerchReportApi } from '@/api/report';

const pagingRef = ref();

const datasource = ref<any[]>([]);

const changeChannelStatusMap: any = {
  0: '初始状态',
  1: '变更成功',
  2: '变更失败',
  3: '审核中',
  6: '审核中,待处理',
};

const channelMap: any = {};

onShow(() => {
  getChannelList();
  pagingRef.value?.reload();
});

function queryDataSource() {
  MerchReportApi.queryChnMerchSettleInfo()
    .then((data) => {
      pagingRef.value.complete(data);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

async function getChannelList() {
  let data = await CommonApi.getChannelList();
  data = data || [];
  data.forEach((item: any) => {
    channelMap[item.channelCode] = item.channelName;
  });
}

function toChangeSettleCard(item: any) {
  uni.navigateTo({
    url: `/pages/settle-info/update-settle-info?chnMerchNo=${item.chnMerchNo}&accountType=${item.accountType}&channelCode=${item.channelCode}`,
  });
}
function toReplenishSettleCard(item: any) {
  uni.navigateTo({
    url: `/pages/settle-info/replenish-settle-info?chnMerchNo=${item.chnMerchNo}&accountType=${item.accountType}&channelCode=${item.channelCode}`,
  });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
