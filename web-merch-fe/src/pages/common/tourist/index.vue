<template>
  <view class="min-h-screen flex flex-col from-[#F7F7F7] to-white bg-gradient-to-b">
    <!-- 顶部欢迎区域 -->
    <view class="rounded-b-2xl bg-[#E6F0FA] p-4 pt-0 text-center">
      <wd-navbar custom-class="!bg-transparent" :bordered="false" />
      <text class="animate-fade-in text-2xl text-gray-900 font-bold">
        万商云Pro
      </text>
      <text class="animate-fade-in-delay mt-1 text-sm text-gray-500">
        登录后体验更多功能
      </text>
    </view>

    <!-- 功能展示区域 -->
    <view class="flex-1 px-4 pb-4 pt-2">
      <view class="space-y-3">
        <view
          v-for="(item, index) in features"
          :key="index"
          class="animate-slide-in flex flex-row items-center justify-between border border-gray-200 rounded-2xl bg-white bg-opacity-95 p-4 shadow-[0_4px_12px_rgba(0,0,0,0.1)] transition-all duration-300 hover:shadow-[0_6px_16px_rgba(0,0,0,0.15)]"
          :style="{ animationDelay: `${index * 0.1}s` }"
          @click="showLoginPrompt"
        >
          <view class="flex items-center">
            <text :class="[item.icon, item.iconColor]" class="mr-4 text-4xl" />
            <text class="text-base text-gray-800 font-medium">
              {{ item.name }}
            </text>
          </view>
          <text class="text-xl text-gray-300">
            >
          </text>
        </view>
      </view>
    </view>

    <!-- 底部登录按钮 -->
    <view class="p-4">
      <button
        class="mx-auto w-3/5 rounded-xl bg-[#007AFF] bg-opacity-90 py-3 text-base text-white font-semibold shadow-[0_2px_8px_rgba(0,122,255,0.3)] transition-all duration-200 active:scale-95 active:brightness-90"
        @click="login"
      >
        立即登录
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { Toast } from '@/utils';

// 功能列表数据，使用 mdi 图标
const features = [
  { name: '收益统计', icon: 'i-mdi:data', iconColor: 'text-green-400' },
  { name: '交易管理', icon: 'i-mdi:file-document', iconColor: 'text-green-400' },
  { name: '结算卡管理', icon: 'i-mdi:credit-card', iconColor: 'text-orange-400' },
  { name: '个人信息', icon: 'i-mdi:account', iconColor: 'text-pink-400' },
];

// 登录方法
const login = () => {
  uni.navigateTo({
    url: '/pages/common/login/index',
  });
};

// 点击功能时的提示
const showLoginPrompt = () => {
  Toast('请先登录哦~');
};
</script>

<style scoped>
/* 动画效果 */
.animate-fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.animate-fade-in-delay {
  animation: fadeIn 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) 0.3s;
}

.animate-slide-in {
  animation: slideIn 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(15px) scale(0.98);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
