<template>
  <view class="h-full overflow-y-scroll bg-white p-30rpx">
    <!-- logo -->
    <view class="mt-100rpx flex justify-center">
      <wd-img
        width="140rpx" height="140rpx"
        :src="logoImg"
        radius="8"
      />
    </view>

    <!-- 表单 -->
    <view class="form-wrap">
      <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
        <wd-input
          v-model="form.account" prop="account"
          placeholder="请输入手机号"
          label="+86" label-width="35px" center
        />

        <wd-row>
          <wd-col :span="17">
            <wd-input
              v-model="form.smsCode" prop="smsCode"
              placeholder="请输入验证码"
              label-width="35px" center use-label-slot
            >
              <template #label>
                <wd-icon name="lock-on" custom-class="label-icon-class" />
              </template>
            </wd-input>
          </wd-col>
          <wd-col :span="7">
            <view
              class="mt-20rpx text-center text-28rpx text-#4d80f0 font-semibold"
              @click="sendSmsCode"
            >
              {{ timer ? `${countdown}s后重发` : '获取验证码' }}
            </view>
          </wd-col>
        </wd-row>

        <wd-input
          v-model="form.password" prop="password" placeholder="请输入8-20位字母+数字组合的密码"
          label-width="35px" center use-label-slot
          show-password
        >
          <template #label>
            <wd-icon name="lock-on" custom-class="label-icon-class" />
          </template>
        </wd-input>

        <wd-input
          v-model="form.confirmPassword" prop="confirmPassword" placeholder="请再次输入密码"
          label-width="35px" center use-label-slot
          show-password
        >
          <template #label>
            <wd-icon name="lock-on" custom-class="label-icon-class" />
          </template>
        </wd-input>
      </wd-form>

      <view class="my-80rpx">
        <wd-button type="primary" size="large" block @click="save">
          注册
        </wd-button>
      </view>

      <view class="flex items-center justify-center text-28rpx text-#333333">
        已有账号 <i class="i-mdi-help size-12px" />
        <text class="ml-10rpx text-#4d80f0" @click="toLogin">
          登录
        </text>
      </view>
    </view>
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { sm4 } from 'sm-crypto';
import type { IRecordItem } from '../login/type';
import logoImg from '@/static/images/logo.jpg';
import { Toast } from '@/utils';
import { CommonApi } from '@/api/common';
import { UserApi } from '@/api/user';
import storage from '@/utils/storage';

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive({
  account: '', // 手机号，限制11位 必填
  password: '', // 登录密码 至少6位 必填
  smsCode: '', // 短信验证码 6位 必填
  sourceType: 1, // 来源标识 1.APP 2.PC 3.OTHER 4.H5 必填

  confirmPassword: '',
});

// 规则
const rules: FormRules = {
  account: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
  smsCode: [{ required: true, message: '请输入短信验证码' }],
  password: [{ required: true, pattern: /[a-z0-9]{8,20}/i, message: '密码必须为8-20位字母+数字组合' }],
  confirmPassword: [
    { required: true, message: '请再次输入密码', validator: (value) => {
      if (value === form.password) {
        return Promise.resolve();
      }
      else {
        return Promise.reject('两次密码不一致');
      }
    } },
  ],
};

async function save() {
  const { valid } = await formRef.value!.validate();
  if (!valid)
    return;

  await UserApi.register(form);
  // 保存新账号
  saveLoginAccount();
  toast.success({
    msg: '注册成功',
    closed: () => {
      toLogin();
    },
  });
}

// 持久存储key
const LoginRecordStoreKey = 'loginRecord';
// 密码处理
const secretPwd = {
  key: '0123456789abcdeffedcba9876543210',
  encrypt: (password: string) => {
    return sm4.encrypt(password, secretPwd.key);
  },
  decrypt: (password: string) => {
    return sm4.decrypt(password, secretPwd.key);
  },
};
/** 保存登录账号 */
function saveLoginAccount() {
  const recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey) || [];
  const { account, password } = form;
  const recordItem: IRecordItem = {
    account,
    password: secretPwd.encrypt(password),
  };

  // 如果存在先删除, 保证记录唯一且最新
  const hadIndex = recordList.findIndex(item => item.account === account);
  if (hadIndex !== -1) {
    recordList.splice(hadIndex, 1);
  }

  // 插入到最前面
  recordList.unshift(recordItem);

  storage.setJSON(LoginRecordStoreKey, recordList);
}

// 计时器
const timer = ref();
const countdown = ref(60);

/** 发送验证码 */
async function sendSmsCode() {
  if (timer.value)
    return;

  if (!/^1[3-9]\d{9}$/.test(form.account))
    return Toast('请输入正确的手机号');

  await CommonApi.sendSms({ mobile: form.account, smsBusinessType: 1, smsSendSource: 1 });
  Toast('验证码已发送');
  startTimer();
}

function startTimer() {
  if (timer.value)
    return;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value!);
      timer.value = null;
      countdown.value = 60;
    }
  }, 1000);
}

function toLogin() {
  uni.reLaunch({ url: '/pages/common/login/index' });
}
</script>

<style lang="scss" scoped>
.form-wrap {
  @apply mt-30rpx;

  :deep(.wd-input) {
    &.is-cell{
      @apply bg-primary mb-30rpx rounded;
    }

    .wd-input__label {
      &.is-required {
        padding-left: 0;
      }

      &::after {
        position: absolute;
        top: 10%;
        right: 0;
        left: auto;
        width: 1px;
        height: 80%;
        background-color: gray;
        content: "";
        transform: scale(0.5, 1);
      }
    }

    .wd-input__icon{
      background-color: transparent;
    }
  }
}

:deep(.label-class) {
  @apply !text-24rpx !text-#999;
}

:deep(.label-icon-class){
  @apply !text-38rpx !text-gray;
}
</style>
