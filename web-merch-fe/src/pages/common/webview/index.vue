<template>
  <view class="h-full">
    <web-view class="web-view" :src="url" />
  </view>
</template>

<script setup lang="ts">
import { decodeUrlParams } from '@/utils';

const url = ref<string>('');

onLoad((params: any) => {
  params = decodeUrlParams(params);
  if (params.url)
    url.value = params.url;

  if (params.title) {
    uni.setNavigationBarTitle({
      title: params.title,
    });
  }
});
</script>

<style lang="scss" scoped>
.web-view{
  height: 100%;

  table {
  max-width: 100vw !important;
}
}
</style>
