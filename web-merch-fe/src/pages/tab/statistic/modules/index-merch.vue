<template>
  <view class="h-full">
    <page-paging ref="pagingRef" refresher-only @on-refresh="onRefreshData">
      <view class="rounded-b-3xl from-#517cf0 to-#254bb2 bg-gradient-to-r text-white">
        <wd-navbar custom-style="background-color: transparent !important;" safe-area-inset-top :bordered="false">
          <template #title>
            <text class="text-white">
              交易总额
            </text>
          </template>
          <template #left>
            <wd-select-picker v-model="where.transType" title="支付类型" :columns="transTypeColumns" use-default-slot type="radio" @confirm="onChangeTransType">
              <wd-icon name="filter" size="20px" />
            </wd-select-picker>
          </template>
        </wd-navbar>

        <view class="flex justify-end">
          <view class="flex flex-col items-end px-20rpx">
            <text class="text-30rpx text-#edf0f3">
              当前总额
            </text>
            <wd-text
              :text="monthTransAmount"
              mode="price"
              prefix="￥"
              color="#fff"
            />
          </view>
        </view>

        <view class="mt-20rpx h-200px">
          <qiun-data-charts class="h-full" :animation="false" type="column" :chart-data="chartDataMonth" :opts="chartMonthOpts" :canvas2d="true" canvas-id="chart-month" @complete="complete" @get-index="onChartMonthActive" />
        </view>
      </view>

      <view class="bg-white p-20rpx">
        <view class="mb-3px">
          交易分析
        </view>
        <view class="h-200px">
          <qiun-data-charts
            type="area"
            :opts="chartDayOpts"
            :chart-data="chartDataDay"
            :animation="false"
            :canvas2d="true"
            canvas-id="chart-day"
            class="h-full"
          />
        </view>
      </view>
    </page-paging>
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { TransApi } from '@/api/trans';
import { CHANNEL_CODE } from '@/config/setting';
import uCharts from '@/uni_modules/qiun-data-charts/js_sdk/u-charts/config-ucharts.js';

defineOptions({
  options: {
    styleIsolation: 'shared', // 启用共享样式
  },
});

const pagingRef = ref();

const chartDataMonth = ref({});
const chartDataDay = ref({});

const where = reactive({
  transChn: null, // 通道编码
  queryDateType: null, // 机构查询日期类型 1:按月 2:按日
  transType: '', // 交易类型(1银联云闪付 2微信支付 3支付宝支付 4EPOS支付 ）
  queryMonthDate: dayjs().format('YYYY-MM'),
});

const monthTransAmount = ref('');

const transTypeColumns = ref<any>([
  {
    label: '全部',
    value: '',
  },
  {
    label: '银联云闪付',
    value: 1,
  },
  {
    label: '微信支付',
    value: 2,
  },
  {
    label: '支付宝支付',
    value: 3,
  },
  {
    label: 'EPOS支付',
    value: 4,
  },
]);

const chartMonthOpts = {
  fontSize: 12,
  dataLabel: false,
  padding: [15, 15, 10, 15],
  xAxis: {
    axisLineColor: '#fff',
    fontColor: '#fff',
  },
  legend: {
    show: false,
  },
  yAxis: {
    disableGrid: true,
    disabled: true,
  },
  extra: {
    tooltip: {
      showBox: false,
    },
  },
};

const chartDayOpts = {
  fontSize: 12,
  dataLabel: false,
  dataPointShape: false,
  padding: [15, 35, 0, 15],
  legend: {
    show: false,
  },
  xAxis: {
    disableGrid: true,
    labelCount: 2,
  },
  yAxis: {
    gridType: 'dash',
    dashLength: 2,
    min: 0,
  },
  extra: {
    area: {
      type: 'curve',
    },
    tooltip: {
      legendShow: false,
    },
  },
};

onLoad(() => {
  reload();
});

function onRefreshData() {
  Promise.allSettled([
    queryStatistics(1),
    queryStatistics(2),
  ]).then(() => {
    pagingRef.value?.complete();
  });
}

function onChangeTransType() {
  reload();
}

function onChartMonthActive(item: any) {
  const { opts, currentIndex } = item;

  if (currentIndex?.index !== -1) {
    where.queryMonthDate = opts.categories[currentIndex.index];
    queryStatistics(2);
  }
}

function reload() {
  queryStatistics(1);
  queryStatistics(2);
}

async function queryStatistics(queryDateType: 1 | 2) {
  const data = await TransApi.yearMonthCountList({ ...where, queryDateType });
  const { dayCounts, monthTransAmountTotal } = data || {};

  if (!dayCounts)
    return;

  const xAxis: any[] = [];
  const yAxis: any[] = [];

  dayCounts.forEach((item: any) => {
    xAxis.push(item.date);
    yAxis.push(item.dayTransAmountTotal);
  });

  // 按月
  if (queryDateType === 1) {
    const assemble = {
      categories: xAxis,
      series: [
        {
          data: yAxis,
          color: '#fff',
        },
      ],
    };
    chartDataMonth.value = assemble;
  }
  // 按日
  else if (queryDateType === 2) {
    const assemble = {
      categories: xAxis.reverse(),
      series: [
        {
          name: '交易量',
          data: yAxis.reverse(),
        },
      ],
    };
    chartDataDay.value = assemble;

    monthTransAmount.value = monthTransAmountTotal;
  }
}

function complete(e: any) {
  console.log('渲染完成事件', e);
  // uCharts.instance[e.id]代表当前的图表实例（除APP端，APP不可在组件外调用uCharts的实例）
  console.log('uCharts实例', uCharts.instance[e.id]);
  // uCharts.option[e.id]代表当前的图表的opts（除APP端，APP不可在组件外调用uCharts的实例）
  console.log('uCharts的option', uCharts.option[e.id]);
  // 下面展示渲染完成后，通过实例调用uCharts的showToolTip方法，有了uCharts实例，您也可以在其他地方调用图表的方法及数据（除APP端，APP因采用renderjs，无法获取uCharts实例）
  const categories = uCharts.option[e.id].categories;
  const series = uCharts.option[e.id].series;

  const index = categories.length - 1;
  // #ifndef APP-PLUS
  // 这里指定了changedTouches的x和y坐标，当指定index索引时，x值会被自动修正到正确位置，给0即可，主要是y的坐标值
  uCharts.instance[e.id].showToolTip(
    { changedTouches: [{ x: 0 }] },
    {
      index,
    },
  );
  // #endif
}
</script>

<style lang="scss" scoped>
.navbar-capsule{
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: $-navbar-capsule-width;
  height: $-navbar-capsule-height;
  box-sizing: border-box;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    border: 2rpx solid $-navbar-capsule-border-color;
    border-radius: calc($-navbar-capsule-border-radius * 2);
    content: '';
    transform: scale(0.5);
    transform-origin: 0 0;
    box-sizing: border-box;
  }

  &::after {
    position: absolute;
    top: 50%;
    left: 50%;
    display: block;
    width: 1px;
    height: 36rpx;
    background: $-navbar-capsule-border-color;
    content: '';
    transform: translateY(-50%);
  }

  &__icon{
    position: relative;
    font-size: 18px;
    color:#fff;
  }
}
</style>
