<template>
  <view class="h-full">
    <IndexMerch v-if="userType === 'merch'" />
    <IndexOrg v-else-if="userType === 'org'" />
  </view>
</template>

<script setup lang="ts">
import IndexMerch from './modules/index-merch.vue';
import IndexOrg from './modules/index-org.vue';
import type { UserState } from '@/store/modules/user/types';
import { useUserStore } from '@/store';

const userType: UserState['userType'] = useUserStore().userType;
</script>
