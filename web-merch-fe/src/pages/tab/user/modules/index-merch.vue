<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <!-- 头部 -->
    <view class="from-#517cf0 to-#254bb2 bg-gradient-to-r pb-70rpx text-white">
      <!-- 占位导航 -->
      <wd-navbar custom-style="background-color: transparent !important;" :bordered="false" safe-area-inset-top />

      <view class="relative flex flex-col items-center">
        <view class="i-mdi-account-circle size-180rpx rounded-50%" />
        <text class="mt-16rpx">
          {{ loginUser.legalName || loginUser.nickName }}
        </text>
        <view class="flex items-center">
          <text class="text-32rpx">
            {{ loginUser.orgCode }}
          </text>
          <i v-if="loginUser.orgCode" class="copy-icon" @click="copyData(loginUser.orgCode)" />
        </view>

        <!-- 通知小铃铛 -->
        <view class="absolute left-30rpx top--20rpx">
          <i class="i-mdi-bell-outline size-48rpx" @click="handleToNotice" />
        </view>
      </view>
    </view>

    <!-- 导航菜单 -->
    <view class="p-12px">
      <wd-cell-group border custom-class="cell-group-class">
        <wd-cell v-for="(item, key) in navigates" :key="key" :title="item.title" is-link size="large" @click="onNavigateActive(item)">
          <template #icon>
            <view class="flex items-center pr-10px">
              <i class="size-24px text-#4d80f0" :class="item.icon" />
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <view class="px-12px py-80rpx">
      <wd-button size="large" block @click="handleLoginOut">
        退出登录
      </wd-button>
    </view>

    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { isNumber } from 'wot-design-uni/components/common/util';
import { useUserStore } from '@/store';
import { MerchApi } from '@/api/merch';
import { useClipboard } from '@/hooks';

defineOptions({
  options: {
    styleIsolation: 'shared', // 启用共享样式
  },
});

const message = useMessage();

const loginUser = computed(() => useUserStore().info);

const merchStatus = ref<null | number>(null);

const navigates = [
  {
    title: '我的费率信息',
    icon: 'i-mdi-brightness-percent',
    to: '/pages/user/rate-info',
  },
  // {
  //   title: '退汇管理',
  //   icon: 'i-mdi-file-document-edit-outline',
  //   to: '/pages/user/refund-manage',
  // },
  {
    title: '资质信息更新',
    icon: 'i-mdi-file-document-edit-outline',
    to: '/pages/user/update-credentials/index',
  },
  // {
  //   title: '意愿确认订单',
  //   icon: 'i-mdi-file-document-edit-outline',
  //   to: '/pages/user/wish-confirm-order',
  // },
  {
    title: '联系客服',
    icon: 'i-mdi-headset',
    isLink: false,
    emitEvent: callCustomer,
  },
  {
    title: '设置',
    icon: 'i-mdi-cog-transfer-outline',
    to: '/pages/settings/index',
  },
];

onShow(() => {
  queryMerchStatus();
});

function onNavigateActive(item: any) {
  if (item.isLink !== false) {
    if (item.to === '/pages/user/update-credentials/index') {
      onBeforeLink({ to: item.to });
      return;
    }
    uni.navigateTo({ url: item.to });
    return;
  }

  if (item.emitEvent) {
    item.emitEvent();
  }
}

async function queryMerchStatus() {
  const data = await MerchApi.queryStatus();
  merchStatus.value = data?.authStatus || null;
}

function onBeforeLink({ to: url }: { to: string }) {
  if (!isNumber(merchStatus.value)) {
    message.alert({
      msg: '商户状态未知, 请稍后再试',
      title: '提示',
    });
    return;
  }

  switch (merchStatus.value) {
    case 0:
      message.alert({
        msg: '尚未完成实名认证, 点击按钮开始认证',
        title: '温馨提示',
      }).then(() => {
        uni.navigateTo({ url: '/pages/report/merch-auth/auth-micro-merch/index' });
      });
      break;
    case 1:
    case 3:
      message.alert({
        msg: '认证信息审核中, 请耐心等待',
        title: '温馨提示',
      });
      break;
    case 2:
      message.alert({
        msg: '认证信息审核未通过',
        title: '温馨提示',
        confirmButtonText: '查看详情',
      }).then(() => {
        uni.navigateTo({ url: '/pages/report/merch-auth/auth-result' });
      });
      break;
    default:
      uni.navigateTo({ url });
  }
}

function handleLoginOut() {
  message
    .confirm({
      msg: '确定要退出登录吗？',
      title: '提示',
    })
    .then(() => {
      useUserStore().logout();
    });
}

/** 联系客服 */
function callCustomer() {
  message.alert({
    title: '联系客服',
    msg: '025-58991933',
  });
}

function handleToNotice() {
  uni.navigateTo({ url: '/pages/common/notice/index' });
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group-class){
  box-shadow: rgb(99 99 99 / 20%) 0 2px 6px 0;

  @apply rounded-10px;

 .wd-cell-group__body, .wd-cell{
    @apply !bg-transparent;
  }
}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-white;
}
</style>
