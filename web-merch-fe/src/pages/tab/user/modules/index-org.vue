<template>
  <view class="h-full overflow-y-scroll">
    <!-- 头部 -->
    <view class="from-#517cf0 to-#254bb2 bg-gradient-to-r pb-70rpx text-white">
      <!-- 占位导航 -->
      <wd-navbar custom-style="background-color: transparent !important;" :bordered="false" safe-area-inset-top />

      <view class="relative flex flex-col items-center">
        <view class="i-mdi-account-circle size-180rpx rounded-50%" />
        <text class="mt-16rpx">
          {{ loginUser.companyName || loginUser.legalName }}
        </text>
        <view class="flex items-center">
          <text class="text-32rpx">
            {{ loginUser.orgCode }}
          </text>
          <i v-if="loginUser.orgCode" class="copy-icon" @click="copyData(loginUser.orgCode)" />
        </view>

        <!-- 通知小铃铛 -->
        <view class="absolute left-30rpx top--20rpx">
          <i class="i-mdi-bell-outline size-48rpx" @click="handleToNotice" />
        </view>
      </view>
    </view>

    <!-- 导航菜单 -->
    <view class="p-12px">
      <wd-cell-group border custom-class="cell-group-class">
        <wd-cell v-for="(item, key) in navigates" :key="key" :title="item.title" is-link size="large" @click="onNavigateActive(item)">
          <template #icon>
            <view class="flex items-center pr-10px">
              <i class="size-22px text-#4d80f0" :class="item.icon" />
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <view class="px-12px py-80rpx">
      <wd-button size="large" block @click="handleLoginOut">
        退出登录
      </wd-button>
    </view>

    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { useUserStore } from '@/store';
import { useClipboard } from '@/hooks';

defineOptions({
  options: {
    styleIsolation: 'shared', // 启用共享样式
  },
});

const message = useMessage();

const loginUser = computed(() => useUserStore().info);

const navigates = [
  {
    title: '我的政策信息',
    icon: 'i-mdi-brightness-percent',
    to: '/pages-org/user/rate-policy/index',
  },
  {
    title: '我的奖励政策',
    icon: 'i-mdi-brightness-percent',
    to: '/pages-org/user/reward-policy/index',
  },
  {
    title: '结算卡管理',
    icon: 'i-mdi-credit-card-outline',
    to: '/pages-org/settle-card/index',
  },
  {
    title: '修改登录密码',
    icon: 'i-mdi-lock-outline',
    to: '/pages-org/settings/update-login-pwd',
  },
  {
    title: '更换手机号',
    icon: 'i-mdi-phone',
    to: '/pages-org/settings/update-phone',
  },
  {
    title: '关于',
    icon: 'i-mdi-message-badge-outline',
    to: '/pages-org/settings/about',
  },
];

onShow(() => {

});

function onNavigateActive(item: any) {
  uni.navigateTo({ url: item.to });
}

function handleLoginOut() {
  message
    .confirm({
      msg: '确定要退出登录吗？',
      title: '提示',
    })
    .then(() => {
      useUserStore().logout();
    });
}

function handleToNotice() {
  uni.navigateTo({ url: '/pages-org/notice/index' });
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group-class){
  box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;

  @apply rounded-xl;

 .wd-cell-group__body, .wd-cell{
    @apply !bg-transparent;
  }
}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-white;
}
</style>
