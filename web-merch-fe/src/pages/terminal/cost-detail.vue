<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-lg bg-white p-20rpx"
        >
          <wd-cell-group custom-class="cell-group">
            <wd-cell title="订单类型" :value=" orderTypeMap[item.orderType] || '--'" />
            <wd-cell v-if="item.orderType === 2" title="扣费期数" :value="`${item.cycleDay}期`" />
            <wd-cell title="订单金额" :value="item.originPayAmt" />
            <wd-cell title="支付状态" :value="payStatusMap[item.payStatus] || '--'" />
            <wd-cell title="扣费发起时间" :value="item.validBeginTime || '--'" />
            <wd-cell title="支付完成时间" :value="item.payTime || '--'" />
          </wd-cell-group>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { TerminalApi } from '@/api/terminal';

const pagingRef = ref();

const datasource = ref<any[]>([]);

const where = reactive({
  terminalSn: '',
});

const orderTypeMap: Record<number, string> = {
  1: '服务费',
  2: '通讯费',
};

const payStatusMap: Record<number, string> = {
  1: '未支付',
  2: '已支付',
  3: '支付失败',
};

onLoad((query) => {
  where.terminalSn = query?.terminalSn || '';
});

function queryDataSource() {
  TerminalApi.queryTerminalActivePayOrderListBySn(where)
    .then((data) => {
      pagingRef.value.complete(data);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
