<template>
  <view class="h-full">
    <web-view :src="url" />
  </view>
</template>

<script setup lang="ts">
import { SELF_WEBVIEW_URL } from '@/config/setting';
import { buildUrlWithParams, getToken } from '@/utils';

const url = ref<string>('');

onLoad((options) => {
  url.value = buildUrlWithParams(`${SELF_WEBVIEW_URL}/#/pages/user/update-credentials/index`, {
    ...options,
    client_type: 'applet',
    token: getToken(),
  });
});
</script>
