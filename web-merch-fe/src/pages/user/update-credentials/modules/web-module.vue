<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view v-if="!isEmbeApplet">
      <wd-navbar title="法人证件信息更新" safe-area-inset-top left-arrow placeholder fixed @click-left="handleClickLeft" />
    </view>
    <view class="mt-10px bg-white py-12px">
      <view class="mb-12px px-12px font-bold">
        人脸照
      </view>
      <view v-for="(item, key) in [fileMap[5]]" :key="key" class="w-50%">
        <GivenUpload
          v-model:file-data="item.fileData"
          :file-name="item.fileName"
          :placeholder="item.placeholder"
          custom-class="w-full"
        />
      </view>
    </view>

    <view class="mt-10px bg-white py-12px">
      <view class="mb-12px px-12px font-bold">
        身份证信息
      </view>
      <view class="flex">
        <view v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" class="w-50%">
          <GivenUpload
            v-model:file-data="item.fileData"
            :file-name="item.fileName"
            :placeholder="item.placeholder"
            custom-class="w-full"
            @choose="(file) => ocrIdcardByFile(file, item)"
          />
        </view>
      </view>
    </view>

    <wd-form ref="formRef" :model="form" :rules="rules">
      <wd-cell-group border>
        <wd-input
          v-model="form.legalName" prop="legalName"
          label="姓名" placeholder="自动识别" label-width="100px"
          align-right
        />
        <wd-input
          v-model="form.legalCertNo" prop="legalCertNo"
          label="身份证号" placeholder="自动识别" label-width="100px"
          align-right
        />
        <wd-textarea
          v-model="form.legalAddr" prop="legalAddr"
          label="居住地址" placeholder="请输入住所或工作单位地址" label-width="100px"
          auto-height
          custom-textarea-class="text-right"
        />
        <wd-datetime-picker
          v-model="licenseDateRegion" prop="legalCertEndDate"
          label="证件有效期" label-width="100px"
          type="date" :min-date="minDate" :max-date="maxDate" align-right
          :default-value="defaultDateRegionValue"
          @confirm="onConfirmLicenseDateRegion"
        />
      </wd-cell-group>
      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import jsweixin from 'weixin-js-sdk';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common';
import { MerchApi } from '@/api/merch';
import { CHANNEL_CODE } from '@/config/setting';
import { setToken } from '@/utils';

const toast = useToast();

const licenseDateRegion = ref<any>([]);
const defaultDateRegionValue = ref([dayjs().valueOf(), dayjs().valueOf()]);

const minDate = dayjs('1900-01-01').valueOf();
const maxDate = dayjs('2099-12-31').valueOf();

const isEmbeApplet = ref(false);

// 文件
type FileType = 1 | 2 | 5 ;
const fileMap = ref <Record<FileType, GivenUploadProps>> ({
  1: {
    fileName: '人像面',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 1,
  },
  2: {
    fileName: '国徽面',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 2,
  },
  5: {
    fileName: '人脸照',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 5,
    required: false,
  },
});

// 表单
const form = reactive({
  // 证件信息
  legalCertNo: '', // 证件号码 必填，用来与之前的比对 是否一致
  legalName: '', // 姓名 必填
  legalAddr: '', // 地址 必填
  legalCertStartDate: '', // 证件有效期（开始：yyyy-MM-dd）必填
  legalCertEndDate: '', // 证件有效期（结束：yyyy-MM-dd） 必填

  // 原图片对象信息  -- 6.4、商户信息查询 筛选出 1-头像面 2-国徽面 5-人脸照
  originImageList: [],
  // 新图片对象信息
  newImageList: [],
});

// 校验规则
const rules: FormRules = {
  legalCertNo: [{ required: true, message: '请填写证件号码' }],
  legalName: [{ required: true, message: '请填写姓名' }],
  legalAddr: [{ required: true, message: '请填写居住地址' }],
  legalCertEndDate: [{ required: true, message: '请选择证件有效期' }],
};

// 表单ref
const formRef = ref<FormInstance | null>(null);

onLoad((options) => {
  if (options?.client_type === 'applet') {
    isEmbeApplet.value = true;
  }

  if (options?.token) {
    setToken(options.token);
  }
});

onMounted(() => {
  queryOriginImageList();
});

function handleClickLeft() {
  uni.navigateBack();
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageList = await uploadFile();
  form.newImageList = imageList as any;

  const data = await MerchApi.updateLegalCredentials(form);
  toast.success({
    msg: data?.message,
    closed: () => {
      if (isEmbeApplet.value) {
        jsweixin.miniProgram.navigateBack();
      }
      else {
        uni.navigateBack();
      }
    },
  });
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileList = Object.values(fileMap.value);
  const imageList = fileList.map((item) => {
    return {
      fileData: item.fileData,
      fileType: item.fileType,
      suffixType: 'png',
    };
  });
  const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList })
    .catch(() => Promise.reject(new Error('upload fail!')));

  return data?.imageJsonList || [];
}

/*
 * 校验文件
 */
async function checkFile() {
  const fileList = Object.values(fileMap.value);
  const isCheckPass = fileList.every((f) => {
    if (f.required === false)
      return true;
    if (!f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

async function ocrIdcardByFile(file: string, item: any) {
  const { fileType } = item;
  const side = fileType === 1 ? 'face' : 'back';
  const data = await CommonApi.ocrIdCard({ imgFile: file, side });

  if (!data?.success)
    return;

  switch (side) {
    case 'face':
      form.legalName = data.name;
      form.legalCertNo = data.num;
      form.legalAddr = data.address;
      break;
    case 'back':
      // eslint-disable-next-line no-case-declarations
      let { startDate, endDate } = data;
      if (startDate && endDate) {
        if (endDate === '长期') {
          endDate = '2099-12-31';
        }
        licenseDateRegion.value = [dayjs(startDate).valueOf(), dayjs(endDate).valueOf()];
        onConfirmLicenseDateRegion();
      }
      break;

    default:
      break;
  }
}

async function queryOriginImageList() {
  const data = await MerchApi.queryMerchInfo();
  const { imageList } = data || {};

  if (!imageList)
    return;

  const filterImageList = imageList.filter((item: any) => [1, 2, 5].includes(item.imageType));
  const imageListMap = filterImageList.map((item: any) => {
    return {
      imageType: item.imageType, // 文件类型
      id: item.id, // 图片记录ID
    };
  });
  form.originImageList = imageListMap;
}

function onConfirmLicenseDateRegion() {
  const [startDate, endDate] = licenseDateRegion.value;
  form.legalCertStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.legalCertEndDate = dayjs(endDate).format('YYYY-MM-DD');
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-input__error-message,
  .wd-cell__error-message,
  .wd-textarea__error-message,
  .wd-picker__error-message
  {
    text-align: right !important;
  }
}
</style>
