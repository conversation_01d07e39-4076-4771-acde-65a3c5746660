<template>
  <view>
    <page-paging ref="pagingRef" v-model="dataSource" @query="queryDataSource">
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in dataSource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text class="text-28rpx font-medium">
              {{ channelMap[item.channelCode] || '--' }}-{{ item.chnMerchNo }}-{{ item.chnMerchName }}
            </text>
          </view>
          <view class="py-20rpx">
            <RateModule
              :rate-item="item"
              :readonly="true"
              :show-rate-type-title="false"
            />
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { CommonApi } from '@/api/common';
import { MerchReportApi } from '@/api/report';

const pagingRef = ref();

const where = reactive({
});

const channelMap: any = {};

const dataSource = ref<any[]>([]);

onLoad(() => {
  getChannelList();
});

function queryDataSource(pageNo: number, pageSize: number) {
  MerchReportApi.queryChlMerchRateInfoPage({ ...where, pageNo, pageSize })
    .then((res) => {
      res?.rows.forEach((item: any) => {
        item.rateInfoDTO = item.rateInfoDTO || {};
      });

      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

async function getChannelList() {
  let data = await CommonApi.getChannelList();
  data = data || [];
  data.forEach((item: any) => {
    channelMap[item.channelCode] = item.channelName;
  });
}
</script>

<style lang="scss" scoped>
.custom-cell{
    @apply flex items-center justify-between mb-20rpx;

    view{
      @apply w-110rpx break-all rounded-md bg-primary p-10rpx text-center text-30rpx;
    }
  }
</style>
