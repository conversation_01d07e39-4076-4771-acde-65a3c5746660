<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 分割块 -->
      <template #top>
        <view class="flex bg-primary p-20rpx">
          <view>
            <custom-datetime-picker v-model="whereDate" type="date" ::default-value="whereDate" @confirm="handleConfirmDate" />
          </view>
        </view>
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text class="font-medium">
              {{ item.reexchangeFlowNo }}
            </text>
          </view>

          <view class="p-16rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="处理状态">
                <wd-text type="primary" :text="handleStatusMap[item.handleStatus]" />
              </wd-cell>
              <wd-cell title="通道商户编号" :value="item.chnMerchNo" />
              <wd-cell title="通道商户名称" :value="item.chnMerchName" />
              <wd-cell title="银行户名" :value="item.bankAccountName" />
              <wd-cell title="银行名称" :value="item.bankName" />
              <wd-cell title="银行账号" :value="item.bankAccountNoMask" />
              <wd-cell title="退汇原因" :value="item.reexchangeReason" />
              <wd-cell title="退汇时间" :value="item.reexchangeTime" />
            </wd-cell-group>
          </view>

          <!-- <view v-if="item.handleStatus === 1" class="flex justify-end border border-#f4f4f4 border-t-solid p-20rpx">
            <wd-tag type="primary" custom-class="!text-24rpx !px-20rpx !py-8rpx">
              去处理
            </wd-tag>
          </view> -->
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { CHANNEL_CODE } from '@/config/setting';
import { TransApi } from '@/api/trans';

// 分页ref
const pagingRef = ref();

// 查询时间范围 默认1个月
const whereStartDateDef = dayjs().subtract(1, 'month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive({
  channelCode: CHANNEL_CODE,
  searchBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间
  searchEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间
});

const handleStatusMap: Record<string, string> = {
  1: '待处理',
  2: '已处理',
};

const datasource = ref<any[]>([]);

function handleConfirmDate({ value }: { value: string[] }) {
  [where.searchBeginTime, where.searchEndTime] = value;
  pagingRef.value.reload();
}

function queryDataSource(pageNo: number, pageSize: number) {
  TransApi.queryReexchangeList({ ...where, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
