import { getToken } from '@/utils/auth';

// 登录页面
const loginPage = '/pages/common/login/index';
// 页面白名单
const whiteList = [
  '/',
  '/pages/advance/index',
  '/pages/common/webview/index',
  '/pages/common/login/index',
  '/pages/common/register/index',
  '/pages/settings/reset-login-pwd',
  '/pages-org/settings/reset-login-pwd',
];

// 检查地址白名单
function checkWhite(url: string) {
  const path = url.split('?')[0];
  return whiteList.includes(path);
}

// 页面跳转验证拦截器
const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab'];
list.forEach((item) => {
  uni.addInterceptor(item, {
    invoke(to) {
      if (getToken()) {
        if (to.url === loginPage)
          uni.reLaunch({ url: '/' });

        return true;
      }
      else {
        if (checkWhite(to.url))
          return true;
      }
    },
    fail(err) {
      console.log(err);
    },
  });
});
