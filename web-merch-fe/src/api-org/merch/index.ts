import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 商户管理API
 */
export class MerchApi {
  /**
   * 查询商户总数
   */
  static queryMerchantCount = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/merchantCount', data, custom: {
    loading: false,
  } });

  /**
   * 商户列表查询
   */
  static findPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/pageMerchant', data, custom: {
    loading: false,
  } });

  /**
   * 查询商户基础信息
   */
  static queryMerchantBaseInfo = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/orgUserBaseInf', data, custom: {
    loading: false,
  } });

  /**
   * 查询商户报备信息列表
   */
  static queryMerchantReportInfoList = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/orgUserReportInfList', data, custom: {
    loading: false,
  } });

  /**
   * 查询商户终端信息列表
   */
  static queryMerchantTerminalInfoList = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/orgUserReportTermList', data, custom: {
    loading: false,
  } });

  /**
   * 根据终端SN查询终端基本信息
   */
  static getTerminalBaseInfoBySn = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/terminal/getTerminalBaseInfoBySn', data, custom: {
    loading: false,
  } });

  /**
   * 根据商户号查询商户交易信息
   */
  static queryTotalTransInfByMerchNo = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryTotalTransInfByMerchNo', data, custom: {
    loading: false,
  } });

  /**
   * 自定义查询商户交易信息
   */
  static queryTotalTransInfByMutiCondition = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryTotalTransInfByMutiCondition', data, custom: {
    loading: false,
  } });
}
