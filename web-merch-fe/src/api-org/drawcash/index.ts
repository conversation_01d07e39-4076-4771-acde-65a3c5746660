import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 机构提现API
 */
export class DrawcashApi {
  /**
   * 获取出款通道接口
   */
  static getWithdrawRemitChannel = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/getWithdrawRemitChannel', data });

  /**
   * 计算提现手续费接口
   */
  static getWithdrawTaxInfo = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/getWithdrawTaxInfo', data });

  /**
   * 提现
   */
  static walletWithdraw = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/walletWithdraw', data });

  /**
   * 查询钉灵工签约三要素（反显）
   */
  static queryOrgAuthInfo = () => post<CommonResult>({ url: '/app/dlg/queryOrgAuthInfo' });

  /**
   * 钉灵工签约
   */
  static signDlg = (data: CommonParams) => post<CommonResult>({ url: '/app/dlg/sign', data });

  /**
   * 提现记录统计
   */
  static withdrawListTotal = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/withdrawListTotal', data });

  /**
   * 提现记录
   */
  static withdrawListPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/withdrawListPage', data });
}
