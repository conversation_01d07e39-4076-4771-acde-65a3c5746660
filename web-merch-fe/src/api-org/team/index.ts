import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 团队API
 */
export class TeamApi {
  /**
   * 团队汇总
   */
  static queryOrgSummary = () => post<CommonResult>({ url: '/app/orgUser/summary', custom: {
    loading: false,
  } });

  /**
   * 机构-运营中心列表
   */
  static findOrgBranchPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/orgBranchPage', data, custom: {
    loading: false,
  } });

  /**
   * 机构-运营中心详情
   */
  static queryBranchDetail = (data: CommonParams) => post<CommonResult>({ url: '/app/branchDetail', data, custom: {
    loading: false,
  } });

  /**
   * 机构-代理列表
   */
  static findOrgAgentPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/orgAgentPage', data, custom: {
    loading: false,
  } });

  /**
   * 机构-代理详情
   */
  static queryAgentDetail = (data: CommonParams) => post<CommonResult>({ url: '/app/agentDetail', data, custom: {
    loading: false,
  } });

  /**
   * 机构-添加运营中心
   */
  static addBranch = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/addBranch', data });

  /**
   * 机构-添加代理商
   */
  static addAgent = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/addAgent', data });

  /**
   * 驳回编辑查询详情（新增一级代理）
   */
  static queryRejectEditDetail = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/rejectEditDetail', data });

  /**
   * 驳回（新增一级代理）
   */
  static rejectEditAgent = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/rejectEditAgent', data });

  /**
   * 团队详情/总交易量
   */
  static queryTotalTransVolume = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/teamDetail/totalTransVolume', data, custom: {
    loading: false,
  } });

  /**
   * 团队详情/查询月交易汇总
   */
  static queryMonthTransVolume = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/teamDetail/monthTransVolume', data, custom: {
    loading: false,
  } });
}
