import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 返现政策API
 */
export class CashbackPolicyApi {
  /**
   * 激活/返现政策分页列表(仅查自身)
   */
  static findSelfPage = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/selfPage', data });

  /**
   * 根据政策编号获取激活/返现政策列表
   */
  static findListByPolicyNo = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/list', data });

  /**
   * 获取政策名称列表
   */
  static findPolicyNameList = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/policyNameList', data, custom: {
    loading: false,
  } });

  /**
   * 获取直属下级的开通情况
   */
  static getDirectAgentOpenSt = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/getDirectAgentOpenSt', data, custom: {
    loading: false,
  } });

  /** 激活/返现政策分页列表(默认查询下级) */
  static cashbackRulePolicyPage = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/page', data, custom: {
    loading: false,
  } });

  /** 直属子代激活/返现政策编辑 */
  static subAgentEditCashbackRulePolicy = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/subAgentEdit', data });

  /**
   * 直属子代政策开通/补开通
   */
  static subAgentOpen = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackRulePolicy/subAgentOpen', data, custom: {
    loading: false,
  } });
}
