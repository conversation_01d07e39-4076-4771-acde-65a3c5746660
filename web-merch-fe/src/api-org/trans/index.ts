import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 交易API
 */
export class TransApi {
  /**
   * 机构-首页汇总（分润记账余额、营销记账余额、奖励记账余额、充值代付余额)
   */
  static profitRewardBalance = () => post<CommonResult>({ url: '/app/orgUser/profitRewardBalance', custom: {
    loading: false,
  } });

  /**
   * 机构-首页汇总（今日及本月的交易总额、收益总额、新增商户数）
   */
  static selectTransProfitCount = () => post<CommonResult>({ url: '/app/orgUser/selectTransProfitCount', custom: {
    loading: false,
  } });

  /**
   * 机构-数据统计-近6个月的交易金额统计
   */
  static monthCountTransList = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/monthCountTransList', data, custom: {
    loading: false,
  } });

  /**
   * 机构-交易汇总管理-按月每日汇总列表
   */
  static monthDayCountTransList = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/monthDayCountTransList', data, custom: {
    loading: false,
  } });

  /**
   * 交易汇总-查询交易汇总列表
   */
  static queryTransSumInfoByPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryTransSumInfoByPage', data, custom: {
    loading: false,
  } });

  /**
   * 交易汇总-查询交易明细列表
   */
  static queryTransDetailInfoByPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryTransDetailInfoByPage', data, custom: {
    loading: false,
  } });

  /**
   * 交易汇总-查询交易明细详情
   */
  static queryTransDetailInfById = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryTransDetailInfById', data, custom: {
    loading: false,
  } });
}
