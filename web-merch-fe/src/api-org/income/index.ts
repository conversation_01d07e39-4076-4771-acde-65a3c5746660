import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 收益管理API
 */
export class IncomeApi {
  /**
   * 收益分润-查询分润汇总列表
   */
  static queryProfitSumInfoByPage = (data: CommonParams) => post({ url: '/app/orgUser/queryProfitSumInfoByPage', data, custom: {
    loading: false,
  } });

  /**
   * 查询分润明细列表
   */
  static queryProfitDetailInfoByPage = (data: CommonParams) => post({ url: '/app/orgUser/queryProfitDetailInfoByPage', data, custom: {
    loading: false,
  } });

  /**
   * 查询分润详情
   */
  static queryProfitDetailInfById = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryProfitDetailInfById', data });

  /**
   * 查询返现汇总列表
   */
  static queryCashbackSumInfoByPage = (data: CommonParams) => post({ url: '/app/orgUser/queryCashbackSumInfoByPage', data, custom: {
    loading: false,
  } });

  /**
   * 查询返现明细列表
   */
  static queryCashbackDetailListInfoByPage = (data: CommonParams) => post({ url: '/app/orgUser/queryCashbackDetailListInfoByPage', data, custom: {
    loading: false,
  } });

  /**
   * 查询返现详情
   */
  static queryCashbackDetailInfById = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryCashbackDetailInfById', data });

  /**
   * 新增
   */
  static add = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/add', data });

  /**
   * 编辑
   */
  static edit = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/edit', data });

  /**
   * 删除
   */
  static delete = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/delete', data });

  /**
   * 生成链接
   */
  static createQrcode = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/createQrcode', data });
}
