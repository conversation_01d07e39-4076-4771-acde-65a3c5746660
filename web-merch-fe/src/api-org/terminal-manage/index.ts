import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 终端管理API
 */
export class TerminalManageApi {
  /** 机具数据汇总 */
  static terminalSummary = () => post<CommonResult>({ url: '/app/orgUser/terminalSummary', custom: {
    loading: false,
  } });

  /**
   * 终端列表
   */
  static terminalInfoPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/terminalInfoPage', data, custom: {
    loading: false,
  } });

  /**
   * 机具管理-查询终端型号
   */
  static terminalModelList = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/terminal/terminalModelList', data, custom: {
    loading: false,
  } });

  /**
   * 代理商已开通的流量费政策列表
   */
  static selfOpenList = () => post<CommonResult>({ url: '/app/orgUser/simFeePolicy/selfOpenList', custom: {
    loading: false,
  } });

  /**
   * 终端撤机
   */
  static abortTermSn = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/terminal/abortTermSn', data });

  /**
   * 终端换绑
   */
  static changeBindTermSn = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/terminal/changeBindTermSn', data });

  /** 机具管理-根据sn查看权益订单获取列表（带分页） */
  static terminalActivePayOrderPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/terminalActivePayOrderPage', data, custom: {
    loading: false,
  } });

  /** 代理商已开通的服务费政策 */
  static serviceFeePolicySelfOpenList = () => post<CommonResult>({ url: '/app/orgUser/serviceFeePolicy/selfOpenList', custom: {
    loading: false,
  } });

  /** 代理商已开通的流量费政策列表 */
  static simFeePolicySelfOpenList = () => post<CommonResult>({ url: '/app/orgUser/simFeePolicy/selfOpenList', custom: {
    loading: false,
  } });

  /** 机具修改营销活动 */
  static modifyActivity = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/modifyServiceFee', data });

  /** 机具管理-修改终端费率-代理个人费率/系统阀值信息 */
  static ownerRateList = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/rate/ownerRateList', data });

  /** 机具终端单个回收 */
  static singleRecycleTerminal = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/singleRecycleTerminal', data });

  /** 机具管理-修改单个终端费率信息 */
  static modifyRateInfo = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/terminal/modifyRateInfo', data });

  /** 机具根据号段查询可回拨终端 */
  static callbackTerminalList = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/callbackTerminalList', data });

  /** 机具号段回拨 */
  static callbackTerminalBySn = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/callbackTerminalBySn', data });

  /** 机具管理-查询直属下级代理商 */
  static findAgentByParentNo = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/findAgentByParentNo', data });

  /** 机具划拨/回拨批次分页查询 */
  static terminalTransferBatchPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/terminalTransferBatchPage', data, custom: {
    loading: false,
  } });

  /** 机具批次回拨 */
  static callbackTermByBatchNo = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/callbackTermByBatchNo', data });

  /** 机具划拨详情 */
  static terminalTransferPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/terminalTransferPage', data, custom: {
    loading: false,
  } });

  /** 机具管理-查询当前登录用户的闲置终端列表（分页） */
  static selefIdleTerminal = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/selefIdleTerminal', data, custom: {
    loading: false,
  } });

  /** 机具根据号段查询自身终端（下发） */
  static queryTerminalList = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/queryTerminalList', data });

  /** 机具管理-终端下发 */
  static transferTerminal = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/transferTerminal', data });

  /** 获取自身返现政策信息(终端入库-下拉框) */
  static fetchOwnerCashPolicyInfo = () => post<CommonResult>({ url: '/orgapp/terminalActiveInfo/fetchOwnerCashPolicyInfo', custom: {
    loading: false,
  } });

  /** 终端下发前置校验 */
  static fetchTerminalActiveScreenCashRules = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/terminalActiveInfo/fetchTerminalActiveScreenCashRules', data });

  /** 机具管理-自身+所属一级是自己的闲置终端列表（分页） */
  static selefIdleAndOneAgentTerminal = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/selefIdleAndOneAgentTerminal', data, custom: {
    loading: false,
  } });

  /** 机具管理-修改n个终端政策及费率信息 */
  static modifyPolicyRateInfo = (data: CommonParams) => post<CommonResult>({ url: '/orgUserApp/terminal/modifyPolicyRateInfo', data });

  /** 机具修改营销活动 */
  static modifyServiceFee = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/modifyServiceFee', data });
}
