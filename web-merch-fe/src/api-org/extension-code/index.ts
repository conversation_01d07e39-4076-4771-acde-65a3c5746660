import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 机构拓展码API
 */
export class ExtensionCodeApi {
  /**
   * 分页
   */
  static findPage = (data: CommonParams) => post({ url: '/orgapp/agentExtensionCode/page', data });

  /**
   * 详情
   */
  static detail = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/detail', data });

  /**
   * 新增
   */
  static add = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/add', data });

  /**
   * 编辑
   */
  static edit = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/edit', data });

  /**
   * 删除
   */
  static delete = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/delete', data });

  /**
   * 生成链接
   */
  static createQrcode = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/agentExtensionCode/createQrcode', data });

  /**
   * 生成链接
   */
  static createMerchRegQrcode = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/extensionCode/createMerchRegQrcode', data });
}
