import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 结算卡API
 */
export class SettleCardApi {
  /**
   * 添加结算卡
   */
  static add = (data: CommonParams) => post<CommonResult>({ url: '/app/addBankCard', data });

  /**
   * 编辑结算卡
   */
  static edit = (data: CommonParams) => post<CommonResult>({ url: '/app/editBankCard', data });

  /**
   * 详情
   */
  static detail = (data: CommonParams) => post<CommonResult>({ url: '/app/bankCard/detail', data });

  /**
   * 结算卡列表
   */
  static findPage = () => post<Record<string, any>[]>({ url: '/app/bankCardFindPage' });
}
