import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 用户API
 */
export class UserApi {
  /**
   * 登录
   */
  static login = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/login', data, custom: {
    auth: false,
  } });

  /**
   * 找回登录密码
   */
  static resetPassword = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/resetPassword', data, custom: {
    auth: false,
  } });

  /**
   * 修改登录密码
   */
  static updatePassword = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/updatePassword', data });

  /**
   * 更换手机号
   */
  static changeTelephone = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/changeTelephone', data });

  /**
   * 退出登录
   */
  static loginout = () => post<CommonResult>({ url: '/app/orgUser/loginOut', custom: {
    loading: false,
  } });

  /**
   * 机构信息详情
   */
  static getOrgUserInfo = () => post({ url: '/app/orgUser/getOrgUserInfo', custom: {
    loading: false,
  } });
}
