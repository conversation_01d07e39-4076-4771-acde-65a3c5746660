import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 费率政策API
 */
export class RatePolicyApi {
  /**
   * 分页查询费率政策
   */
  static findPolicyPage = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/findPolicyPage', data, custom: {
    loading: false,
  } });

  /**
   * 费率政策详情
   */
  static detailPolicy = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/ratePolicy/querySelfDetail', data });

  /**
   * 费率政策详情
   */
  static detailItemPolicy = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/ratePolicy/detail', data });

  /**
   *  获取个人费率信息
   */
  static getOwnerRateList = () => post<CommonResult>({ url: '/app/rate/ownerRateList' });

  /**
   * 新增费率政策
   */
  static addPolicy = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/addPolicy', data });

  /**
   * 编辑费率政策
   */
  static editPolicy = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/editPolicy', data });

  /**
   * 获取同步下级用户列表
   */
  static getSubOrgList = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/getSubOrgList', data });

  /**
   * 同步费率政策
   */
  static syncPolicy = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/syncPolicy', data });

  /**
   * 生成拓展码
   */
  static getExtendCodeLink = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/getExtendCodeLink', data });

  /**
   * 获取指定机构的商户费率政策列表
   */
  static getRatePolicylistOfOrg = (data: CommonParams) => post<CommonResult>({ url: '/app/ratePolicy/listOfOrg', data });

  /**
   * 新增机构反显活动模板列表
   */
  static querySelfCashbackTemplate = () => post<CommonResult>({ url: '/orgapp/cashbackTemplate/querySelfList' });

  /**
   * 新增模板反显列表
   */
  static queryAddDisplay = () => post<CommonResult>({ url: '/orgapp/cashbackTemplate/queryAddDisplay', custom: {
    loading: false,
  } });

  /**
   * 活动政策/详情
   */
  static queryCashbackPolicyDetail = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackPolicy/detail', data });

  /**
   * 活动政策/编辑
   */
  static editCashbackPolicy = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/cashbackPolicy/edit', data });

  /**
   * 活动返现模板/添加
   */
  static addActivityCashbackTemplate = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/activityCashbackTemplate/add', data });

  /**
   * 活动返现模板/分页
   */
  static findActivityCashbackTemplatePage = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/activityCashbackTemplate/page', data, custom: {
    loading: false,
  } });

  /**
   * 活动返现模板/详情
   */
  static detailActivityCashbackTemplate = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/activityCashbackTemplate/detail', data });

  /**
   * 活动返现模板/编辑
   */
  static editActivityCashbackTemplate = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/activityCashbackTemplate/edit', data });

  /**
   * 活动返现政策详情
   */
  static selfCashbackPolicyDetail = () => post<CommonResult>({ url: '/orgapp/cashbackPolicy/querySelfDetail' });

  /**
   * 同步商户费率
   */
  static syncMerchantRatePolicy = (data: CommonParams) => post<CommonResult>({ url: '/app/rate/syncMerchantRatePolicy', data });

  /**
   *  获取个人费率政策列表
   */
  static listOwnerPolicy = () => post<CommonResult>({ url: '/app/rate/listOwnerPolicy', custom: {
    loading: false,
  } });
}
