import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 商户报备API
 */
export class MerchReportApi {
  /**
   * 商户列表查询
   */
  static findPage = (data: CommonParams) => post<CommonResult>({ url: '/app/orgUser/pageMerchantSignOrder', data, custom: {
    loading: false,
  } });

  /**
   * 通道商户费率信息查询
   */
  static queryChlMerchRateInf = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/chlMerch/queryChlMerchRateInf', data });

  /**
   * 通道商户费率编辑
   */
  static chlMerchRateInfUpdate = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/chlMerch/chlMerchRateInfUpdate', data });
}
