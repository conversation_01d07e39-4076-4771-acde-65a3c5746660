:root,
page {
  height: 100%;
	font-size: 30rpx;
  background-color: #fff;
}

view {
  box-sizing: border-box;
  touch-action:pan-y;
}

// cell textarea 处理
:deep(.wd-cell){
  .wd-textarea {
    padding: 0 !important;
    background: transparent !important;

    .wd-textarea__value{
      background:none;
    }
  }
}

// 表单错误提示靠右展示
:deep(.error-message__align-right){
  .wd-input__error-message,
  .wd-cell__error-message,
  .wd-textarea__error-message,
  .wd-picker__error-message
  {
    text-align: right !important;
  }
}
