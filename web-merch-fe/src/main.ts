import { createSSRApp } from 'vue';

// 引入UnoCSS
import 'virtual:uno.css';

import App from '@/App.vue';

// 引入状态管理
import setupStore from '@/store';

// 引入请求封装
import setupRequest from '@/utils/request';

// 引入小程序分享
import setupShare from '@/utils/share';

// 权限管理
import '@/permission';

export function createApp() {
  const app = createSSRApp(App);
  // 状态管理
  setupStore(app);
  // 网络请求
  setupRequest();

  // #ifdef MP-WEIXIN
  setupShare(app);
  // #endif

  return {
    app,
  };
}
