import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 门店认证API
 */
export class StoreCertificationApi {
  /**
   * 多门店随机生成
   */
  static findStoreNameAndMccList = () => post<CommonResult>({ url: '/app/storeNameAndMcc/list' });

  /**
   * 店铺认证
   */
  static preSubmit = (data: CommonParams) => post({ url: 'app/merchReport/preSubmit', data });

  /**
   * 店铺认证地址查询
   */
  static addressDetail = () => post({ url: '/app/merchReport/addressDetail', custom: { loading: false } });
}
