import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 商户提现API
 */
export class DrawcashApi {
  /**
   * 获取提现余额信息
   */
  static queryWithdrawBalance = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantQrcodeTransInfo/queryWithdrawBalance', data });

  /**
   * 通道商户钱包余额查询
   */
  static chlMerchQueryWalletAmt = (data: CommonParams) => post({ url: '/app/report/chlMerchQueryWalletAmt', data });
}
