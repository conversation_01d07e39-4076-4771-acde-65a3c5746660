import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 绑卡提额API
 */
export class IncreaseQuotaApi {
  /**
   * 列表
   */
  static queryList = () => post({ url: '/app/messageAnnounce/queryList' });

  /**
   * 绑卡提额
   */
  static bindCardUpLimit = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantBankCardUpCert/bindCardUpLimit', data });
}
