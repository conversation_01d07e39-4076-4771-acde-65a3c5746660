import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 商户API
 */
export class MerchApi {
  /**
   * 入网状态查询
   */
  static queryStatus = () => post<CommonResult>({ url: '/app/merch/queryStatus', custom: {
    loading: false,
  } });

  /**
   * 商户信息查询
   */
  static queryMerchInfo = () => post<CommonResult>({ url: '/app/merch/query', custom: {
    loading: false,
  } });

  /**
   * 商户限额/累计信息查询
   */
  static cumulateLimitDetail = () => post<CommonResult>({ url: '/app/merch/cumulateLimitDetail', custom: {
    loading: false,
  } });

  /**
   * 法人证件更新
   */
  static updateLegalCredentials = (data: CommonParams) => post<CommonResult>({ url: '/app/legalCredentials/update', data });
}
