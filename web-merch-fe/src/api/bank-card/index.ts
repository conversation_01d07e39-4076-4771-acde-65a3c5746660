import type { CommonParams, CommonResult } from '../common/types';
import type { BankAccountInfo } from './types';
import { post } from '@/utils/request';

/**
 * @description 结算卡API
 */
export class BankCardApi {
  /**
   * 添加结算卡
   */
  static addBankCard = (data: BankAccountInfo) => post<CommonResult>({ url: '/app/bankCard/add', data });

  /**
   * 编辑结算卡
   */
  static editBankCard = (data: BankAccountInfo) => post<CommonResult>({ url: '/app/bankCard/edit', data });

  /**
   * 删除结算卡
   */
  static deleteBankCard = (data: CommonParams) => post<CommonResult>({ url: '/app/bankCard/delete', data });

  /**
   * 结算卡列表
   */
  static queryBankCard = (data: CommonParams) => post<Record<string, any>[]>({ url: '/app/bankCard/query', data });
}
