import type { CommonResult } from '../common/types';

export interface ImageInfo {
  fileType: number; // 文件类型 (3:银行卡卡号面, 4:银行卡背面, 16:开户许可证)
  suffixType: 'jpg' | 'png' | 'gif'; // 文件后缀类型
  fileData: string; // Base64格式的文件数据
}

export interface BankAccountInfo extends CommonResult {
  id?: number; // 主键ID
  mobile?: string; // 预留手机号
  bankBranch?: string; // 开户支行名称
  bankChannelNo?: string; // 联行号
  province?: string; // 开户行省编码
  city?: string; // 开户行市编码
  cardType?: 1 | 2 | 3 | 4; // 卡类型 (1:借记卡, 2:贷记卡, 3:准贷记卡, 4:预付费卡)
  bankName?: string; // 银行名称
  typeCode?: string; // 银行行别代码
  accountType?: 'S' | 'G'; // 账户类型 (S: 对私, G: 对公)
  imageList?: ImageInfo[]; // 图片信息数组
}
