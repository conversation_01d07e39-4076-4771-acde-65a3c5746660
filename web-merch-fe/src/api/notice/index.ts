import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 通知API
 */
export class NoticeApi {
  /**
   * 公告消息列表
   */
  static queryList = () => post({ url: '/app/messageAnnounce/queryList' });

  /**
   * 公告消息详情
   */
  static queryDetail = (data: CommonParams) => post<CommonResult>({ url: '/app/messageAnnounce/queryDetail', data });
}
