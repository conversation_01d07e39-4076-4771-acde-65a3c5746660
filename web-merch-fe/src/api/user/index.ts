import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 用户API
 */
export class UserApi {
  /**
   * 注册
   */
  static register = (data: CommonParams) => post<CommonResult>({ url: '/app/user/register', data, custom: {
    auth: false,
  } });

  /**
   * 登录
   */
  static login = (data: CommonParams) => post<CommonResult>({ url: '/app/user/login', data, custom: {
    auth: false,
  } });

  /**
   * 找回登录密码
   */
  static resetPassword = (data: CommonParams) => post<CommonResult>({ url: '/app/user/resetPassword', data, custom: {
    auth: false,
  } });

  /**
   * 修改登录密码
   */
  static updatePassword = (data: CommonParams) => post<CommonResult>({ url: '/app/user/updatePassword', data });

  /**
   * 更换手机号
   */
  static changeTelephone = (data: CommonParams) => post<CommonResult>({ url: '/app/user/changeTelephone', data });

  /**
   * 退出登录
   */
  static loginout = () => post<CommonResult>({ url: '/app/user/loginout', custom: {
    loading: false,
  } });

  /**
   * 注销状态查询
   */
  static merchantCancelStatus = () => post({ url: '/app/merchantCancelStatus' });

  /**
   * 商户注销申请
   */
  static merchantCancelApply = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantCancelApply', data });
}
