import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 交易API
 */
export class TransApi {
  /**
   * 首页查询今日/当月交易总额、笔数
   */
  static totalAmount = (data: CommonParams) => post<CommonResult>({ url: '/merchantQrcodeTransInfo/totalAmount', data, custom: {
    loading: false,
  } });

  /**
   * 首页查询记账余额
   */
  static accountBalance = (data: CommonParams) => post<CommonResult>({ url: '/merchantQrcodeTransInfo/accountBalance', data, custom: {
    loading: false,
  } });

  /**
   * 交易列表（查询）
   */
  static queryList = (data: CommonParams) => post<CommonResult>({ url: '/merchantQrcodeTransInfo/queryList', data, custom: {
    loading: false,
  } });

  /**
   * 交易详情（查询）
   */
  static transDetailInfo = (data: CommonParams) => post<CommonResult>({ url: '/merchantQrcodeTransInfo/transDetailInfo', data });

  /**
   * 交易记录页面交易总额、笔数、实际到账总额（查询）
   */
  static selectTotalAmount = (data: CommonParams) => post<CommonResult>({ url: '/merchantQrcodeTransInfo/selectTotalAmount', data, custom: {
    loading: false,
  } });

  /**
   * 商户-近6月统计汇总列表、某月-每日统计汇总列表
   */
  static yearMonthCountList = (data: CommonParams) => post<CommonResult>({ url: '/merchantQrcodeTransInfo/yearMonthCountList', data, custom: {
    loading: false,
  } });

  /**
   * 退汇通知列表
   */
  static queryReexchangeList = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantQrcodeTransInfo/queryReexchangeList', data });

  /**
   * 查询待意愿核身确认的权益订单列表
   */
  static queryNeedInentityVertOrderList = () => post({ url: '/app/payOrder/queryNeedInentityVertOrderList', custom: {
    loading: false,
  } });

  /**
   * 获取意愿核身H5链接
   */
  static merchantCheckMerSelf = (data: CommonParams) => post<CommonResult>({ url: '/app/payOrder/merchantCheckMerSelf', data });
}
