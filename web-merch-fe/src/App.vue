<script setup lang="ts">
import { mpUpdate } from '@/utils/index';
import { AutoCleanup } from '@/utils/transfer';
import { useUserStore } from '@/store';
import { getToken } from '@/utils/auth';

onLaunch(() => {
  console.log('App Launch');

  // 启动数据传递自动清理（每5分钟清理一次过期数据）
  AutoCleanup.start(5 * 60 * 1000);

  // 如果用户已登录，初始化tabBar
  if (getToken()) {
    const userStore = useUserStore();
    // 延迟执行，确保tabBar已经初始化
    setTimeout(() => {
      userStore.updateTabBar();
    }, 100);
  }

  // #ifdef MP-WEIXIN
  mpUpdate();
  // #endif
});
onShow(() => {
  console.log('App Show');
});
onHide(() => {
  console.log('App Hide');
});
</script>

<style lang="scss">
/* 每个页面公共css */
@import '@/static/styles/common.scss';
</style>
