<template>
  <view><slot /></view>
</template>

<script>
import { children, parent } from '../common/relation';

export default {
  name: 'LimePainterView',
  mixins: [children('painter'), parent('painter')],
  options: {
    styleIsolation: 'shared',
  },
  props: {
    id: String,
    type: {
      type: String,
      default: 'view',
    },
    css: [String, Object],
  },
  data() {
    return {
      // type: 'view',
      el: {
        css: {},
        views: [],
      },
    };
  },
  mounted() {

  },
};
</script>

<style>
</style>
